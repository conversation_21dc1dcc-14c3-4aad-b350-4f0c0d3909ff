# iOS HTTP服务器访问问题修复方案归档

## 📋 问题背景

**项目**: FitMaster iOS应用  
**服务器**: http://**************:8000  
**问题**: iOS端无法访问HTTP服务器，出现网络连接错误  
**修复日期**: 2024年  
**状态**: ✅ 已解决

## 🚨 问题症状

### 初始错误日志
```
⚡️ [error] - Failed to load exercises: {"type":"server_error","message":"未知错误，请联系技术支持","details":{}}

Task finished with error [-1003] Error Domain=NSURLErrorDomain Code=-1003 
"A server with the specified hostname could not be found."
NSErrorFailingURLStringKey=https://api.fitmaster.com/api/v1/exercise/exercises
```

### 后期错误日志（配置修复后）
```
Task finished with error [-1022] Error Domain=NSURLErrorDomain Code=-1022 
"The resource could not be loaded because the App Transport Security policy requires the use of a secure connection."
NSErrorFailingURLStringKey=http://**************:8000/api/v1/exercise/exercises
```

## 🔍 根因分析

### 主要问题
1. **环境配置错误**: iOS构建时使用生产环境配置，访问错误的HTTPS服务器
2. **App Transport Security阻止**: iOS默认阻止HTTP连接，需要明确配置允许
3. **配置系统缺陷**: 环境变量在iOS构建中未正确加载

### 技术根因
- iOS App Transport Security (ATS) 默认只允许HTTPS连接
- Vite构建系统在生产模式下环境变量处理不当
- Capacitor同步过程中配置文件被覆盖

## ✅ 最终解决方案

### 1. iOS App Transport Security配置
**文件**: `ios/App/App/Info.plist`

```xml
<!-- App Transport Security配置 - 允许HTTP连接 -->
<key>NSAppTransportSecurity</key>
<dict>
    <!-- 允许任意加载 - 仅用于开发环境 -->
    <key>NSAllowsArbitraryLoads</key>
    <true/>
    
    <!-- 针对特定域名的配置（更安全的选择） -->
    <key>NSExceptionDomains</key>
    <dict>
        <key>**************</key>
        <dict>
            <key>NSExceptionAllowsInsecureHTTPLoads</key>
            <true/>
            <key>NSExceptionMinimumTLSVersion</key>
            <string>TLSv1.0</string>
            <key>NSIncludesSubdomains</key>
            <false/>
        </dict>
    </dict>
</dict>
```

### 2. API配置系统重构
**文件**: `src/config/api.config.ts`

```typescript
// 强制开发环境配置
export const getCurrentEnvironment = (): Environment => {
  console.log('🔧 强制使用开发环境配置 (HTTP服务器)');
  return 'development';
};

// 硬编码HTTP服务器地址
const configs: Record<Environment, ApiConfig> = {
  development: {
    baseURL: 'http://**************:8000/api/v1',
    imageBaseURL: 'http://**************:8000/exercises/images',
    timeout: 15000,
    retries: 3,
    retryDelay: 1000,
    enableLogging: true
  },
  // ... 其他环境配置
};
```

### 3. Capacitor网络配置
**文件**: `capacitor.config.ts`

```typescript
const config: CapacitorConfig = {
  appId: 'com.fitmaster.app',
  appName: 'FitMaster',
  webDir: 'dist',
  server: {
    androidScheme: 'https',
    allowNavigation: [
      '**************:8000',
      'localhost:*'
    ]
  },
  plugins: {
    CapacitorHttp: {
      enabled: true
    },
    StatusBar: {
      overlaysWebView: false
    }
    // ... 其他插件配置
  }
};
```

### 4. TypeScript类型声明
**文件**: `src/types/vite-env.d.ts`

```typescript
interface ImportMetaEnv {
  readonly VITE_APP_ENV: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_API_IMAGE_BASE_URL: string
  readonly DEV: boolean
  readonly PROD: boolean
  readonly MODE: string
  readonly BASE_URL: string
  readonly SSR: boolean
}
```

## 🛠️ 实施步骤

### 步骤1: 修改iOS配置
1. 编辑 `ios/App/App/Info.plist`
2. 添加 `NSAppTransportSecurity` 配置
3. 设置 `NSAllowsArbitraryLoads = true`

### 步骤2: 重构API配置系统
1. 创建 `src/config/api.config.ts`
2. 硬编码HTTP服务器地址
3. 强制使用开发环境配置

### 步骤3: 更新服务层
1. 修改 `src/services/api.ts`
2. 使用新的配置系统
3. 添加调试日志

### 步骤4: 构建和部署
```bash
# 构建应用
npm run build

# 同步iOS配置
npx cap sync ios

# 清理构建缓存（重要！）
rm -rf ios/App/build

# 在Xcode中完全重新构建
# Product → Clean Build Folder (⌘+Shift+K)
# Product → Build (⌘+B)
```

## 🔧 调试工具

### 配置调试组件（已移除）
创建了 `ConfigDebug` 组件用于实时查看配置状态：
- 显示当前环境和平台信息
- 显示实际使用的API地址
- 显示环境变量状态
- 提供网络连接状态指示

### 网络诊断Hook
**文件**: `src/hooks/useNetworkDiagnostics.ts`
- 检测网络连接状态
- 验证服务器可达性
- 检查HTTP协议支持
- 提供修复建议

## 🚨 关键注意事项

### 1. 构建缓存问题
**问题**: ATS配置修改后需要完全重新构建才能生效
**解决**: 必须清理构建缓存并重新构建

### 2. Capacitor同步覆盖
**问题**: `npx cap sync ios` 可能覆盖手动修改的配置
**解决**: 每次同步后检查 `Info.plist` 配置是否保留

### 3. 环境变量失效
**问题**: iOS构建中环境变量未正确加载
**解决**: 使用硬编码配置而非依赖环境变量

### 4. 生产环境安全
**警告**: 当前配置允许所有HTTP连接（`NSAllowsArbitraryLoads=true`）
**建议**: 生产环境应使用HTTPS并移除此配置

## 📊 验证结果

### 修复前
- ❌ 访问错误的HTTPS服务器
- ❌ 网络连接错误 (-1003)  
- ❌ App Transport Security阻止 (-1022)
- ❌ 无法加载数据和图片

### 修复后
- ✅ 正确访问HTTP服务器 `**************:8000`
- ✅ 网络连接成功
- ✅ ATS配置生效，允许HTTP连接
- ✅ 数据和图片正常加载

## 🎯 最佳实践建议

### 开发环境
1. 使用明确的配置管理系统
2. 添加充分的调试日志
3. 创建配置验证工具
4. 保持环境配置的一致性

### 生产环境准备
1. **升级服务器为HTTPS**: 获取SSL证书，配置反向代理
2. **移除ATS豁免**: 删除 `NSAllowsArbitraryLoads` 配置  
3. **环境变量管理**: 使用正确的生产环境配置
4. **安全审计**: 确保没有不安全的网络配置

### Capacitor项目配置
1. 定期验证 `Info.plist` 配置不被覆盖
2. 使用版本控制追踪配置变更
3. 建立标准的构建和部署流程
4. 实施自动化测试验证网络连接

## 📝 相关文件清单

### 核心配置文件
- `ios/App/App/Info.plist` - iOS App Transport Security配置
- `capacitor.config.ts` - Capacitor网络配置
- `src/config/api.config.ts` - API配置系统

### 工具文件（可选）
- `src/hooks/useNetworkDiagnostics.ts` - 网络诊断Hook
- `src/components/common/NetworkDiagnostics/` - 网络诊断UI组件
- `src/components/common/ConfigDebug/` - 配置调试组件

### 文档文件
- `iOS-Network-Setup-Guide.md` - 详细设置指导
- `iOS-HTTP-Server-Fix-Archive.md` - 本归档文档

## 🔄 故障排除清单

如果问题重现，按顺序检查：

1. **验证Info.plist配置**
   ```bash
   grep -A 5 "NSAppTransportSecurity" ios/App/App/Info.plist
   ```

2. **检查API配置**
   - 确认 `getCurrentEnvironment()` 返回 'development'
   - 确认 `baseURL` 为 HTTP 地址

3. **清理并重新构建**
   ```bash
   rm -rf ios/App/build
   npm run build
   npx cap sync ios
   ```

4. **在Xcode中清理构建**
   - Product → Clean Build Folder (⌘+Shift+K)
   - 完全重新构建应用

## 📞 技术支持

如遇问题，请提供：
1. Xcode控制台完整日志
2. `Info.plist` 文件内容
3. API配置状态（如有调试组件）
4. iOS设备型号和版本

---

**修复完成日期**: 2024年  
**修复状态**: ✅ 已验证成功  
**后续计划**: 准备生产环境HTTPS升级方案 