# FitMaster - 现代健身管理平台

基于 React 18 + TypeScript 构建的现代化健身管理应用。

## 🎯 项目概述

FitMaster 是一个现代化的健身管理平台，采用最新的前端技术栈构建，提供完整的健身追踪、数据可视化和个性化训练指导功能。

## ✨ 项目特性

### 🎨 设计系统
- **完整的 CSS 变量系统** - 基于 CSS 自定义属性的设计令牌
- **深蓝主色调** - 专业的健身应用配色方案
- **Inter 字体系统** - 现代、清晰的字体排版
- **8px 网格间距** - 一致的空间节奏
- **微妙阴影效果** - 增强界面层次感

### 🏗️ 技术架构
- **React 18** - 最新的 React 版本
- **TypeScript** - 类型安全的开发体验
- **Vite** - 快速的构建工具
- **SCSS** - 强大的 CSS 预处理器
- **React Router** - 客户端路由管理

### 📱 响应式设计
- **桌面优先** - 针对桌面端优化的布局
- **平板适配** - 完美的平板设备体验
- **移动端支持** - 响应式的移动端界面

### ♿ 可访问性
- **WCAG 2.1 AA 标准** - 符合无障碍访问标准
- **键盘导航** - 完整的键盘操作支持
- **屏幕阅读器** - 语义化的 HTML 结构
- **高对比度模式** - 支持高对比度显示
- **减少动画** - 尊重用户的动画偏好

## 🚀 快速开始

### 环境要求
- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本
```bash
npm run build
# 或
yarn build
```

### 预览生产版本
```bash
npm run preview
# 或
yarn preview
```

## 📁 项目结构

```
hevy-fitness-app/
├── public/                 # 静态资源
│   └── index.html         # HTML 模板
├── src/                   # 源代码
│   ├── components/        # React 组件
│   │   ├── common/        # 通用组件
│   │   │   ├── Layout.tsx # 主布局组件
│   │   │   └── Layout.scss
│   │   └── navigation/    # 导航组件
│   │       ├── Sidebar.tsx # 侧边栏组件
│   │       └── Sidebar.scss
│   ├── pages/            # 页面组件
│   │   ├── user-profile/ # 个人资料页面
│   │   │   ├── ProfilePage.tsx
│   │   │   └── ProfilePage.scss
│   │   └── HomePage.tsx  # 首页
│   ├── styles/           # 样式文件
│   │   ├── design-system.css # 设计系统变量
│   │   ├── variables.scss    # SCSS 变量
│   │   └── global.scss       # 全局样式
│   ├── App.tsx           # 主应用组件
│   └── main.tsx          # 应用入口
├── memory-bank/          # 设计文档
│   ├── style-guide.md    # 样式指南
│   └── hevy-ui-analysis.md # UI 分析文档
├── package.json          # 项目配置
├── tsconfig.json         # TypeScript 配置
├── vite.config.ts        # Vite 配置
└── README.md            # 项目文档
```

## 🎨 设计系统

### 色彩系统
- **主色调**: 深蓝色系 (#0f172a, #1e293b, #334155)
- **强调色**: 蓝色 (#3b82f6)
- **成功色**: 绿色 (#22c55e)
- **警告色**: 橙色 (#f59e0b)
- **错误色**: 红色 (#ef4444)

### 字体系统
- **主字体**: Inter, -apple-system, BlinkMacSystemFont, sans-serif
- **等宽字体**: SF Mono, Monaco, Consolas, monospace
- **显示字体**: Inter (用于标题)

### 间距系统
基于 8px 网格系统：
- 4px, 8px, 12px, 16px, 20px, 24px, 32px, 48px, 64px

## 🧩 核心组件

### Layout 组件
主布局组件，包含：
- 固定侧边栏导航
- 页面头部区域
- 主内容区域
- 响应式适配

### Sidebar 组件
侧边栏导航，包含：
- 品牌标识
- 导航菜单
- 用户信息
- 移动端底部导航

### ProfilePage 组件
个人资料页面，包含：
- 用户头像和基本信息
- 健身数据统计
- 最近训练记录
- 成就展示

## 📊 已实现功能

- ✅ 完整的设计系统
- ✅ 侧边栏导航组件
- ✅ 主布局组件
- ✅ 个人资料页面
- ✅ 响应式设计
- ✅ 可访问性支持
- ✅ TypeScript 类型安全

## 🔮 后续计划

- 🔄 Feed 动态页面
- 🏋️ Routines 训练计划页面
- 💪 Exercises 动作库页面
- ⚙️ Settings 设置页面
- 📊 数据可视化组件
- 🎮 游戏化元素
- 🌐 国际化支持

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- 感谢 Hevy 团队提供的设计灵感
- 感谢开源社区的优秀工具和库
- 感谢所有贡献者的努力

---

**注意**: 这是一个 UI/UX 重构项目，专注于界面设计和用户体验的实现，不包含后端功能。 