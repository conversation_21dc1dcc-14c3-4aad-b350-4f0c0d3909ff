/**
 * 🎯 iOS状态栏与布局完整修复方案 - 最终版
 * 解决状态栏遮挡、主题同步、滚动区域三大问题
 */

/* 🍎 iOS设备检测和基础样式重置 */
@supports (-webkit-touch-callout: none) {
  @media (max-width: 768px) {
    
    /* 🔧 清理冲突的遮罩层，统一管理 */
    html::before,
    html::after {
      display: none !important; /* 清理其他遮罩 */
    }
    
    /* 📱 1. 完美的状态栏处理方案 */
    .ios-statusbar-integration {
      /* 状态栏区域遮罩 - 唯一的遮罩层 */
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      height: env(safe-area-inset-top, 44px) !important;
      z-index: 1200 !important; /* 最高优先级 */
      pointer-events: none !important;
      
      /* 🎨 智能主题响应背景 */
      background: var(--bg-primary, #ffffff) !important;
      
      /* 确保完全不透明 */
      opacity: 1 !important;
    }
    
    /* 🏗️ 2. Header区域重新设计 */
    .page-header {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 1100 !important; /* Header在状态栏遮罩下方 */
      
      /* 📐 尺寸计算：状态栏高度 + Header内容高度 */
      height: calc(env(safe-area-inset-top, 44px) + 70px) !important;
      padding-top: calc(env(safe-area-inset-top, 44px) + 16px) !important;
      padding-left: 16px !important;
      padding-right: 16px !important;
      padding-bottom: 16px !important;
      
      /* 🎨 主题响应背景 - 与状态栏一致 */
      background: var(--bg-primary, #ffffff) !important;
      border-bottom: 1px solid var(--border-color, #e2e8f0) !important;
      
      /* 🌟 毛玻璃效果 */
      backdrop-filter: blur(20px) !important;
      -webkit-backdrop-filter: blur(20px) !important;
      
      /* ⚡ 硬件加速 */
      will-change: transform !important;
      transform: translateZ(0) !important;
      
      /* 📱 Header内容布局保持不变 */
      .dashboard-header-content {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        width: 100% !important;
        height: 54px !important;
      }
      
      .dashboard-title {
        color: var(--text-primary, #1e293b) !important;
        font-size: 18px !important;
        font-weight: bold !important;
      }
      
      .dashboard-subtitle {
        color: var(--text-secondary, #64748b) !important;
        font-size: 14px !important;
      }
    }
    
    /* 🎯 3. 滚动区域完美分离 */
    .main-content {
      display: flex !important;
      flex-direction: column !important;
      height: 100vh !important;
      
      /* 为固定Header预留空间 */
      padding-top: calc(env(safe-area-inset-top, 44px) + 70px) !important;
      
      /* 为固定底部导航预留空间 */
      &.with-bottom-nav {
        padding-bottom: calc(70px + env(safe-area-inset-bottom, 0px)) !important;
      }
    }
    
    /* 📄 4. 页面内容区域 - 纯滚动区 */
    .page-content {
      flex: 1 !important;
      overflow-y: auto !important;
      -webkit-overflow-scrolling: touch !important;
      
      /* 重置所有padding，由main-content统一管理 */
      padding: 16px !important;
      
      /* 确保内容可以完全滚动 */
      min-height: max-content !important;
    }
    
    /* 🔻 5. 底部导航固定处理 */
    .bottom-navigation {
      position: fixed !important;
      bottom: 0 !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 1000 !important;
      
      height: calc(70px + env(safe-area-inset-bottom, 0px)) !important;
      padding-bottom: env(safe-area-inset-bottom, 0px) !important;
      
      background: var(--bg-surface, #1e293b) !important;
      border-top: 1px solid var(--border-color, #374151) !important;
      backdrop-filter: blur(10px) !important;
    }
  }
}

/* 🌞 浅色主题专用适配 */
@supports (-webkit-touch-callout: none) {
  @media (max-width: 768px) {
    html[data-theme="light"] {
      .ios-statusbar-integration {
        background: #ffffff !important;
      }
      
      .page-header {
        background: #ffffff !important;
        border-bottom-color: #e2e8f0 !important;
        
        .dashboard-title {
          color: #1e293b !important;
        }
        
        .dashboard-subtitle {
          color: #64748b !important;
        }
      }
      
      .bottom-navigation {
        background: #ffffff !important;
        border-top-color: #e2e8f0 !important;
      }
    }
  }
}

/* 🌙 暗色主题专用适配 */
@supports (-webkit-touch-callout: none) {
  @media (max-width: 768px) {
    html[data-theme="dark"] {
      .ios-statusbar-integration {
        background: #0f172a !important;
      }
      
      .page-header {
        background: #0f172a !important;
        border-bottom-color: #374151 !important;
        
        .dashboard-title {
          color: #f8fafc !important;
        }
        
        .dashboard-subtitle {
          color: #cbd5e1 !important;
        }
      }
      
      .bottom-navigation {
        background: #1e293b !important;
        border-top-color: #374151 !important;
      }
    }
  }
}

/* 🔍 调试模式 - 可视化各个区域 */
.debug-ios-layout {
  @supports (-webkit-touch-callout: none) {
    @media (max-width: 768px) {
      .ios-statusbar-integration {
        background: rgba(255, 0, 0, 0.5) !important;
        
        &::after {
          content: 'Status Bar Area' !important;
          position: absolute !important;
          top: 50% !important;
          left: 50% !important;
          transform: translate(-50%, -50%) !important;
          color: white !important;
          font-size: 10px !important;
          font-weight: bold !important;
          pointer-events: none !important;
        }
      }
      
      .page-header {
        background: rgba(0, 255, 0, 0.3) !important;
        border: 2px solid lime !important;
      }
      
      .page-content {
        background: rgba(0, 0, 255, 0.1) !important;
        border: 1px solid blue !important;
      }
      
      .bottom-navigation {
        background: rgba(255, 255, 0, 0.3) !important;
        border: 2px solid yellow !important;
      }
    }
  }
}

/* 📱 小屏幕设备优化 */
@supports (-webkit-touch-callout: none) {
  @media (max-width: 375px) {
    .page-header {
      padding-left: 12px !important;
      padding-right: 12px !important;
      
      .dashboard-title {
        font-size: 16px !important;
      }
      
      .dashboard-subtitle {
        font-size: 12px !important;
      }
    }
    
    .page-content {
      padding: 12px !important;
    }
  }
}

/* 🎮 初始化类 - 用于动态添加 */
.ios-layout-initialized {
  /* 标记已初始化，避免重复处理 */
} 