/**
 * 🎯 iOS布局统一修复方案 - 彻底解决header被状态栏遮挡问题
 * 
 * 问题分析：
 * 1. 多个CSS文件设置不同的z-index值，导致层级混乱
 * 2. ios-statusbar-integration的z-index高于header，导致遮挡
 * 3. Safe Area计算方式不一致，导致padding-top错误
 * 4. 大量!important导致样式优先级混乱
 * 
 * 解决方案：
 * 1. 统一z-index层级：状态栏遮罩(990) < header(1030) < 其他UI元素
 * 2. 统一Safe Area计算：使用env(safe-area-inset-top, 44px)
 * 3. 清理!important，建立清晰的样式优先级
 * 4. 确保主题切换时的一致性
 */

/* ===== iOS移动端布局优化 ===== */
@media (max-width: 768px) {
    
    /* ===== 基础视口设置 ===== */
    html, body {
      height: 100vh;
      height: -webkit-fill-available;
      overflow-x: hidden;
      margin: 0;
      padding: 0;
    }
    
    #root {
      height: 100vh;
      height: -webkit-fill-available;
      overflow: hidden;
    }
    
    /* ===== 布局容器 ===== */
    .layout.mobile-layout {
      height: 100vh;
      height: -webkit-fill-available;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      background: var(--bg-primary);
    }
    
    /* ===== 🔧 核心修复：iOS原生风格header布局 ===== */
    
    /* Header直接扩展到状态栏区域 - 无缝融合设计 */
    .page-header {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: var(--z-fixed); /* 1030 - 确保在最上层 */
      
      /* 🎯 iOS规范的Safe Area处理 */
      height: calc(env(safe-area-inset-top, 44px) + 56px) !important;
      min-height: calc(env(safe-area-inset-top, 44px) + 56px) !important;
      
      /* ✅ 关键修复：去掉额外间距，直接从Safe Area开始 */
      padding-top: env(safe-area-inset-top, 44px) !important;
      padding-left: 16px !important;
      padding-right: 16px !important;
      padding-bottom: 8px !important;
      
      /* 主题响应背景 */
      background: var(--bg-primary);
      border-bottom: 1px solid var(--border-color);
      
      /* iOS毛玻璃效果 */
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      
      /* 硬件加速 */
      will-change: transform;
      transform: translateZ(0);
      -webkit-transform: translateZ(0);
      
      /* Header内容布局 - 从Safe Area下方开始 */
      .dashboard-header-content {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        width: 100% !important;
        min-height: 48px !important;
        gap: 12px !important;
        position: relative !important;
        z-index: 1 !important;
        /* 📍 内容区域精确定位：从Safe Area边界开始 */
        padding-top: 8px !important;
      }
      
      .dashboard-date-info {
        flex: 1;
        min-width: 0;
        
        .dashboard-title {
          font-size: 16px !important;
          /* ✅ 修复标题间距：减少到1px */
          margin: 0 0 1px 0 !important;  
          color: var(--text-primary) !important;
          font-weight: bold !important;
          white-space: nowrap !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          line-height: 1.2 !important;
        }
        
        .dashboard-subtitle {
          font-size: 12px !important;
          margin: 0 !important;
          color: var(--text-secondary) !important;
          line-height: 1.2 !important;
        }
      }
      
      .header-actions {
        flex-shrink: 0;
      }
    }
    
    /* 主内容区域 - 适配新的header高度 */
    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      
      /* ✅ 为固定Header预留空间 - 使用新的高度计算 */
      padding-top: calc(env(safe-area-inset-top, 44px) + 56px);
      
      /* 为底部导航预留空间 */
      &.with-bottom-nav {
        padding-bottom: calc(70px + env(safe-area-inset-bottom, 0px));
      }
    }
    
    /* 4. 页面内容区域 - 纯滚动区域 */
    .page-content {
      flex: 1;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      
      padding: 16px;
      min-height: max-content;
      height: auto;
      position: relative;
    }
    
    /* 5. 底部导航 */
    .bottom-navigation {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: var(--z-sticky); /* 1020 - 低于header */
      
      height: calc(70px + env(safe-area-inset-bottom, 0px));
      padding-bottom: env(safe-area-inset-bottom, 0px);
      
      background: var(--bg-surface);
      border-top: 1px solid var(--border-color);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
      will-change: transform;
      transform: translateZ(0);
    }
    
    /* 6. 聊天按钮 */
    .dashboard-v2__chat-button {
      position: fixed;
      right: 16px;
      z-index: var(--z-popover); /* 1060 - 高于导航栏 */
      bottom: calc(70px + env(safe-area-inset-bottom, 0px) + 16px);
      
      background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
      color: white;
      border: none;
      border-radius: 50px;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);
      transition: all 0.3s ease;
      
      min-height: 44px; /* Apple HIG标准 */
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 32px rgba(79, 70, 229, 0.4);
      }
      
      &:active {
        transform: translateY(0);
      }
      
      .chat-icon {
        font-size: 16px;
      }
      
      .chat-text {
        white-space: nowrap;
      }
    }
  }

/* ===== 主题适配 ===== */
@media (max-width: 768px) {
    
    /* 浅色主题 */
    .theme-light {
      .page-header {
        background: #ffffff;
        border-bottom-color: #e2e8f0;
        
        .dashboard-title {
          color: #1e293b;
        }
        
        .dashboard-subtitle {
          color: #475569;
        }
      }
      
      .bottom-navigation {
        background: #f8fafc;
        border-top-color: #e2e8f0;
      }
    }
    
    /* 暗色主题 */
    .theme-dark {
      .page-header {
        background: #0f172a;
        border-bottom-color: #374151;
        
        .dashboard-title {
          color: #f8fafc;
        }
        
        .dashboard-subtitle {
          color: #cbd5e1;
        }
      }
      
      .bottom-navigation {
        background: #1e293b;
        border-top-color: #374151;
      }
    }
  }

/* ===== 响应式适配 ===== */

/* 横屏模式 - 适配新的布局方案 */
  @media (max-width: 768px) and (orientation: landscape) {
    .page-header {
      height: calc(env(safe-area-inset-top, 20px) + 44px);
      padding-top: env(safe-area-inset-top, 20px);
      padding-bottom: 6px;
      
      .dashboard-header-content {
        padding-top: 6px;
        min-height: 38px;
      }
    }
    
    .main-content {
      padding-top: calc(env(safe-area-inset-top, 20px) + 44px);
    }
    
    .bottom-navigation {
      height: calc(60px + env(safe-area-inset-bottom, 0px));
    }
    
    .main-content.with-bottom-nav {
      padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px));
    }
    
    .dashboard-v2__chat-button {
      bottom: calc(60px + env(safe-area-inset-bottom, 0px) + 12px);
      padding: 8px 16px;
    }
  }
  
  /* 超小屏幕 */
  @media (max-width: 480px) {
    .page-header {
      padding-left: 12px;
      padding-right: 12px;
      
      .dashboard-header-content {
        gap: 8px;
      }
      
      .dashboard-date-info .dashboard-title {
        font-size: 14px;
      }
      
      .dashboard-date-info .dashboard-subtitle {
        font-size: 11px;
      }
    }
    
    .page-content {
      padding: 12px;
    }
    
    .dashboard-v2__chat-button {
      right: 12px;
      bottom: calc(70px + env(safe-area-inset-bottom, 0px) + 12px);
      padding: 10px 16px;
      font-size: 13px;
      
      .chat-icon {
        font-size: 14px;
      }
    }
  }

/* ===== 辅助功能支持 ===== */
@media (max-width: 768px) {
    
    /* 减少动画模式 */
    @media (prefers-reduced-motion: reduce) {
      .page-header,
      .bottom-navigation,
      .dashboard-v2__chat-button {
        transition: none;
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
      }
    }
    
    /* 高对比度模式 */
    @media (prefers-contrast: high) {
      .page-header {
        border-bottom-width: 2px;
        background: var(--bg-primary);
      }
      
      .bottom-navigation {
        border-top-width: 2px;
      }
      
      .dashboard-v2__chat-button {
        border: 2px solid var(--accent-500);
      }
    }
    
    /* 触摸目标优化 */
    .page-header *,
    .bottom-navigation *,
    .dashboard-v2__chat-button {
      min-height: 44px; /* Apple HIG标准 */
      min-width: 44px;
    }
  }
/* ===== 调试模式 (开发环境) ===== */
.debug-ios-unified-layout {
  @media (max-width: 768px) {
      .page-header {
        background: rgba(0, 255, 0, 0.3);
        border: 2px solid lime;
        
        &::before {
          content: 'Status Bar + Header (z:1030)';
          position: absolute;
          top: 2px;
          left: 2px;
          color: black;
          font-size: 10px;
          font-weight: bold;
          background: rgba(255, 255, 255, 0.8);
          padding: 2px 4px;
          border-radius: 2px;
          z-index: 999;
        }
        
        /* 显示Safe Area边界 */
        &::after {
          content: 'Safe Area Boundary';
          position: absolute;
          top: env(safe-area-inset-top, 44px);
          left: 0;
          right: 0;
          height: 1px;
          background: red;
          font-size: 10px;
          color: red;
          text-align: center;
          line-height: 1px;
        }
      }
      
      .page-content {
        background: rgba(0, 0, 255, 0.1);
        border: 1px solid blue;
      }
      
      .bottom-navigation {
        background: rgba(255, 255, 0, 0.3);
        border: 2px solid yellow;
      }
      
      .dashboard-v2__chat-button {
        background: rgba(255, 0, 255, 0.7);
        border: 2px solid magenta;
      }
    }
  }
}

/* ===== 初始化标记 ===== */
.ios-layout-unified-initialized {
  /* 标记已使用统一布局系统，避免其他样式干扰 */
} 