/**
 * FitMaster 移动端和iOS优化样式
 * 专门针对移动设备和iOS Safari的优化
 */

/* iOS Safe Area 支持 */
:root {
  /* iOS安全区域变量 */
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-right: env(safe-area-inset-right);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);
  
  /* 底部导航栏高度 */
  --bottom-nav-height: 70px;
  --bottom-nav-safe-height: calc(70px + var(--safe-area-inset-bottom));
  
  /* Header高度 - 包含Safe Area */
  --header-height-mobile: 56px;
  --header-safe-height: calc(56px + var(--safe-area-inset-top));
}

/* 全局移动端优化 */
@media (max-width: 768px) {
  body {
    /* iOS Safari 地址栏适配 */
    height: 100vh;
    height: -webkit-fill-available;
    overflow-x: hidden;
  }

  #root {
    height: 100vh;
    height: -webkit-fill-available;
  }

  /* 🔥 关键修复：iOS Header吸顶效果 */
  .page-header {
    position: fixed !important; /* 改为fixed确保完全固定 */
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1100 !important; /* 提高层级，确保在所有内容之上 */
    
    /* iOS Safe Area适配 */
    padding-top: var(--safe-area-inset-top) !important;
    height: var(--header-safe-height) !important;
    min-height: var(--header-safe-height) !important;
    
    /* 🎨 智能主题适配背景 - 确保不穿透 */
    background: var(--bg-primary, #ffffff) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    
    /* 额外的遮罩层确保内容不穿透 */
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--bg-primary, #ffffff);
      opacity: 0.95;
      z-index: -1;
    }
    
    /* 边框和阴影 */
    border-bottom: 1px solid var(--border-color, #e2e8f0) !important;
    box-shadow: 0 1px 10px var(--shadow-color, rgba(0, 0, 0, 0.1)) !important;
    
    /* 硬件加速 */
    will-change: transform !important;
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
  }

  /* 为固定header调整main-content的顶部间距 */
  .main-content {
    padding-top: var(--header-safe-height) !important;
  }

  /* 页面内容区域不需要额外的顶部间距（因为main-content已经处理了） */
  .page-content {
    padding-top: 0 !important; /* 重置顶部间距 */
  }

  /* 确保底部导航栏固定在底部 */
  .bottom-navigation {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1000 !important;
    height: var(--bottom-nav-height) !important;
    padding-bottom: var(--safe-area-inset-bottom) !important;
    background: var(--bg-surface, #1e293b) !important;
    border-top: 1px solid var(--primary-500, rgba(71, 85, 105, 0.3)) !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
  }

  /* 为有底部导航栏的页面添加底部间距 */
  .main-content.with-bottom-nav {
    padding-bottom: var(--bottom-nav-safe-height) !important;
    min-height: calc(100vh - var(--header-safe-height) - var(--bottom-nav-safe-height)) !important;
  }

  /* 页面内容区域优化 */
  .page-content {
    padding-bottom: calc(var(--space-4, 1rem) + var(--safe-area-inset-bottom)) !important;
    min-height: calc(100vh - var(--header-safe-height) - var(--bottom-nav-safe-height) - var(--space-4, 1rem)) !important;
  }

  /* Layout容器优化 */
  .layout.mobile-layout {
    min-height: 100vh !important;
    min-height: -webkit-fill-available !important;
    display: flex !important;
    flex-direction: column !important;
  }

  /* 滚动区域优化 */
  .scrollable-content {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
    height: calc(100vh - var(--header-safe-height) - var(--bottom-nav-safe-height));
  }
}

/* 触摸优化 */
@media (max-width: 768px) {
  /* 最小触摸目标尺寸 - Apple HIG 推荐44px */
  .touch-target,
  .bottom-nav-item,
  .btn,
  .nav-item,
  button,
  [role="button"] {
    min-height: 44px !important;
    min-width: 44px !important;
    padding: var(--space-3, 0.75rem) !important;
  }

  /* 触摸反馈 */
  .touch-target:active,
  .bottom-nav-item:active,
  .btn:active,
  .nav-item:active,
  button:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }

  /* 防止双击缩放 */
  * {
    touch-action: manipulation !important;
  }

  /* 输入框优化 */
  input,
  textarea,
  select {
    font-size: 16px !important; /* 防止iOS缩放 */
    border-radius: 8px !important;
    padding: 12px !important;
  }
}

/* iOS特定优化 */
@supports (-webkit-touch-callout: none) {
  /* 禁用iOS长按菜单 */
  .no-callout {
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    user-select: none !important;
  }

  /* 🍎 iOS状态栏和header一体化处理 */
  @media (max-width: 768px) {
    /* 🎨 强制整个视口为浅色主题背景 - 确保状态栏融合 */
    html {
      background: #ffffff !important;
      background-color: #ffffff !important;
    }
    
    body {
      background: #ffffff !important;
      background-color: #ffffff !important;
    }
    
    /* 🔝 状态栏区域专用背景遮罩 */
    html::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      height: env(safe-area-inset-top, 44px);
      background: #ffffff !important;
      z-index: 9999;
      pointer-events: none;
    }
    
    .page-header {
      /* iOS特有的状态栏适配 */
      -webkit-app-region: no-drag !important;
      
      /* 确保在iOS Safari中正确显示 */
      position: -webkit-sticky !important;
      position: sticky !important;
      position: fixed !important; /* 优先级最高 */
      
      /* iOS特有的背景处理 - 增强毛玻璃效果 */
      -webkit-backdrop-filter: saturate(180%) blur(20px) !important;
      backdrop-filter: saturate(180%) blur(20px) !important;
      
      /* 确保状态栏区域完全覆盖 */
      margin-top: calc(-1 * var(--safe-area-inset-top)) !important;
      padding-top: calc(var(--safe-area-inset-top) + var(--safe-area-inset-top)) !important;
    }
    
    /* 为状态栏区域添加额外的背景遮罩 */
    .page-header::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: var(--safe-area-inset-top);
      background: var(--bg-primary, #ffffff);
      z-index: -2;
    }
  }

  /* iOS键盘弹起时的视口调整 */
  .keyboard-adjust {
    height: 100vh !important;
    height: -webkit-fill-available !important;
  }
}

/* 硬件加速优化 */
@media (max-width: 768px) {
  .bottom-navigation,
  .page-header,
  .floating-action-button,
  .modal,
  .drawer {
    will-change: transform !important;
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
  }

  /* 动画性能优化 */
  .animated {
    will-change: transform, opacity !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
  }
}

/* 主题模式适配 */
.theme-dark {
  @media (max-width: 768px) {
    /* 暗色主题下的状态栏区域背景 */
    html {
      background: var(--bg-primary, #0f172a) !important;
    }
    
    body {
      background: var(--bg-primary, #0f172a) !important;
    }
    
    .page-header {
      background: var(--bg-primary, #0f172a) !important;
      border-bottom-color: var(--border-color, #374151) !important;
      
      &::before {
        background: var(--bg-primary, #0f172a);
      }
      
      &::after {
        background: var(--bg-primary, #0f172a);
      }
    }
    
    .bottom-navigation {
      background: var(--bg-surface, #1e293b) !important;
      border-top-color: var(--border-color, #374151) !important;
    }
  }
}

/* 系统暗色模式适配 */
@media (prefers-color-scheme: dark) {
  @media (max-width: 768px) {
    /* 系统暗色模式下的状态栏区域背景 */
    html {
      background: var(--bg-primary, #0f172a) !important;
    }
    
    body {
      background: var(--bg-primary, #0f172a) !important;
    }
    
    .page-header {
      background: var(--bg-primary, #0f172a) !important;
      border-bottom-color: var(--border-color, #374151) !important;
      
      &::before {
        background: var(--bg-primary, #0f172a);
      }
      
      &::after {
        background: var(--bg-primary, #0f172a);
      }
    }
    
    .bottom-navigation {
      background: var(--bg-surface, #1e293b) !important;
      border-top-color: var(--border-color, #374151) !important;
    }
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  @media (max-width: 768px) {
    .page-header {
      border-bottom-width: 2px !important;
      background-color: var(--bg-primary) !important; /* 去除透明度 */
    }
    
    .bottom-navigation {
      border-top-width: 2px !important;
    }
    
    .bottom-nav-item {
      border: 1px solid transparent !important;
    }
    
    .bottom-nav-item:hover,
    .bottom-nav-item.active {
      border-color: currentColor !important;
    }
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  @media (max-width: 768px) {
    .page-header {
      backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important;
    }
    
    .bottom-nav-item,
    .touch-target {
      transition: none !important;
    }
    
    .touch-target:active,
    .bottom-nav-item:active {
      transform: none !important;
    }
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  :root {
    --header-height-mobile: 48px; /* 横屏时减少header高度 */
    --header-safe-height: calc(48px + var(--safe-area-inset-top));
    --bottom-nav-height: 60px;
    --bottom-nav-safe-height: calc(60px + var(--safe-area-inset-bottom));
  }
  
  .page-header {
    height: var(--header-safe-height) !important;
    min-height: var(--header-safe-height) !important;
  }
  
  .bottom-navigation {
    height: 60px !important; /* 横屏时减少高度 */
  }
  
  .bottom-nav-item {
    padding: var(--space-2, 0.5rem) !important;
  }
  
  .bottom-nav-label {
    font-size: 0.7rem !important;
  }
}

/* 超小屏幕优化 */
@media (max-width: 375px) {
  .page-header {
    padding-left: 12px !important;
    padding-right: 12px !important;
  }
  
  .bottom-nav-item {
    min-width: 50px !important;
    padding: var(--space-2, 0.5rem) !important;
  }
  
  .bottom-nav-label {
    font-size: 0.7rem !important;
  }
  
  .page-content {
    padding: var(--space-3, 0.75rem) !important;
  }
}

/* 特殊机型适配 */
/* iPhone X/11/12/13/14/15系列刘海屏适配 */
@supports (padding: max(0px)) {
  @media (max-width: 768px) {
    .page-header {
      padding-top: max(var(--safe-area-inset-top), 20px) !important;
      height: calc(56px + max(var(--safe-area-inset-top), 20px)) !important;
    }
    
    .main-content {
      padding-top: calc(56px + max(var(--safe-area-inset-top), 20px)) !important;
    }
  }
}

/* iPhone 14 Pro Max等Dynamic Island适配 */
@media (max-width: 768px) and (min-height: 900px) {
  .page-header {
    padding-top: max(var(--safe-area-inset-top), 24px) !important;
  }
}

/* 调试辅助 - 开发时可启用 */
.debug-mobile {
  .page-header {
    background: rgba(255, 0, 0, 0.5) !important;
    border: 2px solid red !important;
    
    &::before {
      background: rgba(255, 255, 0, 0.3) !important;
    }
    
    &::after {
      background: rgba(0, 255, 255, 0.3) !important;
    }
  }
  
  .bottom-navigation {
    background: rgba(255, 0, 0, 0.3) !important;
  }
  
  .main-content.with-bottom-nav {
    background: rgba(0, 255, 0, 0.3) !important;
  }
  
  .page-content {
    background: rgba(0, 0, 255, 0.3) !important;
  }
}

/* 🎯 iOS Header完美修复验证类 */
.ios-header-fixed {
  @media (max-width: 768px) {
    .page-header {
      /* 验证固定定位 */
      position: fixed !important;
      top: 0 !important;
      z-index: 1100 !important;
      
      /* 验证主题适配 */
      background: var(--bg-primary, #ffffff) !important;
      
      /* 验证遮罩效果 */
      &::before {
        opacity: 0.98 !important; /* 更强的遮罩 */
      }
    }
  }
} 