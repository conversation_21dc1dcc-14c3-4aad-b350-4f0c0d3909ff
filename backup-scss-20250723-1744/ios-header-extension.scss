/**
 * 🎨 iOS Header扩展到状态栏区域方案 - 增强版
 * 多重检测机制确保兼容性
 */

/* 🍎 主要iOS检测 - 现代CSS支持 */
@supports (padding: max(0px)) and (-webkit-appearance: none) {
  @media (max-width: 768px) {
    
    /* 🎯 Header扩展到状态栏区域 */
    .page-header {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 1100 !important;
      
      /* 扩展padding到状态栏区域 - 添加fallback */
      padding-top: 44px !important; /* fallback */
      padding-top: calc(env(safe-area-inset-top, 44px) + 16px) !important;
      padding-left: 16px !important;
      padding-right: 16px !important;
      padding-bottom: 16px !important;
      
      /* 确保header有足够的高度 */
      min-height: 108px !important; /* fallback */
      min-height: calc(env(safe-area-inset-top, 44px) + 64px) !important;
      
      background: var(--bg-primary) !important;
      border-bottom: 1px solid var(--border-color) !important;
      backdrop-filter: blur(20px) !important;
      -webkit-backdrop-filter: blur(20px) !important;
      
      /* 硬件加速 */
      will-change: transform !important;
      transform: translateZ(0) !important;
      
      .dashboard-header-content {
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
        justify-content: space-between !important;
        width: 100% !important;
        min-height: 48px !important;
        gap: 12px !important;
        position: relative !important;
        z-index: 1 !important;
      }
      
      .dashboard-date-info {
        flex: 1 !important;
        min-width: 0 !important;
        
        .dashboard-title {
          font-size: 16px !important;
          margin: 0 0 2px 0 !important;
          white-space: nowrap !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          color: var(--text-primary) !important;
        }
        
        .dashboard-subtitle {
          font-size: 12px !important;
          margin: 0 !important;
          color: var(--text-secondary) !important;
        }
      }
      
      .header-actions {
        flex-shrink: 0 !important;
      }
    }
    
    /* 🎯 内容区域适配 */
    .page-content {
      padding-top: 124px !important; /* fallback */
      padding-top: calc(env(safe-area-inset-top, 44px) + 64px + 16px) !important;
      padding-left: 16px !important;
      padding-right: 16px !important;
      padding-bottom: 16px !important;
      height: 100vh !important;
      overflow-y: auto !important;
      -webkit-overflow-scrolling: touch !important;
      position: relative !important;
      z-index: 1 !important;
    }
    
    /* 底部导航适配 */
    .main-content.with-bottom-nav .page-content {
      padding-bottom: calc(70px + env(safe-area-inset-bottom, 0px) + 16px) !important;
      height: calc(100vh - 70px - env(safe-area-inset-bottom, 0px)) !important;
    }
    
    .main-content {
      overflow: hidden !important;
    }
  }
}

/* 🍎 备用iOS检测 - webkit touch支持 */
@supports (-webkit-touch-callout: none) {
  @media (max-width: 768px) {
    .page-header:not([style*="position: fixed"]) {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 1100 !important;
      padding-top: 60px !important;
      padding-left: 16px !important;
      padding-right: 16px !important;
      padding-bottom: 16px !important;
      min-height: 108px !important;
      background: var(--bg-primary) !important;
      border-bottom: 1px solid var(--border-color) !important;
    }
    
    .page-content:not([style*="padding-top"]) {
      padding-top: 124px !important;
      padding-left: 16px !important;
      padding-right: 16px !important;
      padding-bottom: 16px !important;
      height: 100vh !important;
      overflow-y: auto !important;
      -webkit-overflow-scrolling: touch !important;
    }
  }
}

/* 🎨 浅色主题适配 */
@supports (padding: max(0px)) and (-webkit-appearance: none) {
  @media (max-width: 768px) {
    html[data-theme="light"] .page-header {
      background: #ffffff !important;
      color: #1e293b !important;
      border-bottom-color: #e2e8f0 !important;
      
      .dashboard-title {
        color: #1e293b !important;
      }
      
      .dashboard-subtitle {
        color: #475569 !important;
      }
    }
  }
}

/* 🎨 暗色主题适配 */
@supports (padding: max(0px)) and (-webkit-appearance: none) {
  @media (max-width: 768px) {
    html[data-theme="dark"] .page-header {
      background: #0f172a !important;
      color: #f8fafc !important;
      border-bottom-color: #374151 !important;
      
      .dashboard-title {
        color: #f8fafc !important;
      }
      
      .dashboard-subtitle {
        color: #cbd5e1 !important;
      }
    }
  }
}

/* 🔍 调试模式 */
@supports (padding: max(0px)) and (-webkit-appearance: none) {
  @media (max-width: 768px) {
    .debug-statusbar .page-header {
      background: rgba(0, 255, 0, 0.3) !important;
      border: 3px solid lime !important;
      
      &::before {
        content: 'Status Bar Area';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: env(safe-area-inset-top, 44px);
        background: rgba(255, 0, 0, 0.5) !important;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 10px;
        font-weight: bold;
        z-index: -1;
      }
    }
    
    .debug-statusbar .page-content {
      background: rgba(0, 0, 255, 0.1) !important;
      border: 2px solid blue !important;
    }
  }
}

/* 📱 小屏幕优化 */
@supports (padding: max(0px)) and (-webkit-appearance: none) {
  @media (max-width: 480px) {
    .page-header {
      padding-left: 12px !important;
      padding-right: 12px !important;
      
      .dashboard-header-content {
        gap: 8px !important;
      }
      
      .dashboard-date-info .dashboard-title {
        font-size: 14px !important;
      }
      
      .dashboard-date-info .dashboard-subtitle {
        font-size: 11px !important;
      }
    }
    
    .page-content {
      padding-left: 12px !important;
      padding-right: 12px !important;
    }
  }
} 