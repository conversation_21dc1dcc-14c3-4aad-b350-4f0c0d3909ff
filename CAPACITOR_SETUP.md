# FitMaster Capacitor iOS 部署指南

## 🚀 快速开始

### 前置要求
- macOS系统 (iOS开发必需)
- Xcode 14+ (App Store安装)
- Node.js 18+ 
- npm 或 yarn
- iOS模拟器或真机设备

### 第一步：安装依赖
```bash
cd hevy-fitness-app
npm install
```

### 第二步：构建Web应用
```bash
npm run build
```

### 第三步：初始化Capacitor (仅第一次)
```bash
# 初始化Capacitor项目
npm run capacitor:init

# 添加iOS平台
npm run capacitor:add:ios
```

### 第四步：同步代码到iOS
```bash
# 同步Web代码到iOS项目
npm run capacitor:sync
```

### 第五步：在iOS模拟器中运行
```bash
# 打开Xcode项目
npm run capacitor:open:ios
```

在Xcode中：
1. 选择目标设备 (iPhone 14, iPad等)
2. 点击 ▶️ 运行按钮
3. 等待应用在模拟器中启动

## 🔧 开发工作流

### 热重载开发 (推荐)
```bash
# 启动开发服务器并在iOS中实时预览
npm run dev:ios
```

### 标准开发流程
```bash
# 1. 修改Web代码后重新构建
npm run build

# 2. 同步到iOS
npm run capacitor:sync:ios

# 3. 在Xcode中运行
npm run capacitor:open:ios
```

## 📱 API集成配置

### 环境变量设置
创建 `.env` 文件：
```bash
# 复制环境变量模板
cp .env.example .env
```

编辑 `.env` 文件：
```env
VITE_API_BASE_URL=http://**************:8000/api/v1
VITE_APP_ENV=development
VITE_DEBUG_MODE=true
```

### API服务测试
```bash
# 测试API连接
npm run api:test
```

## 🔍 故障排除

### 常见问题

#### 1. Capacitor命令未找到
```bash
# 全局安装Capacitor CLI
npm install -g @capacitor/cli
```

#### 2. iOS平台同步失败
```bash
# 清理并重新同步
rm -rf ios/
npm run capacitor:add:ios
npm run build
npm run capacitor:sync:ios
```

#### 3. API请求失败
- 检查服务器 `**************` 是否可访问
- 确认防火墙设置允许HTTP请求
- 使用 `npm run api:test` 测试连接

#### 4. 应用无法启动
```bash
# 清理构建缓存
npm run build
rm -rf ios/App/public
npm run capacitor:sync
```

#### 5. iOS模拟器问题
- 重启Xcode
- 重置iOS模拟器：Device → Erase All Content and Settings
- 更新Xcode到最新版本

### 调试技巧

#### 1. 查看应用日志
在Xcode中：`View → Debug Area → Activate Console`

#### 2. 网络请求调试
```typescript
// 在API服务中启用调试模式
localStorage.setItem('fitmaster_debug', 'true')
```

#### 3. 性能监控
```bash
# 启用性能监控
npm run dev -- --profile
```

## 📋 部署检查清单

### 开发环境检查
- [ ] Node.js版本 >= 18
- [ ] Xcode已安装并更新
- [ ] iOS模拟器可正常运行
- [ ] API服务器可访问

### 构建检查
- [ ] `npm run build` 成功执行
- [ ] `dist/` 目录已生成
- [ ] 无TypeScript编译错误
- [ ] 无ESLint警告

### iOS集成检查
- [ ] Capacitor配置正确
- [ ] iOS项目生成成功
- [ ] 应用图标设置完成
- [ ] 启动屏配置正确

### API集成检查
- [ ] 环境变量配置正确
- [ ] API连接测试通过
- [ ] 认证流程正常
- [ ] 离线缓存工作正常

## 🎯 性能优化建议

### 1. 构建优化
```bash
# 生产构建
NODE_ENV=production npm run build
```

### 2. 资源优化
- 启用图片压缩
- 使用WebP格式图片
- 启用Gzip压缩

### 3. iOS特定优化
- 启用硬件加速
- 优化触摸响应
- 适配Safe Area

### 4. 网络优化
- 实现请求缓存
- 启用离线模式
- 优化API调用频率

## 🔄 CI/CD配置 (可选)

### GitHub Actions工作流
创建 `.github/workflows/ios-build.yml`:
```yaml
name: iOS Build
on: [push, pull_request]
jobs:
  build:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm install
      - run: npm run build
      - run: npm run capacitor:sync
```

## 📚 进一步学习

### 官方文档
- [Capacitor官方文档](https://capacitorjs.com/docs)
- [Capacitor iOS指南](https://capacitorjs.com/docs/ios)
- [Vite构建工具](https://vitejs.dev/guide/)

### FitMaster特定资源
- `src/services/api.ts` - API服务层
- `src/hooks/useOfflineSync.ts` - 离线同步
- `src/styles/mobile-optimizations.scss` - 移动端优化
- `capacitor.config.ts` - Capacitor配置

---

**需要帮助？** 
- 检查控制台错误信息
- 参考上述故障排除指南
- 查看官方Capacitor文档 