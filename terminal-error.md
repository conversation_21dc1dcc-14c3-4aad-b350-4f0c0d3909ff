npm run build

> fitmaster@1.0.0 build
> tsc && vite build

src/components/fitness/ExerciseList/ExerciseList.tsx:77:12 - error TS2365: Operator '>' cannot be applied to types 'string | number' and 'number'.

77           {exercise.reps > 0 && (
              ~~~~~~~~~~~~~~~~~

src/components/fitness/ExerciseList/ExerciseList.tsx:164:77 - error TS2363: The right-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.

164             {exercises.reduce((total, ex) => total + (ex.sets * ex.weight * ex.reps), 0).toFixed(0)}
                                                                                ~~~~~~~

src/services/__tests__/authService.test.ts:9:12 - error TS2304: Cannot find name 'jest'.

9   getItem: jest.fn(),
             ~~~~

src/services/__tests__/authService.test.ts:10:12 - error TS2304: Cannot find name 'jest'.

10   setItem: jest.fn(),
              ~~~~

src/services/__tests__/authService.test.ts:11:15 - error TS2304: Cannot find name 'jest'.

11   removeItem: jest.fn(),
                 ~~~~

src/services/__tests__/authService.test.ts:12:10 - error TS2304: Cannot find name 'jest'.

12   clear: jest.fn()
            ~~~~

src/services/__tests__/authService.test.ts:20:16 - error TS2304: Cannot find name 'jest'.

20 global.fetch = jest.fn();
                  ~~~~

src/services/__tests__/authService.test.ts:22:1 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

22 describe('AuthService', () => {
   ~~~~~~~~

src/services/__tests__/authService.test.ts:25:3 - error TS2304: Cannot find name 'beforeEach'.

25   beforeEach(() => {
     ~~~~~~~~~~

src/services/__tests__/authService.test.ts:26:5 - error TS2304: Cannot find name 'jest'.

26     jest.clearAllMocks();
       ~~~~

src/services/__tests__/authService.test.ts:35:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

35   describe('loginWithTestUser', () => {
     ~~~~~~~~

src/services/__tests__/authService.test.ts:36:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

36     it('should login successfully in mock mode', async () => {
       ~~

src/services/__tests__/authService.test.ts:39:7 - error TS2304: Cannot find name 'expect'.

39       expect(user).toMatchObject({
         ~~~~~~

src/services/__tests__/authService.test.ts:46:7 - error TS2304: Cannot find name 'expect'.

46       expect(authService.isAuthenticated()).toBe(true);
         ~~~~~~

src/services/__tests__/authService.test.ts:47:7 - error TS2304: Cannot find name 'expect'.

47       expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(2);
         ~~~~~~

src/services/__tests__/authService.test.ts:50:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

50     it('should handle login failure and fallback to mock', async () => {
       ~~

src/services/__tests__/authService.test.ts:56:24 - error TS2503: Cannot find namespace 'jest'.

56       (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));
                          ~~~~

src/services/__tests__/authService.test.ts:60:7 - error TS2304: Cannot find name 'expect'.

60       expect(user).toMatchObject({
         ~~~~~~

src/services/__tests__/authService.test.ts:68:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

68   describe('getAuthHeaders', () => {
     ~~~~~~~~

src/services/__tests__/authService.test.ts:69:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

69     it('should return auth headers when authenticated', async () => {
       ~~

src/services/__tests__/authService.test.ts:74:7 - error TS2304: Cannot find name 'expect'.

74       expect(headers).toHaveProperty('Authorization');
         ~~~~~~

src/services/__tests__/authService.test.ts:75:7 - error TS2304: Cannot find name 'expect'.

75       expect(headers.Authorization).toMatch(/^Bearer /);
         ~~~~~~

src/services/__tests__/authService.test.ts:76:7 - error TS2304: Cannot find name 'expect'.

76       expect(headers).toHaveProperty('X-User-ID', '1');
         ~~~~~~

src/services/__tests__/authService.test.ts:77:7 - error TS2304: Cannot find name 'expect'.

77       expect(headers).toHaveProperty('X-User-OpenID', 'oCU0j7Rg9kzigLzquCBje3KfnQXk');
         ~~~~~~

src/services/__tests__/authService.test.ts:80:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

80     it('should throw error when not authenticated', () => {
       ~~

src/services/__tests__/authService.test.ts:81:7 - error TS2304: Cannot find name 'expect'.

81       expect(() => authService.getAuthHeaders()).toThrow('用户未登录');
         ~~~~~~

src/services/__tests__/authService.test.ts:85:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

85   describe('isTokenValid', () => {
     ~~~~~~~~

src/services/__tests__/authService.test.ts:86:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

86     it('should return true for valid token', async () => {
       ~~

src/services/__tests__/authService.test.ts:89:7 - error TS2304: Cannot find name 'expect'.

89       expect(authService.isTokenValid()).toBe(true);
         ~~~~~~

src/services/__tests__/authService.test.ts:92:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

92     it('should return false for expired token', async () => {
       ~~

src/services/__tests__/authService.test.ts:98:7 - error TS2304: Cannot find name 'expect'.

98       expect(authService.isTokenValid()).toBe(false);
         ~~~~~~

src/services/__tests__/authService.test.ts:102:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

102   describe('logout', () => {
      ~~~~~~~~

src/services/__tests__/authService.test.ts:103:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

103     it('should clear auth data on logout', async () => {
        ~~

src/services/__tests__/authService.test.ts:106:7 - error TS2304: Cannot find name 'expect'.

106       expect(authService.isAuthenticated()).toBe(true);
          ~~~~~~

src/services/__tests__/authService.test.ts:110:7 - error TS2304: Cannot find name 'expect'.

110       expect(authService.isAuthenticated()).toBe(false);
          ~~~~~~

src/services/__tests__/authService.test.ts:111:7 - error TS2304: Cannot find name 'expect'.

111       expect(authService.getCurrentUser()).toBe(null);
          ~~~~~~

src/services/__tests__/authService.test.ts:112:7 - error TS2304: Cannot find name 'expect'.

112       expect(mockLocalStorage.removeItem).toHaveBeenCalledTimes(2);
          ~~~~~~

src/services/__tests__/authService.test.ts:116:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

116   describe('refreshTokenIfNeeded', () => {
      ~~~~~~~~

src/services/__tests__/authService.test.ts:117:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

117     it('should not refresh valid token', async () => {
        ~~

src/services/__tests__/authService.test.ts:124:7 - error TS2304: Cannot find name 'expect'.

124       expect(authService.getAuthHeaders().Authorization).toBe(originalToken);
          ~~~~~~

src/services/__tests__/authService.test.ts:127:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

127     it('should refresh expired token', async () => {
        ~~

src/services/__tests__/authService.test.ts:136:7 - error TS2304: Cannot find name 'expect'.

136       expect(authService.isAuthenticated()).toBe(true);
          ~~~~~~

src/services/__tests__/authService.test.ts:140:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

140   describe('storage operations', () => {
      ~~~~~~~~

src/services/__tests__/authService.test.ts:141:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

141     it('should load stored auth on initialization', () => {
        ~~

src/services/__tests__/authService.test.ts:160:7 - error TS2304: Cannot find name 'expect'.

160       expect(newAuthService.isAuthenticated()).toBe(true);
          ~~~~~~

src/services/__tests__/authService.test.ts:161:7 - error TS2304: Cannot find name 'expect'.

161       expect(newAuthService.getCurrentUser()?.nickName).toBe('存储用户');
          ~~~~~~

src/services/__tests__/authService.test.ts:164:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

164     it('should handle corrupted storage data', () => {
        ~~

src/services/__tests__/authService.test.ts:167:7 - error TS2304: Cannot find name 'expect'.

167       expect(() => new AuthService({ mockMode: true })).not.toThrow();
          ~~~~~~

src/services/authService.ts:296:7 - error TS2353: Object literal may only specify known properties, and 'timeout' does not exist in type 'RequestInit'.

296       timeout: this.config.timeout
          ~~~~~~~

src/services/communityService.ts:82:7 - error TS6133: 'del' is declared but its value is never read.

82 const del = <T>(url: string): Promise<T> => {
         ~~~

src/services/communityService.ts:180:42 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

180       return await get<FeedPost>(`${this.BASE_URL}/posts/${postId}`);
                                             ~~~~~~~~

src/services/communityService.ts:191:35 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

191     return post<FeedPost>(`${this.BASE_URL}/posts/`, postData);
                                      ~~~~~~~~

src/services/communityService.ts:198:34 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

198     return put<FeedPost>(`${this.BASE_URL}/posts/${postId}`, postData);
                                     ~~~~~~~~

src/services/communityService.ts:205:66 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

205     return post<{ liked: boolean; likes_count: number }>(`${this.BASE_URL}/posts/${postId}/like/`);
                                                                     ~~~~~~~~

src/services/communityService.ts:212:47 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

212     return post<{ success: boolean }>(`${this.BASE_URL}/posts/${postId}/report/`, { reason });
                                                  ~~~~~~~~

src/services/communityService.ts:226:15 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

226       `${this.BASE_URL}/posts/${postId}/comments/`,
                  ~~~~~~~~

src/services/communityService.ts:236:46 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

236       return await post<FeedComment>(`${this.BASE_URL}/posts/${postId}/comments/`, { content });
                                                 ~~~~~~~~

src/services/communityService.ts:248:46 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

248       return await post<FeedComment>(`${this.BASE_URL}/comments/${commentId}/replies/`, { content });
                                                 ~~~~~~~~

src/services/communityService.ts:261:17 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

261         `${this.BASE_URL}/comments/${commentId}/like/`
                    ~~~~~~~~

src/services/communityService.ts:279:15 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

279       `${this.BASE_URL}/notifications/`,
                  ~~~~~~~~

src/services/communityService.ts:288:46 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

288     return put<{ success: boolean }>(`${this.BASE_URL}/notifications/${notificationId}/read/`);
                                                 ~~~~~~~~

src/services/communityService.ts:295:46 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

295     return put<{ success: boolean }>(`${this.BASE_URL}/notifications/read-all/`);
                                                 ~~~~~~~~

src/services/communityService.ts:304:29 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

304     return get<any>(`${this.BASE_URL}/users/${userId}`);
                                ~~~~~~~~

src/services/communityService.ts:311:35 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

311     return get<UserStats>(`${this.BASE_URL}/users/${userId}/stats`);
                                      ~~~~~~~~

src/services/communityService.ts:320:17 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

320         `${this.BASE_URL}/users/${userId}/follow/`
                    ~~~~~~~~

src/services/communityService.ts:334:46 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

334       return await get<FollowStatus>(`${this.BASE_URL}/users/${userId}/follow-status/`);
                                                 ~~~~~~~~

src/services/communityService.ts:347:37 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

347     return get<WorkoutData>(`${this.BASE_URL}/daily-workouts/${workoutId}`);
                                        ~~~~~~~~

src/services/communityService.ts:354:38 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

354     return post<WorkoutData>(`${this.BASE_URL}/daily-workouts/`, workoutData);
                                         ~~~~~~~~

src/services/communityService.ts:361:35 - error TS2339: Property 'BASE_URL' does not exist on type 'typeof CommunityService'.

361     return post<FeedPost>(`${this.BASE_URL}/workout/${workoutId}/share`, shareData);
                                      ~~~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:34:1 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

34 describe('muscleColorCalculator', () => {
   ~~~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:35:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

35   describe('calculateExerciseVolume', () => {
     ~~~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:36:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

36     it('should calculate exercise volume correctly', () => {
       ~~

src/utils/__tests__/muscleColorCalculator.test.ts:39:7 - error TS2304: Cannot find name 'expect'.

39       expect(volume).toBe(4 * 8 * 80); // sets * reps * weight = 2560
         ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:43:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

43   describe('calculateMuscleIntensities', () => {
     ~~~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:44:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

44     it('should calculate muscle intensities correctly', () => {
       ~~

src/utils/__tests__/muscleColorCalculator.test.ts:48:7 - error TS2304: Cannot find name 'expect'.

48       expect(intensities.length).toBeGreaterThan(0);
         ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:52:7 - error TS2304: Cannot find name 'expect'.

52       expect(chestIntensity).toBeDefined();
         ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:53:7 - error TS2304: Cannot find name 'expect'.

53       expect(chestIntensity?.isPrimary).toBe(true);
         ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:57:7 - error TS2304: Cannot find name 'expect'.

57       expect(tricepsIntensity).toBeDefined();
         ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:61:7 - error TS2304: Cannot find name 'expect'.

61       expect(totalPercentage).toBeCloseTo(1, 1); // 总和应该接近1
         ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:64:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

64     it('should handle empty exercises array', () => {
       ~~

src/utils/__tests__/muscleColorCalculator.test.ts:66:7 - error TS2304: Cannot find name 'expect'.

66       expect(intensities).toEqual([]);
         ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:70:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

70   describe('generateMuscleColorConfig', () => {
     ~~~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:71:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

71     it('should generate color config correctly', () => {
       ~~

src/utils/__tests__/muscleColorCalculator.test.ts:77:9 - error TS2304: Cannot find name 'expect'.

77         expect(['light', 'medium', 'heavy']).toContain(muscleConfig.intensity);
           ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:78:9 - error TS2304: Cannot find name 'expect'.

78         expect(['primary', 'secondary']).toContain(muscleConfig.type);
           ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:83:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

83   describe('generateStaticMuscleColorConfig', () => {
     ~~~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:84:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

84     it('should generate static muscle color config', () => {
       ~~

src/utils/__tests__/muscleColorCalculator.test.ts:90:9 - error TS2304: Cannot find name 'expect'.

90         expect(['primary', 'secondary']).toContain(type);
           ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:95:3 - error TS2582: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

95   describe('validateWorkoutData', () => {
     ~~~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:96:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

96     it('should validate correct workout data', () => {
       ~~

src/utils/__tests__/muscleColorCalculator.test.ts:98:7 - error TS2304: Cannot find name 'expect'.

98       expect(result.isValid).toBe(true);
         ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:99:7 - error TS2304: Cannot find name 'expect'.

99       expect(result.errors).toHaveLength(0);
         ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:102:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

102     it('should detect invalid workout data', () => {
        ~~

src/utils/__tests__/muscleColorCalculator.test.ts:115:7 - error TS2304: Cannot find name 'expect'.

115       expect(result.isValid).toBe(false);
          ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:116:7 - error TS2304: Cannot find name 'expect'.

116       expect(result.errors.length).toBeGreaterThan(0);
          ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:119:5 - error TS2582: Cannot find name 'it'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha`.

119     it('should handle empty exercises array', () => {
        ~~

src/utils/__tests__/muscleColorCalculator.test.ts:121:7 - error TS2304: Cannot find name 'expect'.

121       expect(result.isValid).toBe(false);
          ~~~~~~

src/utils/__tests__/muscleColorCalculator.test.ts:122:7 - error TS2304: Cannot find name 'expect'.

122       expect(result.errors).toContain('训练动作列表不能为空');
          ~~~~~~

src/utils/apiClient.ts:6:10 - error TS6133: 'authService' is declared but its value is never read.

6 import { authService, AuthService } from '../services/authService';
           ~~~~~~~~~~~

src/utils/apiClient.ts:60:32 - error TS2372: Parameter 'authService' cannot reference itself.

60     authService: AuthService = authService
                                  ~~~~~~~~~~~

src/utils/dataTransformers.ts:18:3 - error TS6133: 'MuscleGroupIntensity' is declared but its value is never read.

18   MuscleGroupIntensity
     ~~~~~~~~~~~~~~~~~~~~

src/utils/dataTransformers.ts:42:37 - error TS2339: Property 'SHOULDERS_REAR' does not exist on type 'typeof MuscleGroupEnum'.

42   'shoulders_rear': MuscleGroupEnum.SHOULDERS_REAR,
                                       ~~~~~~~~~~~~~~

src/utils/dataTransformers.ts:43:37 - error TS2339: Property 'SHOULDERS_SIDE' does not exist on type 'typeof MuscleGroupEnum'.

43   'shoulders_side': MuscleGroupEnum.SHOULDERS_SIDE,
                                       ~~~~~~~~~~~~~~

src/utils/dataTransformers.ts:65:26 - error TS2339: Property 'ABS' does not exist on type 'typeof MuscleGroupEnum'.

65   'abs': MuscleGroupEnum.ABS,
                            ~~~

src/utils/dataTransformers.ts:66:27 - error TS2339: Property 'ABS' does not exist on type 'typeof MuscleGroupEnum'.

66   'core': MuscleGroupEnum.ABS,
                             ~~~

src/utils/dataTransformers.ts:67:25 - error TS2339: Property 'ABS' does not exist on type 'typeof MuscleGroupEnum'.

67   '腹肌': MuscleGroupEnum.ABS,
                           ~~~

src/utils/dataTransformers.ts:68:25 - error TS2339: Property 'ABS' does not exist on type 'typeof MuscleGroupEnum'.

68   '核心': MuscleGroupEnum.ABS
                           ~~~

src/utils/dataTransformers.ts:74:10 - error TS6133: 'mapStringToMuscleEnum' is declared but its value is never read.

74 function mapStringToMuscleEnum(muscleString: string): MuscleGroupEnum | null {
            ~~~~~~~~~~~~~~~~~~~~~

src/utils/dataValidators.ts:6:1 - error TS6192: All imports in import declaration are unused.

6 import { ApiFeedPost, ApiUser, ApiWorkoutDetail } from '../types/feed.types';
  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/utils/muscleColorCalculator.ts:24:44 - error TS2363: The right-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.

24   return exercise.sets * exercise.weight * exercise.reps;
                                              ~~~~~~~~~~~~~

src/utils/muscleColorCalculator.ts:233:9 - error TS2365: Operator '<=' cannot be applied to types 'string | number' and 'number'.

233     if (exercise.reps <= 0) {
            ~~~~~~~~~~~~~~~~~~

src/utils/testDataFetcher.ts:96:28 - error TS2339: Property 'firstItem' does not exist on type '{}'.

96           analysis.samples.firstItem = firstItem;
                              ~~~~~~~~~

src/utils/testDataFetcher.ts:121:30 - error TS2339: Property 'firstItem' does not exist on type '{}'.

121             analysis.samples.firstItem = data.items[0];
                                 ~~~~~~~~~


Found 114 errors in 10 files.

Errors  Files
     2  src/components/fitness/ExerciseList/ExerciseList.tsx:77
    46  src/services/__tests__/authService.test.ts:9
     1  src/services/authService.ts:296
    20  src/services/communityService.ts:82
    30  src/utils/__tests__/muscleColorCalculator.test.ts:34
     2  src/utils/apiClient.ts:6
     8  src/utils/dataTransformers.ts:18
     1  src/utils/dataValidators.ts:6
     2  src/utils/muscleColorCalculator.ts:24
     2  src/utils/testDataFetcher.ts:96