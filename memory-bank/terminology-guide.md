# Hevy Fitness App - 术语规范指南

## 📝 概述
本文档旨在统一整个应用中的术语使用，确保界面文字的一致性，避免重复内容，提升用户体验。

## 🎯 核心术语规范

### 主要功能模块
| 中文术语 | 英文对照 | 使用场景 | 避免使用 |
|---------|---------|---------|----------|
| **训练** | Workout | 主要功能，训练记录页面 | 健身、锻炼 |
| **动作库** | Exercise Library | 运动动作集合 | 运动库、练习库 |
| **训练计划** | Routine | 预设的训练安排 | 健身计划、锻炼计划 |
| **动态** | Feed | 社交功能，用户分享 | 朋友圈、动态流 |
| **个人资料** | Profile | 用户信息页面 | 个人信息、用户资料 |

### 训练相关术语
| 中文术语 | 英文对照 | 说明 | 避免使用 |
|---------|---------|------|----------|
| **组** | Set | 一组重复动作 | 次数组、训练组 |
| **次** | Rep | 单次动作重复 | 重复次数 |
| **重量** | Weight | 训练负重 | 负荷、重量负荷 |
| **休息时间** | Rest Time | 组间休息 | 间歇时间 |
| **训练容量** | Volume | 总重量×次数 | 训练量、总负荷 |
| **个人纪录** | Personal Record (PR) | 最佳成绩 | 最好成绩、记录 |

### 数据统计术语
| 中文术语 | 英文对照 | 使用场景 | 避免使用 |
|---------|---------|---------|----------|
| **训练天数** | Workout Days | 累计训练日 | 健身天数 |
| **总容量** | Total Volume | 累计训练重量 | 总重量、累计重量 |
| **训练时长** | Duration | 单次训练时间 | 训练时间 |
| **完成次数** | Times Completed | 计划完成统计 | 完成数量 |

## 🚫 需要删除的重复内容

### 项目介绍重复文字
以下内容在多个页面重复出现，需要删除或整合：

1. **"基于Hevy设计的现代健身追踪应用"** - 仅在关于页面保留
2. **"记录训练、跟踪进度、建立健身习惯"** - 统一为简洁版本
3. **技术栈介绍** - 移至关于页面或文档
4. **项目特性说明** - 避免在多个界面重复

### 功能描述统一化
| 原描述（重复使用） | 统一描述 | 使用位置 |
|------------------|----------|----------|
| "健身追踪应用" | "训练记录" | 页面标题 |
| "记录训练数据" | "训练记录" | 功能说明 |
| "跟踪健身进度" | "进度追踪" | 统计页面 |
| "健身社交功能" | "训练分享" | 动态页面 |

## 📱 界面文字规范

### 按钮文字标准
| 功能 | 标准文字 | 避免使用 |
|------|----------|----------|
| 开始训练 | "开始训练" | "开始锻炼"、"开始健身" |
| 结束训练 | "结束训练" | "完成训练"、"停止训练" |
| 保存数据 | "保存" | "保存数据"、"确认保存" |
| 查看详情 | "查看详情" | "详细信息"、"更多" |
| 添加动作 | "添加动作" | "添加运动"、"新增动作" |

### 状态提示标准
| 状态 | 标准文字 | 颜色规范 |
|------|----------|----------|
| 进行中 | "训练中" | 蓝色 (accent-500) |
| 已完成 | "已完成" | 绿色 (success-500) |
| 已暂停 | "已暂停" | 橙色 (warning-500) |
| 未开始 | "未开始" | 灰色 (text-secondary) |

## 🎨 设计一致性规范

### 卡片组件标准
```scss
// 统一卡片样式
.card-component {
  background: var(--bg-surface);
  border: 1px solid var(--primary-500);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  transition: all var(--transition-normal);
  
  &:hover {
    border-color: var(--accent-500);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1);
  }
}
```

### 间距使用规范
- **页面主要间距**: `var(--space-8)` (2rem)
- **组件间距**: `var(--space-6)` (1.5rem)  
- **元素内间距**: `var(--space-4)` (1rem)
- **小元素间距**: `var(--space-2)` (0.5rem)

### 字体层级规范
```scss
// 标题层级
.title-primary { font-size: var(--text-2xl); font-weight: var(--font-bold); }
.title-secondary { font-size: var(--text-xl); font-weight: var(--font-semibold); }
.title-tertiary { font-size: var(--text-lg); font-weight: var(--font-medium); }

// 正文层级
.text-body { font-size: var(--text-base); font-weight: var(--font-normal); }
.text-caption { font-size: var(--text-sm); color: var(--text-secondary); }
.text-label { font-size: var(--text-xs); text-transform: uppercase; }
```

## 📋 实施清单

### ✅ 已完成优化
- [x] 将HomePage重构为WorkoutPage（核心训练页面）
- [x] 统一导航栏中文标签
- [x] 删除项目介绍重复内容
- [x] 建立统一的设计系统CSS变量

### 🔄 进行中优化
- [ ] 完善ProfilePage的健身数据展示
- [ ] 优化RoutinesPage的训练计划管理
- [ ] 统一所有页面的卡片组件样式
- [ ] 规范化所有按钮和表单组件

### 📝 待优化内容
- [ ] 清理FeedPage中的重复描述文字
- [ ] 统一ExercisesPage的术语使用
- [ ] 优化SettingsPage的功能描述
- [ ] 建立完整的组件库文档

## 🎯 设计目标对比

### 官方Hevy核心理念
1. **简洁专业** - 专注核心功能，减少冗余
2. **数据驱动** - 突出训练数据和进度追踪  
3. **社交互动** - 适度的社交功能，不喧宾夺主

### 当前项目优化方向
1. **功能聚焦** - 以训练记录为核心，其他功能辅助
2. **界面统一** - 建立完整的设计系统和组件库
3. **内容精简** - 删除重复文字，统一术语规范
4. **体验优化** - 提升页面加载性能和交互流畅度

## 📖 使用指南

### 开发人员
1. 新增功能时参考本规范确定术语
2. 使用统一的CSS类和组件
3. 避免创建重复的界面文字
4. 遵循设计系统的颜色和间距规范

### 设计师
1. 使用规范中的标准术语
2. 保持卡片组件和布局的一致性
3. 遵循字体层级和色彩使用规范
4. 确保新设计符合整体风格

---

**更新日期**: 2025-01-17  
**版本**: v1.0  
**维护者**: Hevy Fitness App 开发团队 