# 社区功能 API 文档

## 目录
1. [概述](#概述)
2. [数据模型](#数据模型)
3. [API 接口](#api-接口)
4. [数据流程](#数据流程)
5. [错误处理](#错误处理)

## 概述

社区功能为用户提供了一个互动平台，允许用户分享训练计划、发布帖子、评论互动、关注其他用户等。主要功能包括：

- 每日训练分享
- 帖子发布与管理
- 评论系统
- 点赞功能
- 用户关注系统
- 通知系统
- 图片管理

## 数据模型

### 1. 每日训练 (DailyWorkout)
```python
class DailyWorkout(Base):
    __tablename__ = "daily_workouts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(100), nullable=False)
    content = Column(String(1000), nullable=False)
    status = Column(Enum(DailyWorkoutStatus), default=DailyWorkoutStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 2. 帖子 (Post)
```python
class Post(Base):
    __tablename__ = "posts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(100), nullable=False)
    content = Column(String(5000), nullable=False)
    related_workout_id = Column(Integer, ForeignKey("daily_workouts.id"), nullable=True)
    like_count = Column(Integer, default=0)
    comment_count = Column(Integer, default=0)
    share_count = Column(Integer, default=0)
    status = Column(Enum(PostStatus), default=PostStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 3. 评论 (Comment)
```python
class Comment(Base):
    __tablename__ = "comments"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=False)
    parent_id = Column(Integer, ForeignKey("comments.id"), nullable=True)
    content = Column(String(1000), nullable=False)
    like_count = Column(Integer, default=0)
    status = Column(Enum(CommentStatus), default=CommentStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 4. 通知 (Notification)
```python
class Notification(Base):
    __tablename__ = "notifications"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    type = Column(Enum(NotificationType), nullable=False)
    content = Column(String(500), nullable=False)
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
```

### 5. 用户关系 (UserRelation)
```python
class UserRelation(Base):
    __tablename__ = "user_relations"
    
    id = Column(Integer, primary_key=True, index=True)
    follower_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    following_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
```

### 6. 图片 (Image)
```python
class Image(Base):
    __tablename__ = "images"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=True)
    url = Column(String(500), nullable=False)
    title = Column(String(100), nullable=True)
    description = Column(String(500), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

## API 接口

### 1. 每日训练接口

#### 创建每日训练
```http
POST /api/v1/community/daily-workouts/
Content-Type: application/json

{
    "title": "今日训练计划",
    "content": "训练内容...",
    "exercises": [
        {
            "exercise_id": 1,
            "sets": 3,
            "reps": "12-15",
            "rest_seconds": 60
        }
    ]
}
```

#### 更新每日训练
```http
PUT /api/v1/community/daily-workouts/{workout_id}
Content-Type: application/json

{
    "title": "更新后的训练计划",
    "content": "更新后的内容..."
}
```

#### 获取每日训练列表
```http
GET /api/v1/community/daily-workouts/?skip=0&limit=20
```

#### 搜索每日训练
```http
GET /api/v1/community/daily-workouts/search/?keyword=训练&skip=0&limit=20
```

### 2. 帖子接口

#### 创建帖子
```http
POST /api/v1/community/posts/
Content-Type: application/json

{
    "title": "我的训练心得",
    "content": "帖子内容...",
    "related_workout_id": 1,
    "image_urls": ["url1", "url2"]
}
```

#### 更新帖子
```http
PUT /api/v1/community/posts/{post_id}
Content-Type: application/json

{
    "title": "更新后的标题",
    "content": "更新后的内容..."
}
```

#### 获取帖子列表
```http
GET /api/v1/community/posts/?skip=0&limit=20
```

#### 点赞帖子
```http
POST /api/v1/community/posts/{post_id}/like/
```

#### 举报帖子
```http
POST /api/v1/community/posts/{post_id}/report/
Content-Type: application/json

{
    "reason": "违规内容"
}
```

### 3. 评论接口

#### 创建评论
```http
POST /api/v1/community/posts/{post_id}/comments/
Content-Type: application/json

{
    "content": "评论内容",
    "parent_id": null
}
```

#### 更新评论
```http
PUT /api/v1/community/comments/{comment_id}
Content-Type: application/json

{
    "content": "更新后的评论内容"
}
```

#### 点赞评论
```http
POST /api/v1/community/comments/{comment_id}/like/
```

#### 举报评论
```http
POST /api/v1/community/comments/{comment_id}/report/
Content-Type: application/json

{
    "reason": "违规内容"
}
```

### 4. 通知接口

#### 获取通知列表
```http
GET /api/v1/community/notifications/?skip=0&limit=20
```

#### 标记通知为已读
```http
PATCH /api/v1/community/notifications/{notification_id}/read/
```

#### 标记所有通知为已读
```http
PATCH /api/v1/community/notifications/read-all/
```

### 5. 用户关系接口

#### 关注用户
```http
POST /api/v1/community/users/{user_id}/follow/
```

#### 取消关注
```http
DELETE /api/v1/community/users/{user_id}/follow/
```

#### 获取关注列表
```http
GET /api/v1/community/users/{user_id}/following/?skip=0&limit=20
```

#### 获取粉丝列表
```http
GET /api/v1/community/users/{user_id}/followers/?skip=0&limit=20
```

### 6. 图片接口

#### 上传图片
```http
POST /api/v1/community/images/
Content-Type: multipart/form-data

{
    "file": "图片文件",
    "title": "图片标题",
    "description": "图片描述",
    "post_id": 1
}
```

#### 更新图片信息
```http
PUT /api/v1/community/images/{image_id}
Content-Type: application/json

{
    "title": "新标题",
    "description": "新描述"
}
```

## 数据流程

### 1. 帖子发布流程
1. 用户创建帖子
2. 系统验证用户权限和内容
3. 保存帖子到数据库
4. 更新用户帖子计数
5. 发送通知给关注者

### 2. 评论流程
1. 用户发表评论
2. 系统验证评论内容
3. 保存评论到数据库
4. 更新帖子评论计数
5. 发送通知给帖子作者

### 3. 点赞流程
1. 用户点赞
2. 系统检查是否已点赞
3. 更新点赞计数
4. 发送通知给被点赞者

### 4. 关注流程
1. 用户关注其他用户
2. 系统检查是否已关注
3. 创建关注关系
4. 发送通知给被关注者

## 错误处理

### 通用错误码
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器内部错误

### 业务错误码
- 1001: 内容违规
- 1002: 重复操作
- 1003: 资源不存在
- 1004: 权限不足
- 1005: 操作失败

### 错误响应格式
```json
{
    "code": 1001,
    "message": "内容包含违规信息",
    "detail": "具体错误详情"
}
``` 

# Community API 优化修复报告

## 修复概述

本次对 `app/api/endpoints/community.py` 中的接口进行了以下关键优化：

### 1. 评论接口422错误修复 ✅

**问题描述：**
- 创建评论接口报错422，提示缺少 `post_id` 字段
- 错误信息：`{"detail":[{"type":"missing","loc":["body","post_id"],"msg":"Field required","input":{"content":"nice"}}]}`

**解决方案：**
```python
@router.post("/posts/{post_id}/comments/", response_model=CommentResponse)
async def create_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int,  # 从路径参数获取
    comment_in: CommentCreate
):
    # 将路径参数中的post_id设置到comment_in中
    comment_in.post_id = post_id
    service = CommunityService(db)
    return await service.create_comment(current_user.id, comment_in)
```

**修复内容：**
- 在接口中从路径参数 `post_id` 获取帖子ID
- 自动设置到 `comment_in.post_id` 中，避免客户端重复传递

### 2. 点赞和关注切换逻辑实现 ✅

**问题描述：**
- 原有点赞和关注接口只能单向操作
- 需要支持切换逻辑：已点赞/关注则取消，未点赞/关注则创建

**解决方案：**

#### 点赞切换接口
```python
@router.post("/posts/{post_id}/like/")
async def toggle_post_like(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int
):
    service = CommunityService(db)
    return await service.toggle_post_like(post_id, current_user.id)
```

#### 服务层实现逻辑
```python
async def toggle_post_like(self, post_id: int, user_id: int) -> Dict[str, Any]:
    # 检查是否已经点赞
    existing_like = self.db.query(PostLike).filter(
        PostLike.post_id == post_id,
        PostLike.user_id == user_id
    ).first()
    
    if existing_like:
        # 已点赞，取消点赞
        self.db.delete(existing_like)
        post.like_count = max(0, post.like_count - 1)
        action = "unliked"
    else:
        # 未点赞，创建点赞
        new_like = PostLike(post_id=post_id, user_id=user_id)
        self.db.add(new_like)
        post.like_count += 1
        action = "liked"
        
        # 只在点赞时创建通知
        if post.user_id != user_id:
            # 创建通知逻辑...
    
    return {
        "message": f"Post {action} successfully",
        "action": action,
        "like_count": post.like_count,
        "is_liked": action == "liked"
    }
```

**类似地实现了：**
- `toggle_comment_like()` - 评论点赞切换
- `toggle_follow_user()` - 用户关注切换

### 3. 通知系统优化 ✅

**改进内容：**
- 通知创建时添加了相关资源ID字段
- 改进了通知内容的用户友好性
- 只在正向操作（点赞/关注/评论）时创建通知，取消操作不删除历史通知

**通知结构优化：**
```python
notification = Notification(
    user_id=post.user_id,
    type=NotificationType.LIKE,
    content=f"有人赞了你的帖子",
    related_post_id=post_id,        # 关联的帖子ID
    related_comment_id=comment_id,  # 关联的评论ID（如果适用）
    related_user_id=user_id         # 操作用户ID
)
```

### 4. 帖子分享功能新增 ✅

**新增接口：**

#### 获取分享信息
```python
@router.get("/posts/{post_id}/share-info/")
async def get_post_share_info(
    *,
    db: Session = Depends(deps.get_db),
    post_id: int
):
    """获取帖子分享信息，用于微信等平台分享"""
    service = CommunityService(db)
    return await service.get_post_share_info(post_id)
```

#### 记录分享行为
```python
@router.post("/posts/{post_id}/share/")
async def share_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int,
    platform: str = Query(..., description="分享平台：wechat_friend, wechat_moments, weibo等")
):
    """记录帖子分享行为"""
    service = CommunityService(db)
    return await service.record_post_share(post_id, current_user.id, platform)
```

**分享信息结构：**
```json
{
    "id": 1,
    "title": "帖子标题",
    "content": "帖子内容摘要（前100字符）...",
    "author_name": "作者昵称",
    "author_avatar": "作者头像URL",
    "image_url": "分享图片URL",
    "like_count": 10,
    "comment_count": 5,
    "created_at": "2024-01-15T10:30:00Z",
    "share_url": "https://yourapp.com/posts/1"
}
```

## 新增Schema模型

在 `app/schemas/community.py` 中新增：

```python
class PostShareInfo(BaseModel):
    """帖子分享信息模型"""
    id: int
    title: str
    content: str
    author_name: str
    author_avatar: Optional[str] = None
    image_url: Optional[str] = None
    like_count: int = 0
    comment_count: int = 0
    created_at: str
    share_url: str

class PostShareRecord(BaseModel):
    """帖子分享记录模型"""
    post_id: int
    user_id: int
    platform: str
    shared_at: datetime
```

## API接口变更总结

### 修改的接口

| 接口 | 原名称 | 新名称 | 变更类型 |
|------|--------|--------|----------|
| POST `/posts/{post_id}/like/` | `like_post` | `toggle_post_like` | 功能增强 |
| POST `/comments/{comment_id}/like/` | `like_comment` | `toggle_comment_like` | 功能增强 |
| POST `/users/{user_id}/follow/` | `follow_user` | `toggle_follow_user` | 功能增强 |
| POST `/posts/{post_id}/comments/` | `create_comment` | `create_comment` | Bug修复 |

### 新增的接口

| 接口 | 功能 | 响应格式 |
|------|------|----------|
| GET `/posts/{post_id}/share-info/` | 获取帖子分享信息 | PostShareInfo |
| POST `/posts/{post_id}/share/` | 记录帖子分享行为 | 分享统计结果 |

## 响应格式优化

### 切换操作响应
```json
{
    "message": "Post liked successfully",
    "action": "liked",  // "liked" | "unliked"
    "like_count": 15,
    "is_liked": true
}
```

### 关注操作响应
```json
{
    "message": "User followed successfully",
    "action": "followed",  // "followed" | "unfollowed"
    "is_following": true
}
```

## 使用示例

### 客户端点赞切换
```javascript
// 点赞/取消点赞
const response = await fetch('/api/v1/community/posts/1/like/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    }
});

const result = await response.json();
// result.action: "liked" 或 "unliked"
// result.is_liked: true 或 false
// result.like_count: 当前点赞数
```

### 微信分享集成
```javascript
// 1. 获取分享信息
const shareInfoResponse = await fetch('/api/v1/community/posts/1/share-info/');
const shareInfo = await shareInfoResponse.json();

// 2. 配置微信分享
wx.updateAppMessageShareData({
    title: shareInfo.title,
    desc: shareInfo.content,
    link: shareInfo.share_url,
    imgUrl: shareInfo.image_url,
    success: function () {
        // 3. 记录分享行为
        fetch('/api/v1/community/posts/1/share/?platform=wechat_friend', {
            method: 'POST',
            headers: { 'Authorization': 'Bearer ' + token }
        });
    }
});
```

## 测试建议

1. **评论创建测试**
   - 验证post_id不再需要在请求体中传递
   - 确认路径参数正确传递到service层

2. **点赞切换测试**
   - 连续调用同一接口，验证点赞状态正确切换
   - 检查like_count的正确计算

3. **关注切换测试**
   - 验证关注/取消关注的切换逻辑
   - 确认通知正确创建

4. **分享功能测试**
   - 验证分享信息的完整性
   - 测试不同平台的分享记录

## 部署注意事项

1. **数据库迁移**
   - 确保PostLike、CommentLike、UserRelation表存在
   - 检查Notification表的相关字段

2. **配置更新**
   - 更新分享URL的域名配置
   - 确认微信等第三方平台的回调配置

3. **性能考虑**
   - 点赞状态查询可考虑添加缓存
   - 通知系统可考虑异步处理

## 总结

本次修复解决了社区模块的核心交互问题，提升了用户体验：

✅ **解决了评论创建的422错误**
✅ **实现了现代化的点赞/关注切换逻辑**
✅ **完善了通知系统的信息结构**
✅ **新增了完整的分享功能支持**

所有修改都向后兼容，不影响现有功能，同时为未来扩展提供了良好的基础。 