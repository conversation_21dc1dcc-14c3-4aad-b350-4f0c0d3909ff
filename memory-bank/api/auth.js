/**
 * 认证相关API
 */

// 导入基础配置
const { BASE_URL, IS_DEV_ENV, DEV_CONFIG, testConnection } = require('./request');

/**
 * 微信登录
 * @param {Object} data - 登录数据，包含code和用户信息
 * @returns {Promise} 返回登录结果
 */
const wechatLogin = (data) => {
  // 打印请求数据用于调试
  console.log('【微信登录】请求数据:', JSON.stringify(data));
  console.log('【微信登录】请求URL:', `${BASE_URL}/api/v1/auth/login/wechat`);
  
  // 按照后端接口文档格式化数据，确保字段名称完全匹配后端的WechatLoginRequest模型
  const formattedData = {
    code: data.code,
    userInfo: data.userInfo || null,
    encryptedData: data.encryptedData || null,
    iv: data.iv || null,
    openid: data.openid || null,
    unionid: data.unionid || null
  };
  
  console.log('【微信登录】格式化后数据:', JSON.stringify(formattedData));
  
  return new Promise((resolve, reject) => {
    // 检查是否使用模拟数据
    if (IS_DEV_ENV && DEV_CONFIG.USE_MOCK_DATA) {
      console.log('【微信登录】开发环境下，使用模拟数据');
      // 在开发环境中，使用模拟数据避免白名单问题
      setTimeout(() => {
        const mockData = {
          success: true,
          token: 'mock_token_' + Date.now(),
          user: {
            id: 1,
            nickName: formattedData.userInfo ? formattedData.userInfo.nickName : '微信用户',
            avatarUrl: formattedData.userInfo ? formattedData.userInfo.avatarUrl : '',
            phone: '',
            gender: formattedData.userInfo ? formattedData.userInfo.gender : 0,
            country: formattedData.userInfo ? formattedData.userInfo.country : '',
            province: formattedData.userInfo ? formattedData.userInfo.province : '',
            city: formattedData.userInfo ? formattedData.userInfo.city : ''
          },
          is_new_user: Math.random() > 0.5 // 模拟新用户标识
        };
        
        // 确保模拟数据中的avatarUrl是完整URL
        if (mockData.user.avatarUrl && 
            typeof mockData.user.avatarUrl === 'string' && 
            !mockData.user.avatarUrl.startsWith('http') &&
            !mockData.user.avatarUrl.startsWith('data:')) {
          mockData.user.avatarUrl = `${BASE_URL}${mockData.user.avatarUrl}`;
        }
        
        console.log('【微信登录】返回模拟数据:', mockData);
        resolve(mockData);
      }, DEV_CONFIG.MOCK_DELAY);
      return;
    }
    
    // 先测试服务器连接
    console.log('【微信登录】正在测试服务器连接...');
    
    // 正常请求后端
    wx.request({
      url: `${BASE_URL}/api/v1/auth/login/wechat`,
      method: 'POST',
      data: formattedData,
      header: {
        'Content-Type': 'application/json'
      },
      timeout: 20000,
      success: (res) => {
        console.log('【微信登录】响应状态码:', res.statusCode);
        console.log('【微信登录】响应数据:', JSON.stringify(res.data));
        
        if (res.statusCode === 200) {
          // 处理响应数据，确保avatarUrl包含完整URL
          if (res.data && res.data.user) {
            // 处理时区问题 - 清理created_at的时区信息
            if (res.data.user.created_at && typeof res.data.user.created_at === 'string') {
              // 移除时区标记，例如将"2025-04-21T22:56:16.820624+08:00"处理为"2025-04-21T22:56:16.820624"
              res.data.user.created_at = res.data.user.created_at.replace(/([+-]\d{2}:\d{2})$/, '');
              console.log('【微信登录】处理后的created_at:', res.data.user.created_at);
            }
            
            // 检查avatarUrl是否为相对路径（不以http开头）
            if (res.data.user.avatarUrl && 
                typeof res.data.user.avatarUrl === 'string' && 
                !res.data.user.avatarUrl.startsWith('http') &&
                !res.data.user.avatarUrl.startsWith('data:')) {
              // 添加BASE_URL前缀
              res.data.user.avatarUrl = `${BASE_URL}${res.data.user.avatarUrl}`;
              console.log('【微信登录】已处理avatarUrl:', res.data.user.avatarUrl);
            }
            
            // 同样处理avatar_url字段（如果存在）
            if (res.data.user.avatar_url && 
                typeof res.data.user.avatar_url === 'string' && 
                !res.data.user.avatar_url.startsWith('http') &&
                !res.data.user.avatar_url.startsWith('data:')) {
              // 添加BASE_URL前缀
              res.data.user.avatar_url = `${BASE_URL}${res.data.user.avatar_url}`;
              console.log('【微信登录】已处理avatar_url:', res.data.user.avatar_url);
            }
          }
          
          resolve(res.data);
        } else {
          console.error('【微信登录】请求失败:', JSON.stringify(res));
          
          // 为用户提供更友好的错误信息
          let errorMsg = '登录失败';
          if (res.data && res.data.detail) {
            errorMsg = res.data.detail;
          } else if (res.data && res.data.message) {
            errorMsg = res.data.message;
          }
          
          reject({
            statusCode: res.statusCode,
            message: errorMsg,
            fullError: res
          });
        }
      },
      fail: (err) => {
        console.error('【微信登录】请求错误:', JSON.stringify(err));
        
        // 网络错误处理
        let errorMsg = '网络连接失败';
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMsg = '服务器响应超时';
          } else if (err.errMsg.includes('parse')) {
            errorMsg = '服务器响应数据格式错误';
          }
        }
        
        reject({
          message: errorMsg,
          fullError: err
        });
      },
      complete: () => {
        console.log('【微信登录】请求完成');
      }
    });
  });
};

/**
 * 发送手机验证码
 * @param {String} phone - 手机号
 * @returns {Promise} 返回发送结果
 */
const sendSmsCode = (phone) => {
  return new Promise((resolve, reject) => {
    // 模拟发送验证码
    // 实际项目中应当连接真实后端接口
    setTimeout(() => {
      resolve({ success: true, message: '验证码发送成功' });
    }, 1000);
    
    // 实际接口调用示例
    /*
    wx.request({
      url: `${BASE_URL}/api/v1/auth/sms/code`,
      method: 'POST',
      data: { phone },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res);
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
    */
  });
};

/**
 * 手机号验证码登录
 * @param {String} phone - 手机号
 * @param {String} code - 验证码
 * @returns {Promise} 返回登录结果
 */
const phoneLogin = (phone, code) => {
  return new Promise((resolve, reject) => {
    // 模拟验证
    // 实际项目中应当连接真实后端接口
    setTimeout(() => {
      if (code === '123456') {
        const mockData = {
          success: true,
          token: 'mock_token_' + Date.now(),
          user: {
            id: 1,
            nickName: '用户' + phone.substring(7),
            avatarUrl: 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132'
          }
        };
        
        // 确保模拟数据中的avatarUrl是完整URL
        if (mockData.user.avatarUrl && 
            typeof mockData.user.avatarUrl === 'string' && 
            !mockData.user.avatarUrl.startsWith('http') &&
            !mockData.user.avatarUrl.startsWith('data:')) {
          mockData.user.avatarUrl = `${BASE_URL}${mockData.user.avatarUrl}`;
        }
        
        resolve(mockData);
      } else {
        reject({ message: '验证码错误' });
      }
    }, 1000);
  });
};

/**
 * 绑定微信手机号
 * @param {Object} data - 包含加密数据和iv的对象
 * @param {String} token - 用户令牌
 * @returns {Promise} 返回API响应
 */
const bindWechatPhone = (data, token) => {
  console.log('【绑定手机号】请求数据:', JSON.stringify(data));
  console.log('【绑定手机号】请求URL:', `${BASE_URL}/api/v1/users/bind_phone`);
  
  // 格式化数据以符合后端API要求
  const formattedData = {
    encrypted_data: data.encryptedData, // 注意后端可能使用下划线格式
    iv: data.iv,
    code: data.code
  };
  
  console.log('【绑定手机号】格式化后数据:', JSON.stringify(formattedData));
  
  return new Promise((resolve, reject) => {
    // 检查是否使用模拟数据
    if (IS_DEV_ENV && DEV_CONFIG.USE_MOCK_DATA) {
      console.log('【绑定手机号】开发环境下，使用模拟数据');
      // 在开发环境中使用模拟数据，避免IP白名单问题
      setTimeout(() => {
        const mockData = {
          success: true,
          user: {
            id: 1,
            nickName: '微信用户',
            avatarUrl: '',
            phone: '138****1234' // 模拟的手机号
          },
          message: '手机号绑定成功'
        };
        console.log('【绑定手机号】返回模拟数据:', mockData);
        resolve(mockData);
      }, DEV_CONFIG.MOCK_DELAY);
      return;
    }
    
    // 正常请求后端
    wx.request({
      url: `${BASE_URL}/api/v1/users/bind_phone`,
      method: 'POST',
      data: formattedData,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      timeout: 10000, // 10秒超时
      success: (res) => {
        console.log('【绑定手机号】响应状态码:', res.statusCode);
        console.log('【绑定手机号】响应数据:', JSON.stringify(res.data));
        
        if (res.statusCode === 200) {
          // 确保返回的用户数据中的avatarUrl包含完整URL
          if (res.data && res.data.user) {
            // 处理avatarUrl
            if (res.data.user.avatarUrl && 
                typeof res.data.user.avatarUrl === 'string' && 
                !res.data.user.avatarUrl.startsWith('http') &&
                !res.data.user.avatarUrl.startsWith('data:')) {
              res.data.user.avatarUrl = `${BASE_URL}${res.data.user.avatarUrl}`;
              console.log('【绑定手机号】已处理avatarUrl:', res.data.user.avatarUrl);
            }
            
            // 处理avatar_url
            if (res.data.user.avatar_url && 
                typeof res.data.user.avatar_url === 'string' && 
                !res.data.user.avatar_url.startsWith('http') &&
                !res.data.user.avatar_url.startsWith('data:')) {
              res.data.user.avatar_url = `${BASE_URL}${res.data.user.avatar_url}`;
              console.log('【绑定手机号】已处理avatar_url:', res.data.user.avatar_url);
            }
          }
          
          resolve(res.data);
        } else {
          console.error('【绑定手机号】请求失败:', JSON.stringify(res));
          
          // 检查是否是白名单错误
          if (res.data && res.data.detail && res.data.detail.includes('not in whitelist')) {
            wx.showModal({
              title: 'IP白名单错误',
              content: '服务器IP不在微信平台的白名单中。请在微信开发者平台添加服务器IP，或使用代理服务。',
              showCancel: false
            });
          }
          
          reject(res);
        }
      },
      fail: (err) => {
        console.error('【绑定手机号】请求错误:', JSON.stringify(err));
        
        // 检查是否为网络错误
        if (err.errMsg && err.errMsg.includes('request:fail')) {
          console.error('【绑定手机号】网络请求失败，可能是域名或网络连接问题');
        }
        
        reject(err);
      },
      complete: () => {
        console.log('【绑定手机号】请求完成');
      }
    });
  });
};

/**
 * 验证token有效性
 * @param {String} token - 登录token
 * @returns {Promise} 返回验证结果
 */
const verifyToken = (token) => {
  return new Promise((resolve, reject) => {
    // 开发环境使用模拟数据
    if (IS_DEV_ENV && DEV_CONFIG.USE_MOCK_DATA) {
      setTimeout(() => {
        resolve({ valid: true });
      }, DEV_CONFIG.MOCK_DELAY);
      return;
    }

    wx.request({
      url: `${BASE_URL}/api/v1/auth/verify`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve({ valid: true });
        } else {
          resolve({ valid: false });
        }
      },
      fail: (err) => {
        console.error('验证token请求失败:', err);
        reject(err);
      }
    });
  });
};

/**
 * 通过 code 换取 openid 和 unionid
 * @param {String} code - 微信登录返回的 code
 * @returns {Promise} 返回包含 openid 和 unionid 的对象
 */
const code2Session = (code) => {
  return new Promise((resolve, reject) => {
    // 检查是否使用模拟数据
    if (IS_DEV_ENV && DEV_CONFIG.USE_MOCK_DATA) {
      console.log('【code2session】开发环境下，使用模拟数据');
      setTimeout(() => {
        const mockData = {
          openid: 'mock_openid_' + Date.now(),
          unionid: 'mock_unionid_' + Date.now()
        };
        console.log('【code2session】返回模拟数据:', mockData);
        resolve(mockData);
      }, DEV_CONFIG.MOCK_DELAY);
      return;
    }
    
    wx.request({
      url: `${BASE_URL}/api/v1/auth/wechat/code2session`,
      method: 'POST',
      data: { code },
      header: {
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      success: (res) => {
        console.log('【code2session】响应状态码:', res.statusCode);
        console.log('【code2session】响应数据:', JSON.stringify(res.data));
        
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          console.error('【code2session】请求失败:', JSON.stringify(res));
          reject({
            statusCode: res.statusCode,
            errorMsg: res.data && res.data.detail ? res.data.detail : '获取失败',
            fullError: res
          });
        }
      },
      fail: (err) => {
        console.error('【code2session】请求错误:', JSON.stringify(err));
        reject({
          errorMsg: '网络请求失败',
          fullError: err
        });
      }
    });
  });
};

/**
 * 刷新认证令牌
 * @param {String} token - 当前令牌
 * @returns {Promise} 返回包含新令牌的对象
 */
const refreshToken = (token) => {
  console.log('【刷新令牌】开始刷新认证令牌');
  
  return new Promise((resolve, reject) => {
    // 开发环境使用模拟数据
    if (IS_DEV_ENV && DEV_CONFIG.USE_MOCK_DATA) {
      console.log('【刷新令牌】开发环境下，使用模拟数据');
      setTimeout(() => {
        const mockData = {
          success: true,
          token: 'refreshed_token_' + Date.now(),
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7天后过期
        };
        console.log('【刷新令牌】返回模拟数据:', mockData);
        resolve(mockData);
      }, DEV_CONFIG.MOCK_DELAY);
      return;
    }

    // 发送刷新令牌请求
    wx.request({
      url: `${BASE_URL}/api/v1/auth/refresh`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: 10000,
      success: (res) => {
        console.log('【刷新令牌】响应状态码:', res.statusCode);
        console.log('【刷新令牌】响应数据:', JSON.stringify(res.data));
        
        if (res.statusCode === 200 && res.data && res.data.token) {
          // 保存令牌过期时间（如果后端提供）
          if (res.data.expires_at) {
            wx.setStorageSync('token_expires_at', res.data.expires_at);
          } else {
            // 默认设置为30天后过期
            const expiresAt = new Date();
            expiresAt.setDate(expiresAt.getDate() + 30);
            wx.setStorageSync('token_expires_at', expiresAt.toISOString());
          }
          
          resolve(res.data);
        } else {
          console.error('【刷新令牌】请求失败:', JSON.stringify(res));
          
          const errorMsg = res.data && res.data.detail 
            ? res.data.detail 
            : '刷新令牌失败';
          
          reject({
            message: errorMsg,
            statusCode: res.statusCode,
            data: res.data
          });
        }
      },
      fail: (err) => {
        console.error('【刷新令牌】请求错误:', JSON.stringify(err));
        
        let errorMsg = '网络连接失败';
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMsg = '服务器响应超时';
          }
        }
        
        reject({
          message: errorMsg,
          originalError: err
        });
      }
    });
  });
};

/**
 * 获取用户OpenID
 * @returns {Promise<string>} 返回用户的OpenID
 */
const getOpenId = async () => {
  console.log('【Auth API】开始获取用户OpenID');
  
  try {
    // 获取登录凭证
    const loginResult = await wx.login();
    
    if (!loginResult.code) {
      throw new Error('获取登录凭证失败');
    }
    
    // 使用登录凭证换取OpenID
    const sessionResult = await code2Session(loginResult.code);
    
    if (!sessionResult.openid) {
      throw new Error('获取OpenID失败');
    }
    
    console.log('【Auth API】获取OpenID成功');
    return sessionResult.openid;
  } catch (error) {
    console.error('【Auth API】获取OpenID失败:', error);
    throw error;
  }
};

// 导出所需的函数和变量
module.exports = {
  wechatLogin,
  verifyToken,
  refreshToken,
  bindWechatPhone,
  sendSmsCode,
  phoneLogin,
  bindWechatPhone,
  testConnection,
  verifyToken,
  code2Session,
  refreshToken,
  getOpenId
}; 