{"timestamp": "2025-07-27T16:36:34.646Z", "authResult": {"success": false, "token": "mock_token_1753634194141", "user": {"id": 1, "email": "<EMAIL>", "openid": "oCU0j7Rg9kzigLzquCBje3KfnQXk", "nickName": "测试用户"}, "error": "HTTP 404: {\"detail\":\"Not Found\"}"}, "feedData": {"total": 7, "items": [{"created_at": "2025-06-04T07:06:43.964382", "updated_at": "2025-06-04T07:06:43.964388", "title": "训练记录 - 2025-06-04 cdy15s", "content": "15s", "image_urls": null, "related_workout_id": 18, "id": 7, "user_id": 2, "like_count": 0, "comment_count": 0, "share_count": 0, "status": "ACTIVE", "reported_count": 0, "user": {"id": 2, "nickname": "微信用户", "avatar_url": "https://example.com/default-avatar.jpg", "gender": 0, "age": null, "weight": null, "height": null, "activity_level": 3, "bmi": null, "tedd": null, "completed": false, "country": null, "province": null, "city": null, "experience_level": null, "fitness_goal": null, "health_conditions": null, "allergies": null, "created_at": null, "birthday": null}, "is_liked_by_current_user": false, "related_workout": {"id": 18, "name": "训练记录 - 2025-06-04 cdy15s", "title": "训练记录 - 2025-06-04 cdy15s", "date": "2025-06-04T07:06:43.910137"}, "related_workout_detail": {"id": 67, "name": "训练记录 - 2025-06-04 cdy15s", "training_plan_id": 1, "day_of_week": null, "day_number": 1, "description": null, "estimated_duration": null, "scheduled_date": null, "status": "completed", "actual_duration": 0, "net_duration": null, "start_time": "2025-06-04T07:06:43.893097+08:00", "end_time": "2025-06-04T07:06:43.893103+08:00", "last_status_change": null, "created_at": "2025-06-04T15:06:43.891842+08:00", "updated_at": "2025-06-04T15:06:43.891842+08:00", "workout_exercises": [{"id": 257, "workout_id": 67, "exercise_id": 252, "exercise_name": "史密斯反握推举", "exercise_image": "/data/exercises/images/<PERSON>-<PERSON>erse-grip-Press9cbc4d7d0410.png", "exercise_description": null, "video_url": "", "sets": 1, "reps": "12", "rest_seconds": 60, "order": 1, "notes": null, "exercise_type": "weight_reps", "superset_group": null, "weight": null, "set_records": [{"id": 769, "workout_exercise_id": 257, "set_number": 1, "set_type": "normal", "weight": 20, "reps": 12, "completed": false, "notes": "", "created_at": "2025-06-04T15:06:43.891842+08:00", "updated_at": "2025-06-04T15:06:43.891842+08:00"}]}, {"id": 258, "workout_id": 67, "exercise_id": 8, "exercise_name": "下斜卧推", "exercise_image": "/data/exercises/images/Decline-Bench-Pressa2c4f77ce69e.png", "exercise_description": null, "video_url": "/data/exercises/videos/Decline-Bench-Pressa2c4f77ce69e.mp4", "sets": 4, "reps": "12,12,12,12", "rest_seconds": 60, "order": 2, "notes": null, "exercise_type": "weight_reps", "superset_group": null, "weight": null, "set_records": [{"id": 770, "workout_exercise_id": 258, "set_number": 1, "set_type": "normal", "weight": 20, "reps": 12, "completed": false, "notes": "", "created_at": "2025-06-04T15:06:43.891842+08:00", "updated_at": "2025-06-04T15:06:43.891842+08:00"}, {"id": 771, "workout_exercise_id": 258, "set_number": 2, "set_type": "normal", "weight": 20, "reps": 12, "completed": false, "notes": "", "created_at": "2025-06-04T15:06:43.891842+08:00", "updated_at": "2025-06-04T15:06:43.891842+08:00"}, {"id": 772, "workout_exercise_id": 258, "set_number": 3, "set_type": "normal", "weight": 20, "reps": 12, "completed": false, "notes": "", "created_at": "2025-06-04T15:06:43.891842+08:00", "updated_at": "2025-06-04T15:06:43.891842+08:00"}, {"id": 773, "workout_exercise_id": 258, "set_number": 4, "set_type": "normal", "weight": 20, "reps": 12, "completed": false, "notes": "", "created_at": "2025-06-04T15:06:43.891842+08:00", "updated_at": "2025-06-04T15:06:43.891842+08:00"}]}]}, "images": [], "comments_summary": [], "tags": ["训练记录", "胸部"], "view_count": 0}, {"created_at": "2025-05-27T10:28:43.266788", "updated_at": "2025-06-02T14:14:35.526781", "title": "练胸", "content": "感觉不错，泵感很强", "image_urls": null, "related_workout_id": 13, "id": 6, "user_id": 1, "like_count": 1, "comment_count": 2, "share_count": 0, "status": "ACTIVE", "reported_count": 0, "user": {"id": 1, "nickname": "shell", "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132", "gender": 1, "age": 29, "weight": null, "height": null, "activity_level": 3, "bmi": null, "tedd": null, "completed": false, "country": null, "province": null, "city": null, "experience_level": null, "fitness_goal": null, "health_conditions": null, "allergies": null, "created_at": null, "birthday": null}, "is_liked_by_current_user": false, "related_workout": {"id": 13, "name": "练胸", "title": "练胸", "date": "2025-05-27T10:28:43.251734"}, "related_workout_detail": {"id": 23, "name": "练胸", "training_plan_id": 24, "day_of_week": null, "day_number": 1, "description": "本训练计划旨在通过使用自重和杠铃来增强手臂的力量。适合中级健身者，预计持续时间为60分钟。", "estimated_duration": 60, "scheduled_date": "2025-05-06T00:00:00+08:00", "status": "completed", "actual_duration": 22, "net_duration": null, "start_time": null, "end_time": "2025-05-27T10:28:43.230623+08:00", "last_status_change": "2025-05-07T11:24:25.433616+08:00", "created_at": "2025-04-29T23:20:09.967670+08:00", "updated_at": "2025-05-27T18:28:43.225789+08:00", "workout_exercises": [{"id": 195, "workout_id": 23, "exercise_id": 8, "exercise_name": "下斜卧推", "exercise_image": "/data/exercises/images/Decline-Bench-Pressa2c4f77ce69e.png", "exercise_description": null, "video_url": "/data/exercises/videos/Decline-Bench-Pressa2c4f77ce69e.mp4", "sets": 3, "reps": "12,12,12", "rest_seconds": 60, "order": 1, "notes": null, "exercise_type": "weight_reps", "superset_group": null, "weight": null, "set_records": [{"id": 642, "workout_exercise_id": 195, "set_number": 1, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-26T19:50:37.031592+08:00", "updated_at": "2025-05-26T19:50:37.031592+08:00"}, {"id": 643, "workout_exercise_id": 195, "set_number": 2, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-26T19:50:37.031592+08:00", "updated_at": "2025-05-26T19:50:37.031592+08:00"}, {"id": 644, "workout_exercise_id": 195, "set_number": 3, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-26T19:50:37.031592+08:00", "updated_at": "2025-05-26T19:50:37.031592+08:00"}]}, {"id": 218, "workout_id": 23, "exercise_id": 20, "exercise_name": "上斜卧推", "exercise_image": "/data/exercises/images/Incline-Bench-Press1e85fcebe9a4.png", "exercise_description": null, "video_url": "/data/exercises/videos/Incline-Bench-Press1e85fcebe9a4.mp4", "sets": 4, "reps": "12,12,12,12", "rest_seconds": 60, "order": 2, "notes": null, "exercise_type": "weight_reps", "superset_group": null, "weight": null, "set_records": [{"id": 689, "workout_exercise_id": 218, "set_number": 1, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-27T18:28:43.225789+08:00", "updated_at": "2025-05-27T18:28:43.225789+08:00"}, {"id": 690, "workout_exercise_id": 218, "set_number": 2, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-27T18:28:43.225789+08:00", "updated_at": "2025-05-27T18:28:43.225789+08:00"}, {"id": 691, "workout_exercise_id": 218, "set_number": 3, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-27T18:28:43.225789+08:00", "updated_at": "2025-05-27T18:28:43.225789+08:00"}, {"id": 692, "workout_exercise_id": 218, "set_number": 4, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-27T18:28:43.225789+08:00", "updated_at": "2025-05-27T18:28:43.225789+08:00"}]}]}, "images": [{"id": 2, "url": "http://tmp/YO5HpH1w9BfH6b748bd1dc8897e476c76c5328da0ade.png", "title": null, "description": null, "type": "image", "created_at": "2025-05-27T10:28:43.274120", "updated_at": "2025-05-27T10:28:43.274127"}, {"id": 3, "url": "http://tmp/sHI5fsqPs7RQ7d6e0a92d3f5dfbdfea2e5ebf9aea800.png", "title": null, "description": null, "type": "image", "created_at": "2025-05-27T10:28:43.274131", "updated_at": "2025-05-27T10:28:43.274134"}], "comments_summary": [], "tags": ["训练记录", "胸部"], "view_count": 26}, {"created_at": "2025-05-26T11:50:37.085728", "updated_at": "2025-06-02T13:26:59.492059", "title": "今天练胸", "content": "不错", "image_urls": null, "related_workout_id": 11, "id": 5, "user_id": 1, "like_count": 0, "comment_count": 0, "share_count": 0, "status": "ACTIVE", "reported_count": 0, "user": {"id": 1, "nickname": "shell", "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132", "gender": 1, "age": 29, "weight": null, "height": null, "activity_level": 3, "bmi": null, "tedd": null, "completed": false, "country": null, "province": null, "city": null, "experience_level": null, "fitness_goal": null, "health_conditions": null, "allergies": null, "created_at": null, "birthday": null}, "is_liked_by_current_user": false, "related_workout": {"id": 11, "name": "今天练胸", "title": "今天练胸", "date": "2025-05-26T11:50:37.062200"}, "related_workout_detail": {"id": 23, "name": "练胸", "training_plan_id": 24, "day_of_week": null, "day_number": 1, "description": "本训练计划旨在通过使用自重和杠铃来增强手臂的力量。适合中级健身者，预计持续时间为60分钟。", "estimated_duration": 60, "scheduled_date": "2025-05-06T00:00:00+08:00", "status": "completed", "actual_duration": 22, "net_duration": null, "start_time": null, "end_time": "2025-05-27T10:28:43.230623+08:00", "last_status_change": "2025-05-07T11:24:25.433616+08:00", "created_at": "2025-04-29T23:20:09.967670+08:00", "updated_at": "2025-05-27T18:28:43.225789+08:00", "workout_exercises": [{"id": 195, "workout_id": 23, "exercise_id": 8, "exercise_name": "下斜卧推", "exercise_image": "/data/exercises/images/Decline-Bench-Pressa2c4f77ce69e.png", "exercise_description": null, "video_url": "/data/exercises/videos/Decline-Bench-Pressa2c4f77ce69e.mp4", "sets": 3, "reps": "12,12,12", "rest_seconds": 60, "order": 1, "notes": null, "exercise_type": "weight_reps", "superset_group": null, "weight": null, "set_records": [{"id": 642, "workout_exercise_id": 195, "set_number": 1, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-26T19:50:37.031592+08:00", "updated_at": "2025-05-26T19:50:37.031592+08:00"}, {"id": 643, "workout_exercise_id": 195, "set_number": 2, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-26T19:50:37.031592+08:00", "updated_at": "2025-05-26T19:50:37.031592+08:00"}, {"id": 644, "workout_exercise_id": 195, "set_number": 3, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-26T19:50:37.031592+08:00", "updated_at": "2025-05-26T19:50:37.031592+08:00"}]}, {"id": 218, "workout_id": 23, "exercise_id": 20, "exercise_name": "上斜卧推", "exercise_image": "/data/exercises/images/Incline-Bench-Press1e85fcebe9a4.png", "exercise_description": null, "video_url": "/data/exercises/videos/Incline-Bench-Press1e85fcebe9a4.mp4", "sets": 4, "reps": "12,12,12,12", "rest_seconds": 60, "order": 2, "notes": null, "exercise_type": "weight_reps", "superset_group": null, "weight": null, "set_records": [{"id": 689, "workout_exercise_id": 218, "set_number": 1, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-27T18:28:43.225789+08:00", "updated_at": "2025-05-27T18:28:43.225789+08:00"}, {"id": 690, "workout_exercise_id": 218, "set_number": 2, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-27T18:28:43.225789+08:00", "updated_at": "2025-05-27T18:28:43.225789+08:00"}, {"id": 691, "workout_exercise_id": 218, "set_number": 3, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-27T18:28:43.225789+08:00", "updated_at": "2025-05-27T18:28:43.225789+08:00"}, {"id": 692, "workout_exercise_id": 218, "set_number": 4, "set_type": "normal", "weight": 20, "reps": 12, "completed": true, "notes": "", "created_at": "2025-05-27T18:28:43.225789+08:00", "updated_at": "2025-05-27T18:28:43.225789+08:00"}]}]}, "images": [{"id": 1, "url": "http://tmp/zJ4gYybiEcmW6b748bd1dc8897e476c76c5328da0ade.png", "title": null, "description": null, "type": "image", "created_at": "2025-05-26T11:50:37.091250", "updated_at": "2025-05-26T11:50:37.091255"}], "comments_summary": [], "tags": ["训练记录", "胸部"], "view_count": 9}, {"created_at": "2025-05-26T10:51:16.075039", "updated_at": "2025-05-27T06:26:03.652822", "title": "测试训练记录 - 2025-05-26 18:51:16", "content": "直接通过服务测试创建的训练记录", "image_urls": null, "related_workout_id": 10, "id": 4, "user_id": 1, "like_count": 0, "comment_count": 0, "share_count": 0, "status": "ACTIVE", "reported_count": 0, "user": {"id": 1, "nickname": "shell", "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132", "gender": 1, "age": 29, "weight": null, "height": null, "activity_level": 3, "bmi": null, "tedd": null, "completed": false, "country": null, "province": null, "city": null, "experience_level": null, "fitness_goal": null, "health_conditions": null, "allergies": null, "created_at": null, "birthday": null}, "is_liked_by_current_user": false, "related_workout": {"id": 10, "name": "测试训练记录", "title": "测试训练记录 - 2025-05-26 18:51:16", "date": "2025-05-26T10:51:16.049687"}, "related_workout_detail": {"id": 53, "name": "测试训练记录 - 2025-05-26 18:51:16", "training_plan_id": 1, "day_of_week": null, "day_number": 1, "description": null, "estimated_duration": null, "scheduled_date": null, "status": "completed", "actual_duration": 30, "net_duration": null, "start_time": "2025-05-26T10:51:16.039132+08:00", "end_time": "2025-05-26T10:51:16.039135+08:00", "last_status_change": null, "created_at": "2025-05-26T18:51:16.036930+08:00", "updated_at": "2025-05-26T18:51:16.036930+08:00", "workout_exercises": [{"id": 194, "workout_id": 53, "exercise_id": null, "exercise_name": "动作ID: None", "exercise_image": null, "exercise_description": null, "video_url": null, "sets": 2, "reps": "12,10", "rest_seconds": 60, "order": 1, "notes": null, "exercise_type": "weight_reps", "superset_group": null, "weight": null, "set_records": [{"id": 640, "workout_exercise_id": 194, "set_number": 1, "set_type": "normal", "weight": 60, "reps": 12, "completed": true, "notes": null, "created_at": "2025-05-26T18:51:16.036930+08:00", "updated_at": "2025-05-26T18:51:16.036930+08:00"}, {"id": 641, "workout_exercise_id": 194, "set_number": 2, "set_type": "normal", "weight": 70, "reps": 10, "completed": true, "notes": null, "created_at": "2025-05-26T18:51:16.036930+08:00", "updated_at": "2025-05-26T18:51:16.036930+08:00"}]}]}, "images": [], "comments_summary": [], "tags": ["测试", "直接API", "训练"], "view_count": 0}, {"created_at": "2025-05-26T10:46:12.306546", "updated_at": "2025-05-26T14:22:25.237718", "title": "测试训练记录 - 2025-05-26 18:46:12", "content": "这是一个通过API测试创建的训练记录", "image_urls": null, "related_workout_id": null, "id": 3, "user_id": 1, "like_count": 0, "comment_count": 0, "share_count": 0, "status": "ACTIVE", "reported_count": 0, "user": {"id": 1, "nickname": "shell", "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132", "gender": 1, "age": 29, "weight": null, "height": null, "activity_level": 3, "bmi": null, "tedd": null, "completed": false, "country": null, "province": null, "city": null, "experience_level": null, "fitness_goal": null, "health_conditions": null, "allergies": null, "created_at": null, "birthday": null}, "is_liked_by_current_user": false, "related_workout": null, "related_workout_detail": null, "images": [], "comments_summary": [], "tags": ["测试", "API", "训练记录"], "view_count": 1}, {"created_at": "2025-05-26T10:35:23.713058", "updated_at": "2025-05-26T14:22:06.429122", "title": "测试训练记录 - 2025-05-26 18:35:23", "content": "这是一个通过API测试创建的训练记录", "image_urls": null, "related_workout_id": null, "id": 2, "user_id": 1, "like_count": 0, "comment_count": 0, "share_count": 0, "status": "ACTIVE", "reported_count": 0, "user": {"id": 1, "nickname": "shell", "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132", "gender": 1, "age": 29, "weight": null, "height": null, "activity_level": 3, "bmi": null, "tedd": null, "completed": false, "country": null, "province": null, "city": null, "experience_level": null, "fitness_goal": null, "health_conditions": null, "allergies": null, "created_at": null, "birthday": null}, "is_liked_by_current_user": false, "related_workout": null, "related_workout_detail": null, "images": [], "comments_summary": [], "tags": ["测试", "API", "训练记录"], "view_count": 1}, {"created_at": "2025-05-26T10:28:10.258539", "updated_at": "2025-05-26T14:20:51.793648", "title": "测试力量训练记录", "content": "今天完成了深蹲和卧推训练，感觉很不错！", "image_urls": null, "related_workout_id": null, "id": 1, "user_id": 1, "like_count": 0, "comment_count": 0, "share_count": 0, "status": "ACTIVE", "reported_count": 0, "user": {"id": 1, "nickname": "shell", "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132", "gender": 1, "age": 29, "weight": null, "height": null, "activity_level": 3, "bmi": null, "tedd": null, "completed": false, "country": null, "province": null, "city": null, "experience_level": null, "fitness_goal": null, "health_conditions": null, "allergies": null, "created_at": null, "birthday": null}, "is_liked_by_current_user": false, "related_workout": null, "related_workout_detail": null, "images": [], "comments_summary": [], "tags": ["力量训练", "腿部", "胸部"], "view_count": 1}]}, "analysis": {"dataType": "object", "isArray": false, "structure": {"total": "number", "items": {"type": "array", "length": 7, "itemType": "object"}}, "samples": {"firstItem": {"created_at": "2025-06-04T07:06:43.964382", "updated_at": "2025-06-04T07:06:43.964388", "title": "训练记录 - 2025-06-04 cdy15s", "content": "15s", "image_urls": null, "related_workout_id": 18, "id": 7, "user_id": 2, "like_count": 0, "comment_count": 0, "share_count": 0, "status": "ACTIVE", "reported_count": 0, "user": {"id": 2, "nickname": "微信用户", "avatar_url": "https://example.com/default-avatar.jpg", "gender": 0, "age": null, "weight": null, "height": null, "activity_level": 3, "bmi": null, "tedd": null, "completed": false, "country": null, "province": null, "city": null, "experience_level": null, "fitness_goal": null, "health_conditions": null, "allergies": null, "created_at": null, "birthday": null}, "is_liked_by_current_user": false, "related_workout": {"id": 18, "name": "训练记录 - 2025-06-04 cdy15s", "title": "训练记录 - 2025-06-04 cdy15s", "date": "2025-06-04T07:06:43.910137"}, "related_workout_detail": {"id": 67, "name": "训练记录 - 2025-06-04 cdy15s", "training_plan_id": 1, "day_of_week": null, "day_number": 1, "description": null, "estimated_duration": null, "scheduled_date": null, "status": "completed", "actual_duration": 0, "net_duration": null, "start_time": "2025-06-04T07:06:43.893097+08:00", "end_time": "2025-06-04T07:06:43.893103+08:00", "last_status_change": null, "created_at": "2025-06-04T15:06:43.891842+08:00", "updated_at": "2025-06-04T15:06:43.891842+08:00", "workout_exercises": [{"id": 257, "workout_id": 67, "exercise_id": 252, "exercise_name": "史密斯反握推举", "exercise_image": "/data/exercises/images/<PERSON>-<PERSON>erse-grip-Press9cbc4d7d0410.png", "exercise_description": null, "video_url": "", "sets": 1, "reps": "12", "rest_seconds": 60, "order": 1, "notes": null, "exercise_type": "weight_reps", "superset_group": null, "weight": null, "set_records": [{"id": 769, "workout_exercise_id": 257, "set_number": 1, "set_type": "normal", "weight": 20, "reps": 12, "completed": false, "notes": "", "created_at": "2025-06-04T15:06:43.891842+08:00", "updated_at": "2025-06-04T15:06:43.891842+08:00"}]}, {"id": 258, "workout_id": 67, "exercise_id": 8, "exercise_name": "下斜卧推", "exercise_image": "/data/exercises/images/Decline-Bench-Pressa2c4f77ce69e.png", "exercise_description": null, "video_url": "/data/exercises/videos/Decline-Bench-Pressa2c4f77ce69e.mp4", "sets": 4, "reps": "12,12,12,12", "rest_seconds": 60, "order": 2, "notes": null, "exercise_type": "weight_reps", "superset_group": null, "weight": null, "set_records": [{"id": 770, "workout_exercise_id": 258, "set_number": 1, "set_type": "normal", "weight": 20, "reps": 12, "completed": false, "notes": "", "created_at": "2025-06-04T15:06:43.891842+08:00", "updated_at": "2025-06-04T15:06:43.891842+08:00"}, {"id": 771, "workout_exercise_id": 258, "set_number": 2, "set_type": "normal", "weight": 20, "reps": 12, "completed": false, "notes": "", "created_at": "2025-06-04T15:06:43.891842+08:00", "updated_at": "2025-06-04T15:06:43.891842+08:00"}, {"id": 772, "workout_exercise_id": 258, "set_number": 3, "set_type": "normal", "weight": 20, "reps": 12, "completed": false, "notes": "", "created_at": "2025-06-04T15:06:43.891842+08:00", "updated_at": "2025-06-04T15:06:43.891842+08:00"}, {"id": 773, "workout_exercise_id": 258, "set_number": 4, "set_type": "normal", "weight": 20, "reps": 12, "completed": false, "notes": "", "created_at": "2025-06-04T15:06:43.891842+08:00", "updated_at": "2025-06-04T15:06:43.891842+08:00"}]}]}, "images": [], "comments_summary": [], "tags": ["训练记录", "胸部"], "view_count": 0}}, "statistics": {"totalItems": 7, "hasWorkoutData": 0, "hasImages": 0, "hasComments": 0}}}