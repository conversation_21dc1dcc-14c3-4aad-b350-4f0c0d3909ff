# Hevy 健身应用 - 设计系统规范

## 📋 概述

本设计系统基于对 Hevy 健身应用的深度视觉分析，提供完整的UI/UX设计规范，确保在React项目中复现高保真的用户界面。

## 🎨 色彩系统

### 主色调
```css
/* 主品牌色 - 深蓝紫色 */
--primary-900: #1a1a2e;       /* 深度背景色 */
--primary-800: #16213e;       /* 主要背景色 */
--primary-700: #0f172a;       /* 侧边栏背景 */
--primary-600: #1e293b;       /* 卡片背景 */
--primary-500: #334155;       /* 边框色 */

/* 强调色 - 蓝色系 */
--accent-500: #3b82f6;        /* 主要蓝色 */
--accent-400: #60a5fa;        /* 悬停蓝色 */
--accent-300: #93c5fd;        /* 浅蓝色 */
--accent-200: #dbeafe;        /* 极浅蓝色 */

/* 成功色 - 绿色系 */
--success-500: #22c55e;       /* 成功绿色 */
--success-400: #4ade80;       /* 浅成功绿色 */
--success-300: #86efac;       /* 极浅成功绿色 */

/* 警告色 - 橙色系 */
--warning-500: #f59e0b;       /* 警告橙色 */
--warning-400: #fbbf24;       /* 浅警告橙色 */

/* 错误色 - 红色系 */
--error-500: #ef4444;         /* 错误红色 */
--error-400: #f87171;         /* 浅错误红色 */
```

### 文本色彩
```css
/* 文本颜色 */
--text-primary: #f8fafc;      /* 主要文本 - 白色 */
--text-secondary: #cbd5e1;    /* 次要文本 - 灰白色 */
--text-tertiary: #94a3b8;     /* 第三级文本 - 灰色 */
--text-disabled: #64748b;     /* 禁用文本 - 深灰色 */
--text-on-primary: #ffffff;   /* 主色上的文本 */
--text-on-accent: #ffffff;    /* 强调色上的文本 */
```

### 背景色彩
```css
/* 背景颜色 */
--bg-primary: #0f172a;        /* 主背景 */
--bg-secondary: #1e293b;      /* 次要背景 */
--bg-tertiary: #334155;       /* 第三级背景 */
--bg-surface: #1e293b;        /* 表面背景 */
--bg-overlay: rgba(15, 23, 42, 0.8); /* 遮罩背景 */
```

## 🔤 字体系统

### 字体族
```css
/* 主要字体 - 现代无衬线字体 */
--font-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;

/* 数字字体 - 等宽字体用于数据显示 */
--font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

/* 装饰字体 - 用于品牌和标题 */
--font-display: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
```

### 字体大小
```css
/* 字体大小系统 - 使用rem单位 */
--text-xs: 0.75rem;    /* 12px - 小标签 */
--text-sm: 0.875rem;   /* 14px - 次要文本 */
--text-base: 1rem;     /* 16px - 基础文本 */
--text-lg: 1.125rem;   /* 18px - 大文本 */
--text-xl: 1.25rem;    /* 20px - 标题 */
--text-2xl: 1.5rem;    /* 24px - 大标题 */
--text-3xl: 1.875rem;  /* 30px - 主标题 */
--text-4xl: 2.25rem;   /* 36px - 超大标题 */
```

### 字体粗细
```css
/* 字体粗细 */
--font-light: 300;     /* 细体 */
--font-normal: 400;    /* 正常 */
--font-medium: 500;    /* 中等 */
--font-semibold: 600;  /* 半粗体 */
--font-bold: 700;      /* 粗体 */
--font-extrabold: 800; /* 超粗体 */
```

## 📏 间距系统

### 间距单位 (基于8px网格)
```css
/* 间距系统 - 8px基础单位 */
--space-1: 0.25rem;    /* 4px */
--space-2: 0.5rem;     /* 8px */
--space-3: 0.75rem;    /* 12px */
--space-4: 1rem;       /* 16px */
--space-5: 1.25rem;    /* 20px */
--space-6: 1.5rem;     /* 24px */
--space-8: 2rem;       /* 32px */
--space-10: 2.5rem;    /* 40px */
--space-12: 3rem;      /* 48px */
--space-16: 4rem;      /* 64px */
--space-20: 5rem;      /* 80px */
--space-24: 6rem;      /* 96px */
```

### 容器尺寸
```css
/* 容器最大宽度 */
--container-xs: 20rem;    /* 320px */
--container-sm: 24rem;    /* 384px */
--container-md: 28rem;    /* 448px */
--container-lg: 32rem;    /* 512px */
--container-xl: 36rem;    /* 576px */
--container-2xl: 42rem;   /* 672px */
--container-full: 100%;   /* 全宽 */
```

## 🔲 组件样式

### 按钮样式
```css
/* 主要按钮 */
.btn-primary {
  background-color: var(--accent-500);
  color: var(--text-on-accent);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  font-size: var(--text-base);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--accent-400);
  transform: translateY(-1px);
}

/* 次要按钮 */
.btn-secondary {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--primary-500);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  font-size: var(--text-base);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--primary-600);
  color: var(--text-primary);
}
```

### 卡片样式
```css
.card {
  background-color: var(--bg-surface);
  border: 1px solid var(--primary-500);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--primary-500);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.card-subtitle {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: var(--space-1) 0 0 0;
}
```

### 导航样式
```css
.sidebar {
  background-color: var(--primary-700);
  width: 16rem; /* 256px */
  height: 100vh;
  border-right: 1px solid var(--primary-500);
  padding: var(--space-6);
}

.nav-item {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  margin-bottom: var(--space-2);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
}

.nav-item:hover {
  background-color: var(--primary-600);
  color: var(--text-primary);
}

.nav-item.active {
  background-color: var(--accent-500);
  color: var(--text-on-accent);
}

.nav-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: var(--space-3);
}
```

## 🎯 圆角系统
```css
/* 圆角尺寸 */
--radius-none: 0;
--radius-sm: 0.125rem;    /* 2px */
--radius-md: 0.375rem;    /* 6px */
--radius-lg: 0.5rem;      /* 8px */
--radius-xl: 0.75rem;     /* 12px */
--radius-2xl: 1rem;       /* 16px */
--radius-full: 9999px;    /* 完全圆形 */
```

## 🌊 阴影系统
```css
/* 阴影效果 */
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
--shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
```

## 🔄 动画和过渡
```css
/* 过渡时间 */
--transition-fast: 0.15s;
--transition-normal: 0.2s;
--transition-slow: 0.3s;

/* 缓动函数 */
--ease-in: cubic-bezier(0.4, 0, 1, 1);
--ease-out: cubic-bezier(0, 0, 0.2, 1);
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

/* 常用过渡 */
.transition-all {
  transition: all var(--transition-normal) var(--ease-in-out);
}

.transition-colors {
  transition: color var(--transition-normal) var(--ease-in-out),
              background-color var(--transition-normal) var(--ease-in-out),
              border-color var(--transition-normal) var(--ease-in-out);
}

.transition-transform {
  transition: transform var(--transition-normal) var(--ease-in-out);
}
```

## 🏃‍♂️ 健身特定组件

### 进度环
```css
.progress-ring {
  width: 120px;
  height: 120px;
  transform: rotate(-90deg);
}

.progress-ring-circle {
  fill: none;
  stroke-width: 8;
  stroke-linecap: round;
}

.progress-ring-background {
  stroke: var(--primary-600);
}

.progress-ring-progress {
  stroke: var(--accent-500);
  stroke-dasharray: 283; /* 2 * π * 45 */
  stroke-dashoffset: 283;
  transition: stroke-dashoffset 0.5s ease;
}

/* 运动数据显示 */
.metric-card {
  background: linear-gradient(135deg, var(--bg-surface) 0%, var(--primary-600) 100%);
  border: 1px solid var(--primary-500);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  text-align: center;
}

.metric-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  font-family: var(--font-mono);
}

.metric-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-top: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
```

### 训练计划卡片
```css
.workout-card {
  background-color: var(--bg-surface);
  border: 1px solid var(--primary-500);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  cursor: pointer;
  transition: all var(--transition-normal) var(--ease-in-out);
}

.workout-card:hover {
  border-color: var(--accent-500);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.workout-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.workout-exercises {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  margin-bottom: var(--space-4);
}

.workout-duration {
  display: inline-flex;
  align-items: center;
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  font-family: var(--font-mono);
}
```

## 📱 响应式断点
```css
/* 响应式断点 */
--breakpoint-sm: 640px;   /* 手机横屏 */
--breakpoint-md: 768px;   /* 平板 */
--breakpoint-lg: 1024px;  /* 桌面 */
--breakpoint-xl: 1280px;  /* 大桌面 */
--breakpoint-2xl: 1536px; /* 超大桌面 */

/* 响应式侧边栏 */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: var(--space-4);
    border-right: none;
    border-top: 1px solid var(--primary-500);
  }
  
  .nav-item {
    flex-direction: column;
    text-align: center;
    padding: var(--space-2);
  }
  
  .nav-icon {
    margin-right: 0;
    margin-bottom: var(--space-1);
  }
}
```

## 🎨 图标系统

### 图标规范
- **尺寸**: 16px, 20px, 24px, 32px
- **描边宽度**: 1.5px (一般), 2px (强调)
- **风格**: 线性图标，圆角端点
- **颜色**: 继承父元素颜色

### 常用图标
```css
.icon {
  width: 1.25rem;
  height: 1.25rem;
  stroke: currentColor;
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.icon-sm { width: 1rem; height: 1rem; }
.icon-lg { width: 1.5rem; height: 1.5rem; }
.icon-xl { width: 2rem; height: 2rem; }
```

## 📋 表单样式
```css
.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.form-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background-color: var(--bg-surface);
  border: 1px solid var(--primary-500);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: var(--text-base);
  transition: border-color var(--transition-normal) var(--ease-in-out);
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder {
  color: var(--text-tertiary);
}
```

## 🌟 特殊效果

### 渐变效果
```css
/* 品牌渐变 */
.gradient-primary {
  background: linear-gradient(135deg, var(--accent-500) 0%, var(--accent-400) 100%);
}

.gradient-surface {
  background: linear-gradient(135deg, var(--bg-surface) 0%, var(--primary-600) 100%);
}

/* 成功渐变 */
.gradient-success {
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-400) 100%);
}
```

### 玻璃态效果
```css
.glass-effect {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
```

## 📊 数据可视化

### 图表颜色
```css
/* 图表配色方案 */
--chart-primary: var(--accent-500);
--chart-secondary: var(--success-500);
--chart-tertiary: var(--warning-500);
--chart-quaternary: var(--error-500);
--chart-grid: var(--primary-500);
--chart-text: var(--text-secondary);
```

---

## 🎯 使用指南

### Tailwind CSS 集成
本设计系统与 Tailwind CSS 完全兼容，可以通过 CSS 变量或 Tailwind 配置文件进行集成。

### React 组件示例
```jsx
// 示例：使用设计系统的按钮组件
const Button = ({ variant = 'primary', children, ...props }) => {
  const className = variant === 'primary' ? 'btn-primary' : 'btn-secondary';
  return (
    <button className={className} {...props}>
      {children}
    </button>
  );
};
```

### 可访问性
- 所有颜色组合均符合 WCAG 2.1 AA 对比度标准
- 支持深色模式
- 键盘导航友好
- 屏幕阅读器兼容

---

**最后更新**: 2025-01-17  
**版本**: 1.0.0  
**基于**: Hevy健身应用视觉分析 