# FitMaster 人体肌肉模组实现文档

## 🏗️ 架构概述

人体肌肉模组(`MuscleVisualization`)是基于 SVG 的交互式肌肉可视化系统，支持前后视图切换、肌肉群选择和标签展示功能。

### 核心组件架构

```
MuscleVisualization/
├── MuscleIllustration.tsx          # 主要的SVG肌肉图组件
├── LabeledMuscleIllustration.tsx   # 带标签的肌肉图组件
├── MuscleGroups/                   # 肌肉群组件目录
│   ├── ChestGroup.tsx             # 胸肌群
│   ├── BackGroup.tsx              # 背肌群
│   ├── BicepsGroup.tsx            # 二头肌群
│   ├── TricepsGroup.tsx           # 三头肌群
│   ├── ShouldersGroup.tsx         # 肩部肌群
│   ├── QuadricepsGroup.tsx        # 股四头肌群
│   ├── HamstringsGroup.tsx        # 腘绳肌群
│   ├── GlutesGroup.tsx            # 臀肌群
│   ├── CalvesGroup.tsx            # 小腿肌群
│   ├── ForearmsGroup.tsx          # 前臂肌群
│   ├── AbsGroup.tsx               # 腹肌群
│   ├── ObliquesGroup.tsx          # 腹斜肌群
│   ├── TrapsGroup.tsx             # 斜方肌群
│   ├── types.ts                   # 肌肉群组件类型定义
│   └── index.ts                   # 导出文件
├── MuscleIllustration.scss         # 主要样式文件
└── LabeledMuscleIllustration.scss  # 标签样式文件
```

## 📊 技术架构

### 1. 基础技术栈

- **前端框架**: React 18 + TypeScript
- **图形技术**: SVG (可伸缩矢量图形) 
- **样式系统**: SCSS + CSS变量
- **状态管理**: React Context + useReducer
- **性能优化**: iOS硬件加速、Intersection Observer

### 2. SVG 架构设计

#### 视图尺寸定义
```typescript
// 主SVG视图盒子
viewBox="0 0 535 462"  // 宽:535px 高:462px

// 前视图区域: x=0-267 (左半部分)
// 后视图区域: x=268-535 (右半部分)
```

#### 层级结构
```xml
<svg>
  <defs>
    <!-- 样式定义和渐变 -->
  </defs>
  
  <!-- 人体轮廓路径 -->
  <path className="body-outline" />
  
  <!-- 肌肉群组件 -->
  <ChestGroup />
  <BackGroup />
  <!-- ... 其他肌肉群 -->
</svg>
```

### 3. 肌肉群组件设计模式

每个肌肉群组件遵循统一的设计模式：

```typescript
// 通用接口定义
interface MuscleGroupComponentProps {
  onToggleMuscle: (muscle: MuscleGroupEnum) => void;
  getMuscleClasses: (muscle: MuscleGroupEnum) => string;
}

// 组件结构
export function ChestGroup({ onToggleMuscle, getMuscleClasses }: MuscleGroupComponentProps) {
  return (
    <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroupEnum.CHEST)}>
      {/* 透明点击区域 */}
      <path className="fill-transparent" d="..." data-elem={MuscleGroupEnum.CHEST} />
      
      {/* 可见肌肉形状 */}
      <path 
        className={getMuscleClasses(MuscleGroupEnum.CHEST)}
        d="..." 
        data-elem={MuscleGroupEnum.CHEST}
      />
    </g>
  );
}
```

## 🎨 样式系统

### 1. CSS 变量系统

```scss
:root {
  // 基础色彩
  --muscle-default: #94a3b8;    // 默认灰色
  --muscle-selected: #3b82f6;   // 选中蓝色
  --muscle-hover: #93c5fd;      // 悬停浅蓝色
  
  // iOS 专用
  --ios-touch-target: 44px;     // Apple HIG最小触摸目标
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
}
```

### 2. 肌肉状态样式

```scss
.muscle-group {
  cursor: pointer;
  transition: all 0.1s ease-out;
}

.muscle-default {
  fill: var(--muscle-default);
}

.muscle-selected {
  fill: var(--muscle-selected);
}

.muscle-group:hover .muscle-default {
  fill: var(--muscle-hover);
}
```

### 3. iOS 优化样式

```scss
.muscle-illustration {
  // iOS硬件加速
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000;
  
  // iOS触摸优化
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
}
```

## 🔧 性能优化策略

### 1. iOS 性能优化Hook

```typescript
const useIOSPerformanceOptimization = () => {
  const [shouldRender, setShouldRender] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);
  
  // Intersection Observer 懒加载
  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !shouldRender) {
          setTimeout(() => setShouldRender(true), 100);
        }
      },
      { rootMargin: '50px', threshold: 0.1 }
    );
  }, []);
  
  return { elementRef, shouldRender };
};
```

### 2. 硬件加速检测

```typescript
const shouldUseHardwareAcceleration = (): boolean => {
  // 检测是否为iOS设备
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  
  // 检测是否支持硬件加速
  const supportsHardwareAccel = 'webkitTransform' in document.body.style;
  
  return isIOS && supportsHardwareAccel;
};
```

### 3. SVG 优化设置

```typescript
<svg
  shapeRendering="optimizeSpeed"      // 优化渲染速度
  imageRendering="optimizeSpeed"      // 优化图像渲染
  vectorEffect="non-scaling-stroke"   // 防止描边缩放
>
```

## 🎯 肌肉群枚举系统

### 1. 枚举定义

```typescript
export enum MuscleGroupEnum {
  // 前视图肌肉群
  BICEPS = "BICEPS",
  CHEST = "CHEST", 
  SHOULDERS_FRONT = "SHOULDERS_FRONT",
  QUADRICEPS = "QUADRICEPS",
  ABDOMINALS = "ABDOMINALS",
  OBLIQUES = "OBLIQUES",
  FOREARMS_FRONT = "FOREARMS_FRONT",
  CALVES_FRONT = "CALVES_FRONT",
  
  // 后视图肌肉群
  TRICEPS = "TRICEPS",
  BACK = "BACK",
  SHOULDERS_BACK = "SHOULDERS_BACK", 
  HAMSTRINGS = "HAMSTRINGS",
  GLUTES = "GLUTES",
  TRAPS = "TRAPS",
  LATS = "LATS",
  LOWER_BACK = "LOWER_BACK",
  FOREARMS_BACK = "FOREARMS_BACK",
  CALVES_BACK = "CALVES_BACK"
}
```

### 2. 中文名称映射

```typescript
export const MUSCLE_NAMES: Record<MuscleGroupEnum, string> = {
  [MuscleGroupEnum.BICEPS]: "二头肌",
  [MuscleGroupEnum.CHEST]: "胸肌",
  [MuscleGroupEnum.SHOULDERS_FRONT]: "前肩",
  [MuscleGroupEnum.QUADRICEPS]: "股四头肌",
  [MuscleGroupEnum.ABDOMINALS]: "腹肌",
  // ... 其他映射
};
```

## 📱 移动端适配

### 1. 响应式设计

```scss
@media (max-width: 768px) {
  .muscle-illustration {
    max-width: 95%;
  }
}

@media (max-width: 480px) {
  .muscle-illustration {
    max-width: 90%;
  }
}
```

### 2. 触摸交互优化

```scss
.muscle-group {
  min-height: var(--ios-touch-target);
  min-width: var(--ios-touch-target);
  
  // iOS触摸反馈
  &:active {
    transform: scale(0.98);
    transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  }
}
```

## 🚀 使用示例

### 基础使用

```typescript
import { MuscleIllustration } from './MuscleVisualization/MuscleIllustration';

function WorkoutPlan() {
  const [selectedMuscles, setSelectedMuscles] = useState<MuscleGroupEnum[]>([]);
  
  const handleToggleMuscle = (muscle: MuscleGroupEnum) => {
    setSelectedMuscles(prev => 
      prev.includes(muscle) 
        ? prev.filter(m => m !== muscle)
        : [...prev, muscle]
    );
  };
  
  return (
    <MuscleIllustration
      selectedMuscles={selectedMuscles}
      onToggleMuscle={handleToggleMuscle}
      theme="light"
      isLoading={false}
    />
  );
}
```

### 带标签版本

```typescript
import { LabeledMuscleIllustration } from './MuscleVisualization/LabeledMuscleIllustration';

function ExerciseDemo() {
  const muscleColorConfig = {
    [MuscleGroupEnum.CHEST]: 'primary',
    [MuscleGroupEnum.TRICEPS]: 'secondary'
  };
  
  return (
    <LabeledMuscleIllustration
      selectedMuscles={[MuscleGroupEnum.CHEST, MuscleGroupEnum.TRICEPS]}
      muscleColorConfig={muscleColorConfig}
      theme="dark"
    />
  );
}
```

## 🔍 调试和测试

### 1. 开发者工具

```typescript
// 开发环境下的调试信息
if (process.env.NODE_ENV === 'development') {
  console.log('肌肉选择状态:', selectedMuscles);
  console.log('iOS硬件加速:', useHardwareAccel);
  console.log('当前主题:', theme);
}
```

### 2. 性能监控

```typescript
// iOS性能监控Hook
function useIOSPerformance() {
  useEffect(() => {
    if (Capacitor.getPlatform() === 'ios') {
      console.time('iOS组件渲染时间');
      return () => {
        console.timeEnd('iOS组件渲染时间');
      };
    }
  }, []);
}
```

## 📋 待改进项目

1. **标签系统完善**: 需要根据参考图像完善中文标签显示
2. **动画效果**: 添加肌肉选择的动画过渡效果
3. **可访问性**: 添加 ARIA 标签和键盘导航支持
4. **国际化**: 支持多语言肌肉名称
5. **数据持久化**: 保存用户的肌肉选择状态

## 🔧 技术债务

1. **muscle-selector-complete 集成**: 当前暂时移除，需要重新实现
2. **SVG 路径优化**: 某些肌肉群的SVG路径可以进一步简化
3. **类型安全**: 部分组件的类型定义可以更严格
4. **测试覆盖**: 缺少单元测试和集成测试

---

*本文档遵循 FitMaster iOS优先开发规范，确保所有功能在iOS设备上有最佳体验。* 