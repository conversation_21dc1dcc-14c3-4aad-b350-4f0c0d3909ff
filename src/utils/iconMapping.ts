/**
 * iconMapping.ts
 * 定义应用中使用的图标与Pixel Icon Library图标的映射关系
 */

export type IconName = 
  | 'dashboard' 
  | 'workout' 
  | 'profile'
  | 'feed'
  | 'routines'
  | 'exercises'
  | 'settings'
  | 'add'
  | 'remove'
  | 'edit'
  | 'delete'
  | 'search'
  | 'calendar'
  | 'heart'
  | 'water'
  | 'weight'
  | 'steps'
  | 'nutrition'
  | 'sleep'
  | 'check'
  | 'close'
  | 'menu'
  | 'arrow-left'
  | 'arrow-right'
  | 'arrow-up'
  | 'arrow-down'
  | 'chart'
  | 'refresh'
  | 'user'
  | 'users'
  | 'trophy'
  | 'star'
  | 'book'
  | 'bell'
  | 'notification'
  | 'sun'
  | 'moon';

/**
 * 图标映射对象
 * 将应用图标名称映射到Pixel Icon Library的类名
 */
export const iconMap: Record<IconName, string> = {
  // 主导航
  dashboard: 'hn-home',
  workout: 'hn-fire', 
  profile: 'hn-user',
  feed: 'hn-users',
  routines: 'hn-clipboard',
  exercises: 'hn-heart',
  settings: 'hn-cog',
  
  // 操作图标
  add: 'hn-plus',
  remove: 'hn-minus',
  edit: 'hn-edit',
  delete: 'hn-trash',
  search: 'hn-search',
  
  // 健身数据图标
  calendar: 'hn-calender', // 注意拼写是calender
  heart: 'hn-heart',
  water: 'hn-globe', // 使用globe替代water
  weight: 'hn-trophy', // 使用trophy替代weight
  steps: 'hn-plane', // 使用plane替代steps
  nutrition: 'hn-star', // 使用star替代nutrition
  sleep: 'hn-moon',
  
  // 通用UI图标
  check: 'hn-check',
  close: 'hn-times',
  menu: 'hn-bars',
  'arrow-left': 'hn-arrow-left',
  'arrow-right': 'hn-arrow-right',
  'arrow-up': 'hn-arrow-up',
  'arrow-down': 'hn-arrow-down',
  chart: 'hn-chart-line',
  refresh: 'hn-refresh',
  user: 'hn-user',
  users: 'hn-users',
  trophy: 'hn-trophy',
  star: 'hn-star',
  book: 'hn-bookmark',
  bell: 'hn-bell',
  notification: 'hn-bell',
  sun: 'hn-sun',
  moon: 'hn-moon'
};

/**
 * 获取图标类名
 * @param name 图标名称
 * @returns 对应的Pixel Icon Library类名
 */
export const getIconClass = (name: IconName): string => {
  return iconMap[name] || '';
};

/**
 * 图标组件
 * 简化后续在应用中使用图标的方法
 * 使用示例: <Icon name="workout" size="large" />
 */
export interface IconProps {
  name: IconName;
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  className?: string;
}; 