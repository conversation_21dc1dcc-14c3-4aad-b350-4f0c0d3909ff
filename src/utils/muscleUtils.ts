import { cn } from '../lib/utils';
import { MuscleGroupEnum, MuscleGroupColors } from '../types/muscle.types';
import { MUSCLE_CATEGORIES } from '../constants/exerciseCategories';

// 肌肉群中文显示名称映射
export const getMuscleDisplayName = (muscle: MuscleGroupEnum): string => {
  const muscleNames: Partial<Record<MuscleGroupEnum, string>> = {
    [MuscleGroupEnum.CHEST]: '胸部',
    [MuscleGroupEnum.BACK]: '背部', 
    [MuscleGroupEnum.SHOULDERS]: '肩部',
    [MuscleGroupEnum.SHOULDERS_FRONT]: '前肩',
    [MuscleGroupEnum.SHOULDERS_BACK]: '后肩',
    [MuscleGroupEnum.BICEPS]: '二头肌',
    [MuscleGroupEnum.TRICEPS]: '三头肌',
    [MuscleGroupEnum.FOREARMS]: '前臂',
    [MuscleGroupEnum.FOREARMS_FRONT]: '前臂前侧',
    [MuscleGroupEnum.FOREARMS_BACK]: '前臂后侧',
    [MuscleGroupEnum.ABDOMINALS]: '腹肌',
    [MuscleGroupEnum.QUADRICEPS]: '股四头肌',
    [MuscleGroupEnum.HAMSTRINGS]: '腘绳肌',
    [MuscleGroupEnum.CALVES]: '小腿',
    [MuscleGroupEnum.CALVES_FRONT]: '小腿前侧',
    [MuscleGroupEnum.CALVES_BACK]: '小腿后侧',
    [MuscleGroupEnum.GLUTES]: '臀部',
    [MuscleGroupEnum.TRAPS]: '斜方肌',
    [MuscleGroupEnum.LATS]: '背阔肌',
    [MuscleGroupEnum.LOWER_BACK]: '下背部',
    [MuscleGroupEnum.OBLIQUES]: '腹斜肌'
  };
  return muscleNames[muscle] || muscle;
};

// 肌肉群颜色配置 - iOS优化
export const muscleGroupColors: MuscleGroupColors = {
  selected: {
    light: '#3b82f6', // iOS蓝色
    dark: '#60a5fa'   // iOS蓝色浅色版
  },
  unselected: {
    light: '#9ca3af', // iOS灰色
    dark: '#6b7280'   // iOS灰色深色版
  },
  hover: {
    light: '#2563eb', // iOS蓝色深色版
    dark: '#93c5fd'   // iOS蓝色更浅版
  }
};

// 生成肌肉群SVG样式类 - iOS优化
export const getMuscleClasses = (
  muscle: MuscleGroupEnum,
  isSelected: boolean,
  theme: 'light' | 'dark' = 'light',
  interactive: boolean = true
): string => {
  const colors = muscleGroupColors;
  
  return cn(
    'muscle-group',
    `muscle-${muscle.toLowerCase()}`, // 使用muscle参数添加特定肌肉群类名
    interactive && 'cursor-pointer',
    'transition-all duration-200 ease-out',
    'ios-touch-target', // iOS触摸目标类
    // 基础填充色
    isSelected 
      ? `fill-[${colors.selected[theme]}]`
      : `fill-[${colors.unselected[theme]}]`,
    // 悬停效果
    interactive && [
      isSelected 
        ? `hover:fill-[${colors.hover[theme]}]`
        : `hover:fill-[${colors.selected[theme]}]`,
      'hover:opacity-80'
    ],
    // iOS触摸反馈
    interactive && 'active:scale-[0.98]',
    // 选中状态特效
    isSelected && [
      'filter brightness-110 saturate-130',
      'drop-shadow-sm'
    ],
    // 暗色主题适配
    theme === 'dark' && [
      'dark:stroke-slate-300',
      isSelected && 'dark:filter dark:brightness-125'
    ]
  );
};

// 将字符串肌肉名转换为枚举 - 支持多种表达方式
export const convertStringToMuscleEnum = (muscleNames: string[]): MuscleGroupEnum[] => {
  const muscleMap: Record<string, MuscleGroupEnum> = {
    // 胸部相关
    '胸大肌': MuscleGroupEnum.CHEST,
    '胸部': MuscleGroupEnum.CHEST,
    '胸肌': MuscleGroupEnum.CHEST,
    
    // 背部相关
    '背阔肌': MuscleGroupEnum.LATS,
    '背部': MuscleGroupEnum.BACK,
    '后背': MuscleGroupEnum.BACK,
    '斜方肌': MuscleGroupEnum.TRAPS,
    '下背部': MuscleGroupEnum.LOWER_BACK,
    '下背': MuscleGroupEnum.LOWER_BACK,
    
    // 肩部相关
    '肩部': MuscleGroupEnum.SHOULDERS,
    '三角肌': MuscleGroupEnum.SHOULDERS,
    '三角肌前束': MuscleGroupEnum.SHOULDERS,
    '三角肌中束': MuscleGroupEnum.SHOULDERS,
    '三角肌后束': MuscleGroupEnum.SHOULDERS,
    
    // 手臂相关
    '二头肌': MuscleGroupEnum.BICEPS,
    '肱二头肌': MuscleGroupEnum.BICEPS,
    '三头肌': MuscleGroupEnum.TRICEPS,
    '肱三头肌': MuscleGroupEnum.TRICEPS,
    '前臂': MuscleGroupEnum.FOREARMS,
    '前臂肌': MuscleGroupEnum.FOREARMS,
    
    // 腿部相关
    '股四头肌': MuscleGroupEnum.QUADRICEPS,
    '四头肌': MuscleGroupEnum.QUADRICEPS,
    '大腿前侧': MuscleGroupEnum.QUADRICEPS,
    '腘绳肌': MuscleGroupEnum.HAMSTRINGS,
    '大腿后侧': MuscleGroupEnum.HAMSTRINGS,
    '小腿': MuscleGroupEnum.CALVES,
    '小腿肌': MuscleGroupEnum.CALVES,
    '腓肠肌': MuscleGroupEnum.CALVES,
    
    // 臀部和核心
    '臀大肌': MuscleGroupEnum.GLUTES,
    '臀部': MuscleGroupEnum.GLUTES,
    '臀肌': MuscleGroupEnum.GLUTES,
    '腹肌': MuscleGroupEnum.ABDOMINALS,
    '核心': MuscleGroupEnum.ABDOMINALS,
    '腹部': MuscleGroupEnum.ABDOMINALS,
    '腹斜肌': MuscleGroupEnum.OBLIQUES,
    '侧腹': MuscleGroupEnum.OBLIQUES,
  };
  
  return muscleNames
    .map(name => muscleMap[name.trim()])
    .filter((muscle): muscle is MuscleGroupEnum => muscle !== undefined);
};

// 获取肌肉群在SVG中的位置信息 - 用于动画和定位
export const getMusclePosition = (muscle: MuscleGroupEnum): { x: number; y: number } => {
  const positions: Partial<Record<MuscleGroupEnum, { x: number; y: number }>> = {
    [MuscleGroupEnum.CHEST]: { x: 115, y: 110 },
    [MuscleGroupEnum.SHOULDERS]: { x: 90, y: 85 },
    [MuscleGroupEnum.SHOULDERS_FRONT]: { x: 85, y: 85 },
    [MuscleGroupEnum.SHOULDERS_BACK]: { x: 95, y: 85 },
    [MuscleGroupEnum.BICEPS]: { x: 70, y: 140 },
    [MuscleGroupEnum.TRICEPS]: { x: 150, y: 140 },
    [MuscleGroupEnum.FOREARMS]: { x: 60, y: 180 },
    [MuscleGroupEnum.FOREARMS_FRONT]: { x: 55, y: 180 },
    [MuscleGroupEnum.FOREARMS_BACK]: { x: 65, y: 180 },
    [MuscleGroupEnum.ABDOMINALS]: { x: 115, y: 170 },
    [MuscleGroupEnum.OBLIQUES]: { x: 90, y: 180 },
    [MuscleGroupEnum.QUADRICEPS]: { x: 115, y: 250 },
    [MuscleGroupEnum.HAMSTRINGS]: { x: 115, y: 260 },
    [MuscleGroupEnum.CALVES]: { x: 115, y: 350 },
    [MuscleGroupEnum.CALVES_FRONT]: { x: 110, y: 350 },
    [MuscleGroupEnum.CALVES_BACK]: { x: 120, y: 350 },
    [MuscleGroupEnum.GLUTES]: { x: 115, y: 200 },
    [MuscleGroupEnum.BACK]: { x: 115, y: 130 },
    [MuscleGroupEnum.LATS]: { x: 95, y: 150 },
    [MuscleGroupEnum.TRAPS]: { x: 115, y: 95 },
    [MuscleGroupEnum.LOWER_BACK]: { x: 115, y: 180 }
  };
  
  return positions[muscle] || { x: 115, y: 230 };
};

// iOS设备肌肉可视化尺寸配置
export const getIOSOptimalSize = (): 'sm' | 'md' | 'lg' => {
  if (typeof window === 'undefined') return 'md';
  
  const width = window.innerWidth;
  const height = window.innerHeight;
  const isLandscape = width > height;
  
  // iPhone小屏设备
  if (width <= 375 || (isLandscape && height <= 375)) {
    return 'sm';
  }
  
  // iPad或大屏iPhone
  if (width >= 768 || (isLandscape && height >= 768)) {
    return 'lg';
  }
  
  // 标准iPhone尺寸
  return 'md';
};

// 计算iOS Safe Area Insets
export const getIOSSafeAreaInsets = () => {
  if (typeof window === 'undefined' || typeof getComputedStyle === 'undefined') {
    return { top: 0, bottom: 0, left: 0, right: 0 };
  }
  
  const computedStyle = getComputedStyle(document.documentElement);
  
  return {
    top: parseInt(computedStyle.getPropertyValue('--safe-area-inset-top') || '0', 10),
    bottom: parseInt(computedStyle.getPropertyValue('--safe-area-inset-bottom') || '0', 10),
    left: parseInt(computedStyle.getPropertyValue('--safe-area-inset-left') || '0', 10),
    right: parseInt(computedStyle.getPropertyValue('--safe-area-inset-right') || '0', 10)
  };
};

// 判断是否需要硬件加速 - iOS优化
export const shouldUseHardwareAcceleration = (): boolean => {
  if (typeof navigator === 'undefined') return false;
  
  // 检测iOS设备
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  
  // 检测现代浏览器支持
  const supportsWillChange = CSS.supports('will-change', 'transform');
  
  return isIOS && supportsWillChange;
};

// 生成肌肉群动画延迟 - 创建波浪效果
export const getMuscleAnimationDelay = (muscle: MuscleGroupEnum, index: number): number => {
  const baseDelay = 0.1; // 100ms基础延迟
  const position = getMusclePosition(muscle);
  
  // 基于位置计算延迟，从上到下，从中心向外
  const distanceFromCenter = Math.sqrt(
    Math.pow(position.x - 115, 2) + Math.pow(position.y - 230, 2)
  );
  
  return baseDelay + (distanceFromCenter / 1000) + (index * 0.05);
};

// 肌肉群分类 - 用于组织和筛选
export const muscleGroupCategories = {
  upper: [
    MuscleGroupEnum.CHEST,
    MuscleGroupEnum.SHOULDERS, 
    MuscleGroupEnum.BICEPS,
    MuscleGroupEnum.TRICEPS,
    MuscleGroupEnum.FOREARMS,
    MuscleGroupEnum.BACK,
    MuscleGroupEnum.LATS,
    MuscleGroupEnum.TRAPS
  ],
  core: [
    MuscleGroupEnum.ABDOMINALS,
    MuscleGroupEnum.OBLIQUES,
    MuscleGroupEnum.LOWER_BACK
  ],
  lower: [
    MuscleGroupEnum.QUADRICEPS,
    MuscleGroupEnum.HAMSTRINGS,
    MuscleGroupEnum.CALVES,
    MuscleGroupEnum.GLUTES
  ]
};

// 获取肌肉群类别
export const getMuscleCategory = (muscle: MuscleGroupEnum): 'upper' | 'core' | 'lower' => {
  if (muscleGroupCategories.upper.includes(muscle)) return 'upper';
  if (muscleGroupCategories.core.includes(muscle)) return 'core';
  return 'lower';
}; 

/**
 * 智能肌肉群映射：根据主要肌肉智能判断肩部肌肉的前后束
 * @param primaryMuscleIds 主要肌肉ID数组
 * @param secondaryMuscleIds 次要肌肉ID数组
 * @returns 调整后的肌肉ID映射
 */
export function smartMuscleMappingForExercise(
  primaryMuscleIds: number[],
  secondaryMuscleIds: number[]
): {
  adjustedPrimary: number[];
  adjustedSecondary: number[];
} {
  // MUSCLE_CATEGORIES will be imported at the top
  
  // 获取主要肌肉的肌肉群类型
  const primaryMuscleGroups = primaryMuscleIds
    .map(id => MUSCLE_CATEGORIES[id as keyof typeof MUSCLE_CATEGORIES])
    .filter(muscle => muscle)
    .map(muscle => muscle.group);
  
  // 检查主要肌肉是否包含背部相关肌群
  const hasBackMuscles = primaryMuscleGroups.some(group => 
    group === MuscleGroupEnum.BACK || 
    group === MuscleGroupEnum.TRAPS
  );
  
  // 检查主要肌肉是否包含胸部相关肌群
  const hasChestMuscles = primaryMuscleGroups.some(group => 
    group === MuscleGroupEnum.CHEST
  );
  
  // 调整次要肌肉中的肩部肌肉
  const adjustedSecondary = secondaryMuscleIds.map(id => {
    const muscle = MUSCLE_CATEGORIES[id as keyof typeof MUSCLE_CATEGORIES];
    if (!muscle) return id;
    
    // 如果是普通的三角肌（SHOULDERS），根据主要肌肉进行智能映射
    if (muscle.group === MuscleGroupEnum.SHOULDERS) {
      if (hasBackMuscles) {
        // 主要肌肉包含背部肌群时，三角肌映射为后束 (ID: 9)
        return 9;
      } else if (hasChestMuscles) {
        // 主要肌肉包含胸部肌群时，三角肌映射为前束 (ID: 8)
        return 8;
      }
    }
    
    return id;
  });
  
  return {
    adjustedPrimary: primaryMuscleIds, // 主要肌肉不需要调整
    adjustedSecondary: adjustedSecondary
  };
} 