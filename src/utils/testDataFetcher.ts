/**
 * 测试数据获取工具
 * 用于获取真实的后端动态数据并分析数据结构
 */

import { authService } from '../services/authService';
import { CommunityService } from '../services/communityService';

/**
 * 数据获取和分析工具类
 */
export class TestDataFetcher {
  /**
   * 获取真实动态数据并分析结构
   */
  static async fetchAndAnalyzeRealFeedData(): Promise<{
    success: boolean;
    data?: any;
    analysis?: any;
    error?: string;
  }> {
    console.log('【数据获取器】开始获取真实动态数据');

    try {
      // 第一步：确保用户认证
      console.log('【数据获取器】检查用户认证状态');
      
      if (!authService.isAuthenticated()) {
        console.log('【数据获取器】用户未认证，开始登录');
        await authService.loginWithTestUser();
      }

      const currentUser = authService.getCurrentUser();
      console.log('【数据获取器】当前用户信息:', currentUser);

      // 第二步：获取动态数据
      console.log('【数据获取器】开始获取动态数据');
      
      const feedData = await CommunityService.getPosts({
        skip: 0,
        limit: 5 // 获取少量数据用于分析
      });

      console.log('【数据获取器】动态数据获取成功');

      // 第三步：分析数据结构
      const analysis = this.analyzeDataStructure(feedData);

      console.log('【数据获取器】数据结构分析完成:', analysis);

      return {
        success: true,
        data: feedData,
        analysis
      };

    } catch (error) {
      console.error('【数据获取器】获取数据失败:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 分析数据结构
   */
  private static analyzeDataStructure(data: any): any {
    console.log('【数据获取器】开始分析数据结构');

    const analysis: any = {
      dataType: typeof data,
      isArray: Array.isArray(data),
      structure: {},
      samples: {},
      fieldTypes: {},
      statistics: {
        totalItems: 0,
        hasWorkoutData: 0,
        hasImages: 0,
        hasComments: 0
      }
    };

    try {
      // 分析根级数据结构
      if (Array.isArray(data)) {
        analysis.statistics.totalItems = data.length;
        
        if (data.length > 0) {
          // 分析第一个项目的结构
          const firstItem = data[0];
          analysis.structure = this.getObjectStructure(firstItem);
          analysis.samples.firstItem = firstItem;
          analysis.fieldTypes = this.getFieldTypes(firstItem);

          // 统计特殊字段
          data.forEach((item: any) => {
            if (item.related_workout_detail || item.related_workout_id) {
              analysis.statistics.hasWorkoutData++;
            }
            if (item.images && item.images.length > 0) {
              analysis.statistics.hasImages++;
            }
            if (item.comments_count > 0) {
              analysis.statistics.hasComments++;
            }
          });
        }
      } else if (typeof data === 'object' && data !== null) {
        // 如果是对象，可能是分页响应
        analysis.structure = this.getObjectStructure(data);
        analysis.fieldTypes = this.getFieldTypes(data);
        
        // 检查是否有items字段（分页响应）
        if (data.items && Array.isArray(data.items)) {
          analysis.statistics.totalItems = data.items.length;
          if (data.items.length > 0) {
            analysis.samples.firstItem = data.items[0];
          }
        }
      }

      console.log('【数据获取器】数据结构分析结果:', analysis);
      return analysis;

    } catch (error) {
      console.error('【数据获取器】数据结构分析失败:', error);
      return {
        ...analysis,
        error: error instanceof Error ? error.message : '分析失败'
      };
    }
  }

  /**
   * 获取对象结构（字段名和嵌套层级）
   */
  private static getObjectStructure(obj: any, maxDepth: number = 3, currentDepth: number = 0): any {
    if (currentDepth >= maxDepth || obj === null || typeof obj !== 'object') {
      return typeof obj;
    }

    if (Array.isArray(obj)) {
      return {
        type: 'array',
        length: obj.length,
        itemType: obj.length > 0 ? this.getObjectStructure(obj[0], maxDepth, currentDepth + 1) : 'unknown'
      };
    }

    const structure: any = {};
    Object.keys(obj).forEach(key => {
      structure[key] = this.getObjectStructure(obj[key], maxDepth, currentDepth + 1);
    });

    return structure;
  }

  /**
   * 获取字段类型信息
   */
  private static getFieldTypes(obj: any): any {
    if (obj === null || typeof obj !== 'object') {
      return typeof obj;
    }

    if (Array.isArray(obj)) {
      return {
        type: 'array',
        length: obj.length,
        itemTypes: obj.length > 0 ? obj.slice(0, 3).map(item => typeof item) : []
      };
    }

    const fieldTypes: any = {};
    Object.entries(obj).forEach(([key, value]) => {
      if (value === null) {
        fieldTypes[key] = 'null';
      } else if (Array.isArray(value)) {
        fieldTypes[key] = {
          type: 'array',
          length: value.length,
          itemTypes: value.length > 0 ? value.slice(0, 3).map(item => typeof item) : []
        };
      } else if (typeof value === 'object') {
        fieldTypes[key] = 'object';
      } else {
        fieldTypes[key] = typeof value;
      }
    });

    return fieldTypes;
  }

  /**
   * 保存数据样例到文件
   */
  static async saveDataSample(data: any, filename: string = 'real-feed-data-sample.json'): Promise<void> {
    try {
      const jsonString = JSON.stringify(data, null, 2);
      
      // 在浏览器环境中，我们可以创建下载链接
      if (typeof window !== 'undefined') {
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        console.log(`【数据获取器】数据样例已保存为: ${filename}`);
      } else {
        // Node.js环境（测试环境）
        console.log('【数据获取器】数据样例（JSON格式）:');
        console.log(jsonString);
      }

    } catch (error) {
      console.error('【数据获取器】保存数据样例失败:', error);
    }
  }

  /**
   * 生成数据结构报告
   */
  static generateDataStructureReport(analysis: any): string {
    const report = `
# 动态数据结构分析报告

## 基本信息
- 数据类型: ${analysis.dataType}
- 是否为数组: ${analysis.isArray}
- 总项目数: ${analysis.statistics?.totalItems || 0}

## 统计信息
- 包含训练数据的项目: ${analysis.statistics?.hasWorkoutData || 0}
- 包含图片的项目: ${analysis.statistics?.hasImages || 0}
- 包含评论的项目: ${analysis.statistics?.hasComments || 0}

## 数据结构
\`\`\`json
${JSON.stringify(analysis.structure, null, 2)}
\`\`\`

## 字段类型
\`\`\`json
${JSON.stringify(analysis.fieldTypes, null, 2)}
\`\`\`

## 样例数据
\`\`\`json
${JSON.stringify(analysis.samples?.firstItem, null, 2)}
\`\`\`

---
生成时间: ${new Date().toISOString()}
    `.trim();

    return report;
  }

  /**
   * 验证必需字段
   */
  static validateRequiredFields(data: any): {
    isValid: boolean;
    missingFields: string[];
    warnings: string[];
  } {
    const requiredFields = [
      'id',
      'user',
      'content',
      'created_at'
    ];

    const optionalButImportantFields = [
      'related_workout_detail',
      'images',
      'likes_count',
      'comments_count',
      'is_liked_by_current_user'
    ];

    const result = {
      isValid: true,
      missingFields: [] as string[],
      warnings: [] as string[]
    };

    if (!Array.isArray(data) && (!data.items || !Array.isArray(data.items))) {
      result.isValid = false;
      result.missingFields.push('数据不是数组格式，也不包含items字段');
      return result;
    }

    const items = Array.isArray(data) ? data : data.items;
    
    if (items.length === 0) {
      result.warnings.push('数据为空，无法验证字段');
      return result;
    }

    const firstItem = items[0];

    // 检查必需字段
    requiredFields.forEach(field => {
      if (!(field in firstItem)) {
        result.isValid = false;
        result.missingFields.push(field);
      }
    });

    // 检查重要的可选字段
    optionalButImportantFields.forEach(field => {
      if (!(field in firstItem)) {
        result.warnings.push(`缺少重要字段: ${field}`);
      }
    });

    return result;
  }
}

export default TestDataFetcher;
