/**
 * 运动数据映射工具
 * 用于将API返回的数据转换为UI组件所需的数据格式
 */

import { 
  getBodyPartName, 
  getBodyPartNames, 
  getEquipmentNames, 
  getDifficultyText,
  EXERCISE_TYPES,
  BODY_PART_CATEGORIES,
  EQUIPMENT_CATEGORIES
} from '../constants/exerciseCategories';

import { getApiConfig } from '../config/api.config';

// 构建图片URL
export const buildImageUrl = (imageName: string): string => {
  const config = getApiConfig();
  return `${config.imageBaseURL}/${imageName}`;
};

// API返回的运动数据接口
export interface ApiExerciseResponse {
  name: string;
  en_name: string;
  body_part_id: number[];
  equipment_id: number[];
  image_name: string;
  gif_url: string;
  description: string | null;
  level: number;
  sort_priority: number;
  user_id: string | null;
  exercise_type: string;
  hit_time: number;
  id: number;
  created_at: string;
  updated_at: string;
}

// UI组件使用的运动数据接口（来自ExercisesPage.tsx）
export interface Exercise {
  id: string;
  name: string;
  englishName: string;
  category: string;
  muscleGroups: {
    primary: string[];
    secondary: string[];
  };
  equipment: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  instructions: string[];
  tips: string[];
  images: string[];
  videos: string[];
  alternatives: string[];
  is_favorite: boolean;
  popularity: number;
  // 新增字段
  gif_url?: string; // GIF动图URL
  primary_muscles?: string[]; // 主要肌肉群
  target?: string; // 目标肌肉（降级字段）
}

// API搜索参数接口
export interface ExerciseSearchParams {
  body_part_id?: number[];
  equipment_id?: number[];
  skip?: number;
  limit?: number;
  search?: string;
}

/**
 * 将API返回的运动数据转换为UI组件数据格式
 */
export function mapApiExerciseToUIExercise(apiExercise: ApiExerciseResponse): Exercise {
  const allMuscleGroups = getBodyPartNames(apiExercise.body_part_id);
  
  // 构建图片URL
  const imageUrl = apiExercise.image_name 
    ? buildImageUrl(apiExercise.image_name)
    : '';
  
  return {
    id: apiExercise.id.toString(),
    name: apiExercise.name,
    englishName: apiExercise.en_name,
    category: getBodyPartName(apiExercise.body_part_id[0]) || '其他',
    muscleGroups: {
      primary: allMuscleGroups.slice(0, Math.ceil(allMuscleGroups.length / 2)), // 前半部分作为主要肌群
      secondary: allMuscleGroups.slice(Math.ceil(allMuscleGroups.length / 2)) // 后半部分作为次要肌群
    },
    equipment: getEquipmentNames(apiExercise.equipment_id),
    difficulty: getDifficultyText(apiExercise.level) as 'beginner' | 'intermediate' | 'advanced',
    instructions: apiExercise.description ? [apiExercise.description] : [],
    tips: [], // API中没有tips字段，使用空数组
    images: imageUrl ? [imageUrl] : [],
    videos: apiExercise.gif_url ? [apiExercise.gif_url] : [],
    alternatives: [], // API中没有alternatives字段，使用空数组
    is_favorite: false, // 用户偏好功能暂时忽略
    popularity: apiExercise.hit_time || apiExercise.sort_priority || 0,
    // 新增字段映射
    gif_url: apiExercise.gif_url, // 直接映射GIF URL
    primary_muscles: allMuscleGroups.slice(0, Math.ceil(allMuscleGroups.length / 2)), // 主要肌肉群
    target: allMuscleGroups.length > 0 ? allMuscleGroups[0] : undefined // 目标肌肉（第一个）
  };
}

/**
 * 批量转换API运动数据
 */
export function mapApiExercisesToUIExercises(apiExercises: ApiExerciseResponse[]): Exercise[] {
  return apiExercises.map(mapApiExerciseToUIExercise);
}

/**
 * 构建API查询参数
 */
export function buildExerciseQueryParams(params: ExerciseSearchParams): URLSearchParams {
  const queryParams = new URLSearchParams();
  
  if (params.body_part_id && params.body_part_id.length > 0) {
    params.body_part_id.forEach(id => {
      queryParams.append('body_part_id', id.toString());
    });
  }
  
  if (params.equipment_id && params.equipment_id.length > 0) {
    params.equipment_id.forEach(id => {
      queryParams.append('equipment_id', id.toString());
    });
  }
  
  if (params.skip !== undefined) {
    queryParams.append('skip', params.skip.toString());
  }
  
  if (params.limit !== undefined) {
    queryParams.append('limit', params.limit.toString());
  }
  
  return queryParams;
}

/**
 * 根据分类名称获取对应的body_part_id
 */
export function getCategoryBodyPartIds(category: string): number[] {
  return BODY_PART_CATEGORIES
    .filter(bodyPart => bodyPart.name === category)
    .map(bodyPart => bodyPart.id);
}

/**
 * 根据器械名称获取对应的equipment_id
 */
export function getEquipmentIds(equipmentNames: string[]): number[] {
  const ids: number[] = [];
  equipmentNames.forEach(name => {
    const equipment = EQUIPMENT_CATEGORIES.find(eq => eq.name === name);
    if (equipment) {
      ids.push(equipment.id);
    }
  });
  
  return ids;
}

/**
 * 获取运动类型的中文名称
 */
export function getExerciseTypeName(exerciseType: string): string {
  return EXERCISE_TYPES[exerciseType as keyof typeof EXERCISE_TYPES] || exerciseType;
}
