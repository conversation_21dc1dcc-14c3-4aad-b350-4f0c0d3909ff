// 肌肉颜色计算工具函数
import { MuscleGroupEnum } from '../types/muscle.types';
import { WorkoutExercise, MuscleGroupIntensity, MuscleColorConfig } from '../types/feed.types';

// 权重配置
const MUSCLE_WEIGHTS = {
  PRIMARY: 1.0,   // 主要肌肉群权重100%
  SECONDARY: 0.5  // 次要肌肉群权重50%
} as const;

// 颜色强度阈值配置
const COLOR_INTENSITY_THRESHOLDS = {
  LIGHT: 0.2,   // 20%以下为浅色
  MEDIUM: 0.5,  // 20%-50%为中等
  HEAVY: 1.0    // 50%以上为深色
} as const;

/**
 * 计算单个动作的总训练重量
 * @param exercise 训练动作
 * @returns 总重量 (sets × weight × reps)
 */
export const calculateExerciseVolume = (exercise: WorkoutExercise): number => {
  return exercise.sets * exercise.weight * exercise.reps;
};

/**
 * 计算所有肌肉群的训练强度
 * @param exercises 训练动作列表
 * @returns 肌肉群强度数组
 */
export const calculateMuscleIntensities = (exercises: WorkoutExercise[]): MuscleGroupIntensity[] => {
  // 存储每个肌肉群的总重量
  const muscleWeights = new Map<MuscleGroupEnum, { total: number; isPrimary: boolean }>();

  // 计算总训练重量
  let totalWorkoutVolume = 0;

  // 遍历所有动作
  exercises.forEach(exercise => {
    const exerciseVolume = calculateExerciseVolume(exercise);
    totalWorkoutVolume += exerciseVolume;

    // 处理主要肌肉群
    exercise.primary_muscles.forEach(muscle => {
      const weightedVolume = exerciseVolume * MUSCLE_WEIGHTS.PRIMARY;
      const existing = muscleWeights.get(muscle);
      
      muscleWeights.set(muscle, {
        total: (existing?.total || 0) + weightedVolume,
        isPrimary: true // 如果有主要肌肉群记录，标记为主要
      });
    });

    // 处理次要肌肉群
    exercise.secondary_muscles?.forEach(muscle => {
      const weightedVolume = exerciseVolume * MUSCLE_WEIGHTS.SECONDARY;
      const existing = muscleWeights.get(muscle);
      
      // 只有当该肌肉群不是主要肌肉群时，才标记为次要
      muscleWeights.set(muscle, {
        total: (existing?.total || 0) + weightedVolume,
        isPrimary: existing?.isPrimary || false
      });
    });
  });

  // 转换为强度数组
  const intensities: MuscleGroupIntensity[] = [];
  
  muscleWeights.forEach((data, muscle) => {
    const percentage = totalWorkoutVolume > 0 ? data.total / totalWorkoutVolume : 0;
    
    // 确定颜色强度
    let colorIntensity: 'light' | 'medium' | 'heavy';
    if (percentage <= COLOR_INTENSITY_THRESHOLDS.LIGHT) {
      colorIntensity = 'light';
    } else if (percentage <= COLOR_INTENSITY_THRESHOLDS.MEDIUM) {
      colorIntensity = 'medium';
    } else {
      colorIntensity = 'heavy';
    }

    intensities.push({
      muscle,
      totalWeight: data.total,
      percentage,
      colorIntensity,
      isPrimary: data.isPrimary
    });
  });

  // 按重量降序排序
  return intensities.sort((a, b) => b.totalWeight - a.totalWeight);
};

/**
 * 生成肌肉颜色配置对象
 * @param intensities 肌肉群强度数组
 * @returns 肌肉颜色配置
 */
export const generateMuscleColorConfig = (intensities: MuscleGroupIntensity[]): MuscleColorConfig => {
  const config: MuscleColorConfig = {};

  intensities.forEach(intensity => {
    config[intensity.muscle] = {
      intensity: intensity.colorIntensity,
      type: intensity.isPrimary ? 'primary' : 'secondary'
    };
  });

  return config;
};

/**
 * 为StaticMuscleIllustration组件生成颜色配置
 * @param intensities 肌肉群强度数组
 * @returns 适用于StaticMuscleIllustration的颜色配置
 */
export const generateStaticMuscleColorConfig = (intensities: MuscleGroupIntensity[]): { [key: string]: 'primary' | 'secondary' } => {
  const config: { [key: string]: 'primary' | 'secondary' } = {};

  intensities.forEach(intensity => {
    config[intensity.muscle] = intensity.isPrimary ? 'primary' : 'secondary';
  });

  return config;
};

/**
 * 获取CSS变量名称
 * @param intensity 颜色强度
 * @returns CSS变量名
 */
export const getColorIntensityCSSVar = (intensity: 'light' | 'medium' | 'heavy'): string => {
  switch (intensity) {
    case 'light':
      return 'var(--accent-300)';
    case 'medium':
      return 'var(--accent-400)';
    case 'heavy':
      return 'var(--accent-500)';
    default:
      return 'var(--accent-300)';
  }
};

/**
 * 获取肌肉群的显示颜色
 * @param muscle 肌肉群
 * @param intensities 强度数组
 * @returns CSS颜色值
 */
export const getMuscleDisplayColor = (muscle: MuscleGroupEnum, intensities: MuscleGroupIntensity[]): string => {
  const intensity = intensities.find(i => i.muscle === muscle);
  if (!intensity) {
    return 'var(--primary-400)'; // 默认颜色
  }
  
  return getColorIntensityCSSVar(intensity.colorIntensity);
};

/**
 * 计算肌肉群训练覆盖率
 * @param intensities 强度数组
 * @returns 覆盖的肌肉群数量和总数的比例
 */
export const calculateMuscleCoverage = (intensities: MuscleGroupIntensity[]): {
  covered: number;
  total: number;
  percentage: number;
} => {
  const totalMuscles = Object.keys(MuscleGroupEnum).length;
  const coveredMuscles = intensities.length;
  
  return {
    covered: coveredMuscles,
    total: totalMuscles,
    percentage: totalMuscles > 0 ? (coveredMuscles / totalMuscles) * 100 : 0
  };
};

/**
 * 获取训练强度等级描述
 * @param intensities 强度数组
 * @returns 训练强度描述
 */
export const getWorkoutIntensityLevel = (intensities: MuscleGroupIntensity[]): {
  level: 'light' | 'moderate' | 'intense' | 'extreme';
  description: string;
} => {
  const coverage = calculateMuscleCoverage(intensities);
  const avgIntensity = intensities.reduce((sum, i) => sum + i.percentage, 0) / intensities.length;

  if (coverage.percentage < 20 || avgIntensity < 0.1) {
    return { level: 'light', description: '轻度训练' };
  } else if (coverage.percentage < 40 || avgIntensity < 0.3) {
    return { level: 'moderate', description: '中等强度' };
  } else if (coverage.percentage < 60 || avgIntensity < 0.6) {
    return { level: 'intense', description: '高强度训练' };
  } else {
    return { level: 'extreme', description: '极限训练' };
  }
};

/**
 * 验证训练数据完整性
 * @param exercises 训练动作列表
 * @returns 验证结果
 */
export const validateWorkoutData = (exercises: WorkoutExercise[]): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!exercises || exercises.length === 0) {
    errors.push('训练动作列表不能为空');
    return { isValid: false, errors, warnings };
  }

  exercises.forEach((exercise, index) => {
    if (!exercise.name) {
      errors.push(`动作 ${index + 1}: 缺少动作名称`);
    }
    
    if (exercise.sets <= 0) {
      errors.push(`动作 ${index + 1}: 组数必须大于0`);
    }
    
    if (exercise.reps <= 0) {
      errors.push(`动作 ${index + 1}: 次数必须大于0`);
    }
    
    if (exercise.weight < 0) {
      errors.push(`动作 ${index + 1}: 重量不能为负数`);
    }
    
    if (!exercise.primary_muscles || exercise.primary_muscles.length === 0) {
      warnings.push(`动作 ${index + 1}: 缺少主要肌肉群信息`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};
