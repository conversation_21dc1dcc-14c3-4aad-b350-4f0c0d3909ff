/**
 * 时间格式化工具
 * 提供相对时间和绝对时间的格式化功能
 */

/**
 * 格式化时间戳为相对时间或绝对时间
 * @param timestamp 时间戳（Date对象或字符串）
 * @returns 格式化后的时间字符串
 */
export function formatPostTime(timestamp: Date | string): string {
  const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  // 小于1分钟
  if (diffInSeconds < 60) {
    return '刚刚';
  }
  
  // 小于1小时
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}分钟前`;
  }
  
  // 小于24小时
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}小时前`;
  }
  
  // 小于7天
  if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    if (days === 1) {
      return '昨天';
    } else if (days === 2) {
      return '前天';
    } else {
      return `${days}天前`;
    }
  }
  
  // 超过7天，显示绝对时间
  return formatAbsoluteTime(date);
}

/**
 * 格式化为绝对时间
 * @param date Date对象
 * @returns 格式化后的绝对时间字符串（YYYY年MM月DD日 HH:mm）
 */
export function formatAbsoluteTime(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}年${month}月${day}日 ${hours}:${minutes}`;
}

/**
 * 获取详细的时间信息（用于tooltip等）
 * @param timestamp 时间戳
 * @returns 详细时间信息对象
 */
export function getDetailedTimeInfo(timestamp: Date | string) {
  const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
  
  return {
    relative: formatPostTime(timestamp),
    absolute: formatAbsoluteTime(date),
    iso: date.toISOString(),
    timestamp: date.getTime()
  };
}

/**
 * 格式化训练时长（秒）为可读的中文时间格式
 * @param seconds 秒数
 * @returns 格式化后的时间字符串（如："2小时30分钟"、"45分钟"、"15秒"）
 */
export function formatWorkoutDuration(seconds: number): string {
  if (seconds < 60) {
    return `${seconds}秒`;
  }
  
  if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (remainingSeconds === 0) {
      return `${minutes}分钟`;
    } else {
      return `${minutes}分钟${remainingSeconds}秒`;
    }
  }
  
  const hours = Math.floor(seconds / 3600);
  const remainingMinutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  
  let result = `${hours}小时`;
  
  if (remainingMinutes > 0) {
    result += `${remainingMinutes}分钟`;
  }
  
  if (remainingSeconds > 0 && remainingMinutes === 0) {
    result += `${remainingSeconds}秒`;
  }
  
  return result;
}
