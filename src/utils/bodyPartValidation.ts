/**
 * 身体部位数据一致性验证工具
 * 用于确保icon、name和id的映射关系正确
 */

import { BODY_PART_CATEGORIES, BODY_PART_ICONS, getBodyPartIconByName } from '../constants/exerciseCategories';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  summary: {
    totalCategories: number;
    categoriesWithIcons: number;
    missingIcons: string[];
  };
}

/**
 * 验证身体部位数据的一致性
 */
export function validateBodyPartData(): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  const missingIcons: string[] = [];

  // 检查每个身体部位是否有对应的图标
  BODY_PART_CATEGORIES.forEach(bodyPart => {
    const iconFileName = getBodyPartIconByName(bodyPart.name);
    
    if (!iconFileName) {
      errors.push(`身体部位 "${bodyPart.name}" (ID: ${bodyPart.id}) 缺少对应的图标文件`);
      missingIcons.push(bodyPart.name);
    } else {
      // 检查图标文件名是否在BODY_PART_ICONS中定义
      const hasIconMapping = BODY_PART_ICONS[bodyPart.id];
      if (!hasIconMapping) {
        warnings.push(`身体部位 "${bodyPart.name}" (ID: ${bodyPart.id}) 在BODY_PART_ICONS中没有映射`);
      }
    }
  });

  // 检查BODY_PART_ICONS中是否有孤立的图标（没有对应的分类）
  Object.keys(BODY_PART_ICONS).forEach(idStr => {
    const id = parseInt(idStr);
    const hasCategory = BODY_PART_CATEGORIES.find(bp => bp.id === id);
    if (!hasCategory) {
      warnings.push(`图标映射中的ID ${id} 在BODY_PART_CATEGORIES中没有对应的分类`);
    }
  });

  const categoriesWithIcons = BODY_PART_CATEGORIES.length - missingIcons.length;

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    summary: {
      totalCategories: BODY_PART_CATEGORIES.length,
      categoriesWithIcons,
      missingIcons
    }
  };
}

/**
 * 打印验证结果到控制台
 */
export function logValidationResult(): void {
  const result = validateBodyPartData();
  
  console.group('🔍 身体部位数据验证结果');
  
  console.log('📊 数据摘要:');
  console.log(`  总分类数: ${result.summary.totalCategories}`);
  console.log(`  有图标的分类: ${result.summary.categoriesWithIcons}`);
  console.log(`  覆盖率: ${((result.summary.categoriesWithIcons / result.summary.totalCategories) * 100).toFixed(1)}%`);
  
  if (result.errors.length > 0) {
    console.error('❌ 错误:');
    result.errors.forEach(error => console.error(`  - ${error}`));
  }
  
  if (result.warnings.length > 0) {
    console.warn('⚠️ 警告:');
    result.warnings.forEach(warning => console.warn(`  - ${warning}`));
  }
  
  if (result.isValid) {
    console.log('✅ 验证通过，数据一致性良好');
  } else {
    console.error('❌ 验证失败，需要修复数据不一致问题');
  }
  
  console.groupEnd();
}

/**
 * 开发环境下自动验证
 */
if (import.meta.env.DEV) {
  logValidationResult();
} 