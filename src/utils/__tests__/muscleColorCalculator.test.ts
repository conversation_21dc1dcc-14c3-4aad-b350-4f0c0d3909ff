// 肌肉颜色计算工具函数测试
import { 
  calculateMuscleIntensities, 
  generateMuscleColorConfig,
  generateStaticMuscleColorConfig,
  validateWorkoutData,
  calculateExerciseVolume
} from '../muscleColorCalculator';
import { MuscleGroupEnum } from '../../types/muscle.types';
import { WorkoutExercise } from '../../types/feed.types';

// 测试数据
const mockExercises: WorkoutExercise[] = [
  {
    id: 'ex_1',
    name: '杠铃卧推',
    sets: 4,
    reps: 8,
    weight: 80,
    primary_muscles: [MuscleGroupEnum.CHEST],
    secondary_muscles: [MuscleGroupEnum.TRICEPS, MuscleGroupEnum.SHOULDERS_FRONT]
  },
  {
    id: 'ex_2',
    name: '哑铃肩上推举',
    sets: 3,
    reps: 10,
    weight: 25,
    primary_muscles: [MuscleGroupEnum.SHOULDERS_FRONT],
    secondary_muscles: [MuscleGroupEnum.TRICEPS]
  }
];

describe('muscleColorCalculator', () => {
  describe('calculateExerciseVolume', () => {
    it('should calculate exercise volume correctly', () => {
      const exercise = mockExercises[0];
      const volume = calculateExerciseVolume(exercise);
      expect(volume).toBe(4 * 8 * 80); // sets * reps * weight = 2560
    });
  });

  describe('calculateMuscleIntensities', () => {
    it('should calculate muscle intensities correctly', () => {
      const intensities = calculateMuscleIntensities(mockExercises);
      
      // 应该包含所有涉及的肌肉群
      expect(intensities.length).toBeGreaterThan(0);
      
      // 检查是否包含主要肌肉群
      const chestIntensity = intensities.find(i => i.muscle === MuscleGroupEnum.CHEST);
      expect(chestIntensity).toBeDefined();
      expect(chestIntensity?.isPrimary).toBe(true);
      
      // 检查次要肌肉群
      const tricepsIntensity = intensities.find(i => i.muscle === MuscleGroupEnum.TRICEPS);
      expect(tricepsIntensity).toBeDefined();
      
      // 检查百分比计算
      const totalPercentage = intensities.reduce((sum, i) => sum + i.percentage, 0);
      expect(totalPercentage).toBeCloseTo(1, 1); // 总和应该接近1
    });

    it('should handle empty exercises array', () => {
      const intensities = calculateMuscleIntensities([]);
      expect(intensities).toEqual([]);
    });
  });

  describe('generateMuscleColorConfig', () => {
    it('should generate color config correctly', () => {
      const intensities = calculateMuscleIntensities(mockExercises);
      const config = generateMuscleColorConfig(intensities);
      
      // 检查配置结构
      Object.values(config).forEach(muscleConfig => {
        expect(['light', 'medium', 'heavy']).toContain(muscleConfig.intensity);
        expect(['primary', 'secondary']).toContain(muscleConfig.type);
      });
    });
  });

  describe('generateStaticMuscleColorConfig', () => {
    it('should generate static muscle color config', () => {
      const intensities = calculateMuscleIntensities(mockExercises);
      const config = generateStaticMuscleColorConfig(intensities);
      
      // 检查配置格式
      Object.values(config).forEach(type => {
        expect(['primary', 'secondary']).toContain(type);
      });
    });
  });

  describe('validateWorkoutData', () => {
    it('should validate correct workout data', () => {
      const result = validateWorkoutData(mockExercises);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid workout data', () => {
      const invalidExercises: WorkoutExercise[] = [
        {
          id: 'ex_invalid',
          name: '',
          sets: 0,
          reps: -1,
          weight: -10,
          primary_muscles: []
        }
      ];
      
      const result = validateWorkoutData(invalidExercises);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should handle empty exercises array', () => {
      const result = validateWorkoutData([]);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('训练动作列表不能为空');
    });
  });
});
