import type {
  ExerciseRecord,
  NutritionRecord,
  WeightTrend,
  WaterIntake,
  StepsCount,
  DashboardData,
  MockDataGenerator
} from '../types/dashboard'

class DashboardMockDataGenerator implements MockDataGenerator {
  
  generateExerciseRecord(): ExerciseRecord {
    const exerciseTypes = ['跑步', '健身', '游泳', '骑行', '瑜伽', '力量训练']
    const exerciseNames = ['晨跑', '胸肌训练', '自由泳', '公路骑行', '哈他瑜伽', '深蹲训练']
    
    const todayDuration = Math.floor(Math.random() * 90) + 15 // 15-105分钟
    const goalDuration = 60 // 目标60分钟
    const exerciseCount = Math.floor(Math.random() * 3) + 1 // 1-3次
    
    return {
      todayDuration,
      goalDuration,
      exerciseType: exerciseTypes[Math.floor(Math.random() * exerciseTypes.length)],
      exerciseCount,
      lastExercise: {
        name: exerciseNames[Math.floor(Math.random() * exerciseNames.length)],
        duration: Math.floor(Math.random() * 45) + 15,
        calories: Math.floor(Math.random() * 300) + 100,
        timestamp: new Date(Date.now() - Math.random() * 4 * 60 * 60 * 1000) // 4小时内
      }
    }
  }

  generateNutritionRecord(): NutritionRecord {
    const caloriesConsumed = Math.floor(Math.random() * 800) + 1200 // 1200-2000
    const caloriesGoal = 2000
    
    // 营养素分配 (蛋白质20%, 碳水50%, 脂肪30%)
    const proteinCalories = caloriesConsumed * 0.2
    const carbsCalories = caloriesConsumed * 0.5
    const fatCalories = caloriesConsumed * 0.3
    
    const meals = ['早餐燕麦粥', '午餐鸡胸肉沙拉', '晚餐三文鱼', '酸奶坚果', '水果拼盘']
    
    return {
      calories: {
        consumed: caloriesConsumed,
        goal: caloriesGoal
      },
      macros: {
        protein: {
          consumed: Math.round(proteinCalories / 4), // 蛋白质4卡/克
          goal: 150,
          percentage: 20
        },
        carbs: {
          consumed: Math.round(carbsCalories / 4), // 碳水4卡/克
          goal: 250,
          percentage: 50
        },
        fat: {
          consumed: Math.round(fatCalories / 9), // 脂肪9卡/克
          goal: 67,
          percentage: 30
        }
      },
      mealCount: Math.floor(Math.random() * 4) + 2, // 2-5餐
      lastMeal: {
        name: meals[Math.floor(Math.random() * meals.length)],
        calories: Math.floor(Math.random() * 400) + 200,
        timestamp: new Date(Date.now() - Math.random() * 3 * 60 * 60 * 1000) // 3小时内
      }
    }
  }

  generateWeightTrend(): WeightTrend {
    const currentWeight = 65 + Math.random() * 20 // 65-85kg
    const baseWeight = currentWeight + (Math.random() - 0.5) * 2 // 基础体重
    
    // 生成一周数据
    const weeklyData = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const weight = baseWeight + (Math.random() - 0.5) * 1.5 // 体重波动
      weeklyData.push({
        date: date.toISOString().split('T')[0],
        weight: Math.round(weight * 10) / 10
      })
    }
    
    // 计算趋势
    const firstWeight = weeklyData[0].weight
    const lastWeight = weeklyData[weeklyData.length - 1].weight
    const changeAmount = Math.round((lastWeight - firstWeight) * 10) / 10
    
    let trend: 'up' | 'down' | 'stable'
    if (Math.abs(changeAmount) < 0.2) {
      trend = 'stable'
    } else if (changeAmount > 0) {
      trend = 'up'
    } else {
      trend = 'down'
    }
    
    // 计算BMI (假设身高170cm)
    const height = 1.7
    const bmi = Math.round((currentWeight / (height * height)) * 10) / 10
    
    return {
      currentWeight: Math.round(currentWeight * 10) / 10,
      weightUnit: 'kg',
      weeklyData,
      trend,
      changeAmount,
      bmi,
      targetWeight: 70
    }
  }

  generateWaterIntake(): WaterIntake {
    const consumed = Math.floor(Math.random() * 1500) + 500 // 500-2000ml
    const goal = 2000 // 目标2000ml
    const drinkCount = Math.floor(consumed / 250) // 每次约250ml
    
    const lastDrinkTime = new Date(Date.now() - Math.random() * 2 * 60 * 60 * 1000) // 2小时内
    
    return {
      consumed,
      goal,
      unit: 'ml',
      drinkCount,
      lastDrinkTime,
      reminders: {
        enabled: true,
        interval: 60, // 60分钟提醒一次
        nextReminder: new Date(Date.now() + 30 * 60 * 1000) // 30分钟后提醒
      }
    }
  }

  generateStepsCount(): StepsCount {
    const steps = Math.floor(Math.random() * 8000) + 2000 // 2000-10000步
    const goal = 10000
    
    // 根据步数计算距离和卡路里
    const distance = Math.round((steps * 0.0007) * 100) / 100 // 约0.7米/步
    const calories = Math.floor(steps * 0.04) // 约0.04卡/步
    const activeMinutes = Math.floor(steps / 100) // 约100步/分钟活跃时间
    
    // 生成每小时数据
    const hourlyData = []
    const currentHour = new Date().getHours()
    let remainingSteps = steps
    
    for (let hour = 0; hour <= currentHour; hour++) {
      let hourSteps
      if (hour === currentHour) {
        hourSteps = remainingSteps
      } else {
        // 模拟不同时间段的活跃度
        let activity = 0.5 // 基础活跃度
        if (hour >= 7 && hour <= 9) activity = 1.2 // 早晨活跃
        if (hour >= 12 && hour <= 14) activity = 0.8 // 午休较少
        if (hour >= 18 && hour <= 20) activity = 1.0 // 晚间活跃
        if (hour >= 22 || hour <= 6) activity = 0.1 // 睡眠时间
        
        hourSteps = Math.floor(Math.random() * 800 * activity)
        remainingSteps -= hourSteps
      }
      
      hourlyData.push({
        hour,
        steps: Math.max(0, hourSteps)
      })
    }
    
    return {
      steps,
      goal,
      distance,
      calories,
      activeMinutes,
      hourlyData
    }
  }

  generateFullDashboard(): DashboardData {
    return {
      exerciseRecord: this.generateExerciseRecord(),
      nutritionRecord: this.generateNutritionRecord(),
      weightTrend: this.generateWeightTrend(),
      waterIntake: this.generateWaterIntake(),
      stepsCount: this.generateStepsCount(),
      lastUpdated: new Date()
    }
  }
}

// 创建单例实例
export const mockDataGenerator = new DashboardMockDataGenerator()

// 导出默认实例
export default mockDataGenerator 