/**
 * 🍎 iOS Header扩展 - JavaScript运行时检测工具
 * 动态检测iOS设备、Safe Area值并应用样式修复
 */

export interface SafeAreaInsets {
  top: number;
  bottom: number;
  left: number;
  right: number;
}

export interface iOSDetectionResult {
  isIOS: boolean;
  isCapacitor: boolean;
  safeAreaInsets: SafeAreaInsets;
  deviceInfo: {
    userAgent: string;
    platform: string;
    vendor: string;
  };
}

/**
 * 检测是否为iOS设备
 */
export const detectiOS = (): boolean => {
  // 多重检测机制
  const checks = [
    // 用户代理检测
    /iPad|iPhone|iPod/.test(navigator.userAgent),
    
    // 平台检测
    /^iP/.test(navigator.platform),
    
    // Vendor检测
    navigator.vendor?.includes('Apple'),
    
    // Touch事件检测 (iOS Safari特有)
    'ontouchstart' in window && /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent),
    
    // CSS支持检测
    CSS.supports?.('-webkit-touch-callout', 'none'),
    
    // 屏幕尺寸特征检测 (常见iOS设备)
    (() => {
      const { width, height } = screen;
      const commonIOSSizes = [
        [375, 667], [414, 736], [375, 812], [414, 896], // iPhone
        [768, 1024], [820, 1180], [834, 1194], [1024, 1366] // iPad
      ];
      return commonIOSSizes.some(([w, h]) => 
        (width === w && height === h) || (width === h && height === w)
      );
    })()
  ];
  
  // 至少两个检测通过才认为是iOS
  return checks.filter(Boolean).length >= 2;
};

/**
 * 检测是否在Capacitor环境中
 */
export const detectCapacitor = (): boolean => {
  return !!(window as any).Capacitor?.isNativePlatform?.();
};

/**
 * 获取Safe Area Insets值
 */
export const getSafeAreaInsets = (): SafeAreaInsets => {
  const computedStyle = getComputedStyle(document.documentElement);
  
  const getEnvValue = (property: string): number => {
    const value = computedStyle.getPropertyValue(`env(${property})`);
    if (!value) return 0;
    
    // 解析像素值
    const match = value.match(/(\d+(?:\.\d+)?)px?/);
    return match ? parseFloat(match[1]) : 0;
  };
  
  return {
    top: getEnvValue('safe-area-inset-top') || 44, // iOS标准状态栏高度
    bottom: getEnvValue('safe-area-inset-bottom') || 0,
    left: getEnvValue('safe-area-inset-left') || 0,
    right: getEnvValue('safe-area-inset-right') || 0,
  };
};

/**
 * 获取设备信息
 */
export const getDeviceInfo = () => ({
  userAgent: navigator.userAgent,
  platform: navigator.platform,
  vendor: navigator.vendor || 'unknown',
});

/**
 * 综合iOS检测
 */
export const detectiOSEnvironment = (): iOSDetectionResult => {
  const isIOS = detectiOS();
  const isCapacitor = detectCapacitor();
  const safeAreaInsets = getSafeAreaInsets();
  const deviceInfo = getDeviceInfo();
  
  console.log('🍎 iOS环境检测结果:', {
    isIOS,
    isCapacitor,
    safeAreaInsets,
    deviceInfo
  });
  
  return {
    isIOS,
    isCapacitor,
    safeAreaInsets,
    deviceInfo
  };
};

/**
 * 动态应用iOS Header修复样式
 */
export const applyiOSHeaderFix = (theme: 'light' | 'dark' = 'light'): void => {
  const detection = detectiOSEnvironment();
  
  if (!detection.isIOS) {
    console.log('📱 非iOS设备，跳过Header修复');
    return;
  }
  
  console.log('🎨 开始应用iOS Header修复...');
  
  const { safeAreaInsets } = detection;
  
  // 计算尺寸
  const headerContentHeight = 64; // Header内容高度
  const headerPadding = 16; // Header内边距
  const totalHeaderHeight = safeAreaInsets.top + headerContentHeight;
  const contentPaddingTop = safeAreaInsets.top + headerContentHeight + headerPadding;
  
  // 创建或更新动态样式
  let styleElement = document.getElementById('ios-header-fix-dynamic') as HTMLStyleElement;
  
  if (!styleElement) {
    styleElement = document.createElement('style');
    styleElement.id = 'ios-header-fix-dynamic';
    document.head.appendChild(styleElement);
  }
  
  // 生成动态CSS
  const css = `
    /* 🍎 动态iOS Header修复样式 */
    @media (max-width: 768px) {
      .page-header {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1100 !important;
        
        padding-top: ${safeAreaInsets.top + headerPadding}px !important;
        padding-left: 16px !important;
        padding-right: 16px !important;
        padding-bottom: 16px !important;
        
        min-height: ${totalHeaderHeight}px !important;
        
        background: ${theme === 'light' ? '#ffffff' : '#0f172a'} !important;
        color: ${theme === 'light' ? '#1e293b' : '#f8fafc'} !important;
        border-bottom: 1px solid ${theme === 'light' ? '#e2e8f0' : '#374151'} !important;
        
        backdrop-filter: blur(20px) !important;
        -webkit-backdrop-filter: blur(20px) !important;
        will-change: transform !important;
        transform: translateZ(0) !important;
      }
      
      .page-content {
        padding-top: ${contentPaddingTop}px !important;
        padding-left: 16px !important;
        padding-right: 16px !important;
        padding-bottom: 16px !important;
        height: 100vh !important;
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        position: relative !important;
        z-index: 1 !important;
      }
      
      .main-content.with-bottom-nav .page-content {
        padding-bottom: ${70 + safeAreaInsets.bottom + 16}px !important;
        height: calc(100vh - ${70 + safeAreaInsets.bottom}px) !important;
      }
      
      .main-content {
        overflow: hidden !important;
      }
    }
    
    /* 调试信息 */
    .debug-statusbar::before {
      content: "Safe Area Top: ${safeAreaInsets.top}px, Total Header: ${totalHeaderHeight}px";
      position: fixed;
      top: 0;
      right: 0;
      background: rgba(255, 0, 0, 0.8);
      color: white;
      font-size: 10px;
      padding: 4px;
      z-index: 9999;
      pointer-events: none;
    }
  `;
  
  styleElement.textContent = css;
  
  console.log('✅ iOS Header修复应用成功:', {
    safeAreaTop: safeAreaInsets.top,
    totalHeaderHeight,
    contentPaddingTop,
    theme
  });
};

/**
 * 监听主题变化并更新样式
 */
export const watchThemeChanges = (): (() => void) => {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
        const theme = document.documentElement.getAttribute('data-theme') as 'light' | 'dark' || 'light';
        console.log('🎨 主题变化检测到:', theme);
        
        // 延迟应用，确保主题切换完成
        setTimeout(() => {
          applyiOSHeaderFix(theme);
        }, 100);
      }
    });
  });
  
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['data-theme', 'class']
  });
  
  // 返回清理函数
  return () => observer.disconnect();
};

/**
 * 初始化iOS Header修复
 */
export const initializeiOSHeaderFix = (): (() => void) => {
  console.log('🚀 初始化iOS Header修复...');
  
  // 立即检测和应用
  const initialTheme = document.documentElement.getAttribute('data-theme') as 'light' | 'dark' || 'light';
  applyiOSHeaderFix(initialTheme);
  
  // 监听主题变化
  const cleanupThemeWatcher = watchThemeChanges();
  
  // 监听窗口大小变化（设备旋转）
  const handleResize = () => {
    console.log('📱 设备尺寸变化，重新应用修复');
    const currentTheme = document.documentElement.getAttribute('data-theme') as 'light' | 'dark' || 'light';
    setTimeout(() => applyiOSHeaderFix(currentTheme), 200);
  };
  
  window.addEventListener('resize', handleResize);
  window.addEventListener('orientationchange', handleResize);
  
  // 返回清理函数
  return () => {
    cleanupThemeWatcher();
    window.removeEventListener('resize', handleResize);
    window.removeEventListener('orientationchange', handleResize);
    
    // 移除动态样式
    const styleElement = document.getElementById('ios-header-fix-dynamic');
    if (styleElement) {
      styleElement.remove();
    }
  };
}; 