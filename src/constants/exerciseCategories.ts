/**
 * 运动分类映射常量
 * 用于API数据与UI数据之间的转换
 */

import { MuscleGroupEnum } from '../types/muscle.types';

// 身体部位分类 - 只保留有对应图标的身体部位
export const BODY_PART_CATEGORIES = [
  { id: 2, name: '胸部' },
  { id: 3, name: '臀部' },
  { id: 4, name: '背部' },
  { id: 6, name: '肩部' },
  { id: 7, name: '小臂' },
  { id: 8, name: '小腿' },
  { id: 9, name: '颈部' },
  { id: 10, name: '有氧' },
  { id: 12, name: '腰腹部' },
  { id: 17, name: '二头' },
  { id: 18, name: '三头' },
  { id: 19, name: '股四头肌' },
  { id: 20, name: '腘绳肌' }
];

// 身体部位图标映射
export const BODY_PART_ICONS: { [key: number]: string } = {
  2: 'ic_chip_chest_b.svg',      // 胸部
  3: 'ic_chip_hips_b.svg',       // 臀部
  4: 'ic_chip_back_b.svg',       // 背部
  6: 'ic_chip_shoulders_b.svg',  // 肩部
  7: 'ic_chip_forearms_b.svg',   // 小臂
  8: 'ic_chip_calves_b.svg',     // 小腿
  9: 'ic_chip_neck_b.svg',       // 颈部
  10: 'chip_cardio.svg',         // 有氧
  12: 'chip_abs_b.svg',          // 腰腹部
  17: 'chip_biceps_b.svg',       // 二头
  18: 'chip_triceps_b.svg',      // 三头
  19: 'chip_quadriceps_b.svg',   // 股四头肌
  20: 'chip_hamstrings_b.svg'    // 腘绳肌
};

// 器械分类映射
export const EQUIPMENT_CATEGORIES = [
  { "id": 1, "name": "杠铃" },
  { "id": 2, "name": "自重" },
  { "id": 3, "name": "绳索" },
  { "id": 4, "name": "哑铃" },
  { "id": 5, "name": "EZ杠铃" },
  { "id": 6, "name": "固定器械" },
  { "id": 7, "name": "悍马器械" },
  { "id": 8, "name": "史密斯" },
  { "id": 9, "name": "负重" },
  { "id": 10, "name": "协助" },
  { "id": 11, "name": "弹力带" },
  { "id": 12, "name": "战绳" },
  { "id": 13, "name": "波速球" },
  { "id": 14, "name": "战锤" },
  { "id": 15, "name": "壶铃" },
  { "id": 16, "name": "药球" },
  { "id": 17, "name": "奥杆" },
  { "id": 18, "name": "雪橇" },
  { "id": 19, "name": "阻力带" },
  { "id": 20, "name": "泡沫轴" },
  { "id": 21, "name": "筋膜球" },
  { "id": 22, "name": "绳类" },
  { "id": 23, "name": "瑜伽球" },
  { "id": 24, "name": "训练棍" },
  { "id": 25, "name": "TRX" },
  { "id": 26, "name": "六角杠铃" },
  { "id": 27, "name": "卷腹轮" }
] as const;

// 肌肉分类映射
export const MUSCLE_CATEGORIES = {
  2: { name: '内收肌', group: MuscleGroupEnum.HIP_ADDUCTORS, primary: false },
  4: { name: '肱二头肌', group: MuscleGroupEnum.BICEPS, primary: true },
  5: { name: '肱肌', group: MuscleGroupEnum.BICEPS, primary: false },
  6: { name: '肱桡肌', group: MuscleGroupEnum.FOREARMS, primary: false },
  8: { name: '三角肌前束', group: MuscleGroupEnum.SHOULDERS_FRONT, primary: true },
  9: { name: '三角肌后束', group: MuscleGroupEnum.SHOULDERS_BACK, primary: true },
  10: { name: '三角肌', group: MuscleGroupEnum.SHOULDERS, primary: false },
  12: { name: '腓肠肌', group: MuscleGroupEnum.CALVES_BACK, primary: true },
  13: { name: '臀大肌', group: MuscleGroupEnum.GLUTES, primary: true },
  17: { name: '腘绳肌', group: MuscleGroupEnum.HAMSTRINGS, primary: true },
  18: { name: '髂腰肌', group: MuscleGroupEnum.HIP_ADDUCTORS, primary: false },
  19: { name: '冈下肌', group: MuscleGroupEnum.BACK, primary: false },
  20: { name: '背阔肌', group: MuscleGroupEnum.BACK, primary: true },
  22: { name: '腹斜肌', group: MuscleGroupEnum.OBLIQUES, primary: true },
  24: { name: '胸大肌上束', group: MuscleGroupEnum.CHEST, primary: false },
  25: { name: '胸大肌', group: MuscleGroupEnum.CHEST, primary: true },
  27: { name: '股四头肌', group: MuscleGroupEnum.QUADRICEPS, primary: true },
  28: { name: '腹直肌', group: MuscleGroupEnum.ABDOMINALS, primary: true },
  29: { name: '缝匠肌', group: MuscleGroupEnum.QUADRICEPS, primary: false },
  31: { name: '前锯肌', group: MuscleGroupEnum.OBLIQUES, primary: false },
  32: { name: '比目鱼肌', group: MuscleGroupEnum.CALVES_BACK, primary: true },
  36: { name: '股筋膜张肌', group: MuscleGroupEnum.HAMSTRINGS, primary: false },
  37: { name: '大圆肌', group: MuscleGroupEnum.BACK, primary: false },
  38: { name: '小圆肌', group: MuscleGroupEnum.BACK, primary: false },
  39: { name: '胫骨肌', group: MuscleGroupEnum.CALVES_FRONT, primary: false },
  43: { name: '斜方肌', group: MuscleGroupEnum.TRAPS, primary: true },
  44: { name: '肱三头肌', group: MuscleGroupEnum.TRICEPS, primary: true },
  45: { name: '腕伸肌', group: MuscleGroupEnum.FOREARMS_BACK, primary: false },
  46: { name: '腕屈肌', group: MuscleGroupEnum.FOREARMS_FRONT, primary: false },
  51: { name: '胸大肌下束', group: MuscleGroupEnum.CHEST, primary: false }
} as const;

// 难度等级映射
export const DIFFICULTY_LEVELS = {
  1: 'beginner',
  2: 'intermediate', 
  3: 'advanced'
} as const;

// 运动类型映射
export const EXERCISE_TYPES = {
  'weight_reps': '重量次数',
  'time': '时间',
  'distance': '距离',
  'bodyweight': '自重'
} as const;

// 根据身体部位ID获取图标文件名
export const getBodyPartIcon = (bodyPartId: number): string | null => {
  return BODY_PART_ICONS[bodyPartId] || null;
};

// 根据身体部位名称获取图标文件名
export const getBodyPartIconByName = (bodyPartName: string): string | null => {
  const bodyPart = BODY_PART_CATEGORIES.find(bp => bp.name === bodyPartName);
  return bodyPart ? getBodyPartIcon(bodyPart.id) : null;
};

// 工具函数：根据body_part_id获取身体部位名称
export function getBodyPartName(bodyPartId: number): string {
  const bodyPart = BODY_PART_CATEGORIES.find(item => item.id === bodyPartId);
  return bodyPart?.name || '其他';
}

// 工具函数：根据body_part_ids获取身体部位名称列表
export function getBodyPartNames(bodyPartIds: number[]): string[] {
  if (!bodyPartIds || bodyPartIds.length === 0) return [];
  
  return bodyPartIds.map(id => {
    const bodyPart = BODY_PART_CATEGORIES.find(item => item.id === id);
    return bodyPart?.name || '其他';
  });
}

// 工具函数：根据equipment_id获取器械名称
export function getEquipmentName(equipmentId: number): string {
  const equipment = EQUIPMENT_CATEGORIES.find(item => item.id === equipmentId);
  return equipment?.name || '未知器械';
}

// 工具函数：根据equipment_ids获取器械名称列表
export function getEquipmentNames(equipmentIds: number[]): string[] {
  if (!equipmentIds || equipmentIds.length === 0) return [];
  
  return equipmentIds.map(id => {
    const equipment = EQUIPMENT_CATEGORIES.find(item => item.id === id);
    return equipment?.name || '未知器械';
  });
}

// 工具函数：根据难度等级获取难度文本
export function getDifficultyText(level: number): string {
  return DIFFICULTY_LEVELS[level as keyof typeof DIFFICULTY_LEVELS] || 'beginner';
}

// 工具函数：构建图片URL
export function buildImageUrl(imagePath: string): string {
  // 使用配置化的方式获取baseURL
  const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
  return `${baseURL}${imagePath}`;
}

// 自定义身体部位显示顺序 - 有氧优先，其他按用户习惯排序
export function getDisplayOrderedBodyParts(): typeof BODY_PART_CATEGORIES {
  // 定义理想的显示顺序（基于用户使用习惯）
  const DISPLAY_ORDER = [
    '有氧',     // 最常用，放在首位
    '胸部',     // 上肢主要肌群
    '背部', 
    '肩部',
    '二头',     // 手臂肌群
    '三头',
    '腰腹部',   // 核心肌群
    '股四头肌', // 下肢肌群
    '腘绳肌',
    '臀部',
    '小腿',     // 辅助肌群
    '小臂',
    '颈部'      // 专项肌群
  ];

  // 根据显示顺序重新排列BODY_PART_CATEGORIES
  const orderedParts: typeof BODY_PART_CATEGORIES = [];
  
  DISPLAY_ORDER.forEach(name => {
    const bodyPart = BODY_PART_CATEGORIES.find(bp => bp.name === name);
    if (bodyPart) {
      orderedParts.push(bodyPart);
    }
  });

  // 添加任何可能遗漏的身体部位（防御性编程）
  BODY_PART_CATEGORIES.forEach(bodyPart => {
    if (!orderedParts.find(bp => bp.id === bodyPart.id)) {
      orderedParts.push(bodyPart);
    }
  });

  return orderedParts;
}