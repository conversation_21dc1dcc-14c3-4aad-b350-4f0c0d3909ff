/**
 * FitMaster 品牌常量定义
 * 用于在整个应用中统一品牌信息
 */

export const BRAND = {
  // 品牌名称
  NAME: 'FitMaster',
  FULL_NAME: 'FitMaster - 智能健身管理平台',
  
  // 版本信息
  VERSION: '1.0.0',
  BUILD: '2025.01.17',
  
  // 描述信息
  DESCRIPTION: '专业的智能健身管理应用，帮助您记录训练、跟踪进度、建立健身习惯。让健身变得更简单、更有趣、更有效果。',
  SHORT_DESCRIPTION: 'FitMaster现代健身管理应用，提供完整的训练记录、数据分析和个性化健身指导。',
  
  // 团队信息
  TEAM: 'FitMaster Team',
  COPYRIGHT: '© 2025 FitMaster Team',
  TAGLINE: 'Made with ❤️ for fitness enthusiasts',
  
  // 本地存储键名
  STORAGE_KEYS: {
    THEME: 'fitmaster-theme',
    CACHE: 'fitmaster-cache',
    USER_DATA: 'fitmaster-user-data'
  },
  
  // 文件下载名称
  EXPORT_FILENAMES: {
    USER_DATA: 'fitmaster-user-data.json',
    WORKOUT_DATA: 'fitmaster-workout-data.json',
    SETTINGS: 'fitmaster-settings.json'
  },
  
  // 颜色主题
  COLORS: {
    PRIMARY: '#3b82f6',
    SECONDARY: '#10b981',
    ACCENT: '#8b5cf6',
    WARNING: '#f59e0b',
    ERROR: '#ef4444',
    SUCCESS: '#22c55e'
  },
  
  // SEO 元信息
  META: {
    TITLE: 'FitMaster - 智能健身管理平台',
    DESCRIPTION: 'FitMaster现代健身管理应用，提供完整的训练记录、数据分析和个性化健身指导。',
    KEYWORDS: ['健身', '训练', '管理', '数据分析', '智能', 'fitness', 'workout', 'tracking'],
    THEME_COLOR: '#0f172a'
  }
} as const;

// 导出类型定义
export type BrandConstant = typeof BRAND; 