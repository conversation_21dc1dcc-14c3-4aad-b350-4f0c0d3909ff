import { useState, useCallback, useMemo, useEffect } from 'react';
import { MuscleGroupEnum, MuscleVisualizationHook, iOSMuscleConfig } from '../types/muscle.types';
import { getIOSOptimalSize, getIOSSafeAreaInsets, shouldUseHardwareAcceleration } from '../utils/muscleUtils';
import { useCapacitorFeatures } from './useCapacitorFeatures';

/**
 * 肌肉可视化状态管理Hook
 * 提供肌肉选择、交互和iOS优化功能
 */
export const useMuscleVisualization = (
  initialMuscles: MuscleGroupEnum[] = []
): MuscleVisualizationHook => {
  const [selectedMuscles, setSelectedMuscles] = useState<MuscleGroupEnum[]>(initialMuscles);
  const [isLoading, setIsLoading] = useState(false);

  // iOS功能检测（此处未使用，但为保持一致性保留）
  // const { isNative, platform } = useCapacitorFeatures();

  // 切换肌肉群选择状态
  const toggleMuscle = useCallback((muscle: MuscleGroupEnum) => {
    setSelectedMuscles(prev => 
      prev.includes(muscle)
        ? prev.filter(m => m !== muscle)
        : [...prev, muscle]
    );
  }, []);

  // 清空所有选择
  const clearMuscles = useCallback(() => {
    setSelectedMuscles([]);
  }, []);

  // 设置肌肉群选择
  const setMuscles = useCallback((muscles: MuscleGroupEnum[]) => {
    setSelectedMuscles(muscles);
  }, []);

  // 计算选中肌肉数量
  const muscleCount = useMemo(() => selectedMuscles.length, [selectedMuscles]);

  // 检查是否包含特定肌肉
  const hasMuscle = useCallback((muscle: MuscleGroupEnum) => 
    selectedMuscles.includes(muscle), [selectedMuscles]
  );

  return {
    selectedMuscles,
    toggleMuscle,
    clearMuscles,
    setMuscles,
    muscleCount,
    hasMuscle,
    isLoading,
    setIsLoading
  };
};

/**
 * iOS优化配置Hook
 * 提供iOS设备特定的优化配置
 */
export const useiOSMuscleVisualization = (): iOSMuscleConfig => {
  const { isNative, platform } = useCapacitorFeatures();
  const [config, setConfig] = useState<iOSMuscleConfig>({
    optimalSize: 'md',
    useHardwareAcceleration: false,
    enableTouchFeedback: true,
    safeAreaInsets: { top: 0, bottom: 0, left: 0, right: 0 }
  });

  useEffect(() => {
    const updateConfig = () => {
      const newConfig: iOSMuscleConfig = {
        optimalSize: getIOSOptimalSize(),
        useHardwareAcceleration: shouldUseHardwareAcceleration(),
        enableTouchFeedback: isNative && platform === 'ios',
        safeAreaInsets: getIOSSafeAreaInsets()
      };
      
      setConfig(newConfig);
    };

    // 初始设置
    updateConfig();

    // 监听窗口大小变化
    const handleResize = () => {
      updateConfig();
    };

    // 监听方向变化
    const handleOrientationChange = () => {
      setTimeout(updateConfig, 100); // 延迟以确保获取正确的尺寸
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, [isNative, platform]);

  return config;
};

/**
 * 肌肉动画管理Hook
 * 处理肌肉群选择的动画效果
 */
export const useMuscleAnimation = (selectedMuscles: MuscleGroupEnum[]) => {
  const [animatingMuscles, setAnimatingMuscles] = useState<Set<MuscleGroupEnum>>(new Set());

  // 开始动画
  const startAnimation = useCallback((muscle: MuscleGroupEnum) => {
    setAnimatingMuscles(prev => new Set(prev).add(muscle));
    
    // 动画结束后移除
    setTimeout(() => {
      setAnimatingMuscles(prev => {
        const next = new Set(prev);
        next.delete(muscle);
        return next;
      });
    }, 300); // 与CSS动画时长一致
  }, []);

  // 批量开始动画
  const startBatchAnimation = useCallback((muscles: MuscleGroupEnum[]) => {
    muscles.forEach((muscle, index) => {
      setTimeout(() => startAnimation(muscle), index * 50); // 50ms间隔
    });
  }, [startAnimation]);

  // 检查肌肉是否在动画中
  const isAnimating = useCallback((muscle: MuscleGroupEnum) => 
    animatingMuscles.has(muscle), [animatingMuscles]
  );

  // 当选中肌肉发生变化时触发动画
  useEffect(() => {
    if (selectedMuscles.length > 0) {
      startBatchAnimation(selectedMuscles);
    }
  }, [selectedMuscles, startBatchAnimation]);

  return {
    animatingMuscles,
    startAnimation,
    startBatchAnimation,
    isAnimating
  };
};

/**
 * 肌肉搜索和筛选Hook
 * 提供肌肉群的搜索和筛选功能
 */
export const useMuscleSearch = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'upper' | 'core' | 'lower'>('all');

  // 所有肌肉群
  const allMuscles = useMemo(() => Object.values(MuscleGroupEnum), []);

  // 根据分类筛选肌肉
  const musclesByCategory = useMemo(() => {
    const categories = {
      upper: [
        MuscleGroupEnum.CHEST, MuscleGroupEnum.SHOULDERS, MuscleGroupEnum.BICEPS,
        MuscleGroupEnum.TRICEPS, MuscleGroupEnum.FOREARMS, MuscleGroupEnum.BACK,
        MuscleGroupEnum.LATS, MuscleGroupEnum.TRAPS
      ],
      core: [
        MuscleGroupEnum.ABDOMINALS, MuscleGroupEnum.OBLIQUES, MuscleGroupEnum.LOWER_BACK
      ],
      lower: [
        MuscleGroupEnum.QUADRICEPS, MuscleGroupEnum.HAMSTRINGS, 
        MuscleGroupEnum.CALVES, MuscleGroupEnum.GLUTES
      ]
    };

    if (selectedCategory === 'all') return allMuscles;
    return categories[selectedCategory] || [];
  }, [selectedCategory, allMuscles]);

  // 根据搜索词过滤肌肉
  const filteredMuscles = useMemo(() => {
    if (!searchTerm.trim()) return musclesByCategory;

    return musclesByCategory.filter(muscle => {
      const muscleNames: Partial<Record<MuscleGroupEnum, string[]>> = {
        [MuscleGroupEnum.CHEST]: ['胸部', '胸大肌', '胸肌'],
        [MuscleGroupEnum.BACK]: ['背部', '后背'],
        [MuscleGroupEnum.SHOULDERS]: ['肩部', '三角肌'],
        [MuscleGroupEnum.SHOULDERS_FRONT]: ['前肩', '前三角肌'],
        [MuscleGroupEnum.SHOULDERS_BACK]: ['后肩', '后三角肌'],
        [MuscleGroupEnum.BICEPS]: ['二头肌', '肱二头肌'],
        [MuscleGroupEnum.TRICEPS]: ['三头肌', '肱三头肌'],
        [MuscleGroupEnum.FOREARMS]: ['前臂', '前臂肌'],
        [MuscleGroupEnum.FOREARMS_FRONT]: ['前臂前侧'],
        [MuscleGroupEnum.FOREARMS_BACK]: ['前臂后侧'],
        [MuscleGroupEnum.ABDOMINALS]: ['腹肌', '核心', '腹部'],
        [MuscleGroupEnum.QUADRICEPS]: ['股四头肌', '四头肌', '大腿前侧'],
        [MuscleGroupEnum.HAMSTRINGS]: ['腘绳肌', '大腿后侧'],
        [MuscleGroupEnum.CALVES]: ['小腿', '小腿肌', '腓肠肌'],
        [MuscleGroupEnum.CALVES_FRONT]: ['小腿前侧'],
        [MuscleGroupEnum.CALVES_BACK]: ['小腿后侧'],
        [MuscleGroupEnum.GLUTES]: ['臀部', '臀大肌', '臀肌'],
        [MuscleGroupEnum.TRAPS]: ['斜方肌'],
        [MuscleGroupEnum.LATS]: ['背阔肌'],
        [MuscleGroupEnum.LOWER_BACK]: ['下背部', '下背'],
        [MuscleGroupEnum.OBLIQUES]: ['腹斜肌', '侧腹']
      };

      const names = muscleNames[muscle] || [muscle];
      const term = searchTerm.toLowerCase().trim();
      
      return names.some(name => name.toLowerCase().includes(term));
    });
  }, [searchTerm, musclesByCategory]);

  // 清空搜索
  const clearSearch = useCallback(() => {
    setSearchTerm('');
  }, []);

  return {
    searchTerm,
    setSearchTerm,
    selectedCategory,
    setSelectedCategory,
    filteredMuscles,
    clearSearch,
    allMuscles
  };
}; 