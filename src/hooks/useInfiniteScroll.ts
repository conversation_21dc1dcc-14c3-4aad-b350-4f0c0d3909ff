import { useEffect, useRef, useCallback, useState } from 'react';

interface UseInfiniteScrollOptions {
  hasMore: boolean;
  loading: boolean;
  onLoadMore: () => void;
  rootMargin?: string;
  threshold?: number;
}

interface UseInfiniteScrollReturn {
  sentinelRef: React.RefObject<HTMLDivElement>;
  isIntersecting: boolean;
}

/**
 * 无限滚动自定义Hook
 * 使用Intersection Observer API实现高性能的滚动检测
 */
export const useInfiniteScroll = ({
  hasMore,
  loading,
  onLoadMore,
  rootMargin = '100px',
  threshold = 0.1
}: UseInfiniteScrollOptions): UseInfiniteScrollReturn => {
  const sentinelRef = useRef<HTMLDivElement>(null);
  const [isIntersecting, setIsIntersecting] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const handleIntersection = useCallback((entries: IntersectionObserverEntry[]) => {
    const [entry] = entries;
    const isCurrentlyIntersecting = entry.isIntersecting;
    
    console.log('🔍 Intersection Observer触发:', {
      isIntersecting: isCurrentlyIntersecting,
      intersectionRatio: entry.intersectionRatio,
      hasMore,
      loading,
      boundingClientRect: entry.boundingClientRect,
      rootBounds: entry.rootBounds
    });
    
    setIsIntersecting(isCurrentlyIntersecting);
    
    if (isCurrentlyIntersecting && hasMore && !loading) {
      console.log('✅ 触发增量加载');
      onLoadMore();
    } else {
      console.log('❌ 跳过增量加载:', {
        isIntersecting: isCurrentlyIntersecting,
        hasMore,
        loading
      });
    }
  }, [hasMore, loading, onLoadMore]);

  useEffect(() => {
    const sentinel = sentinelRef.current;
    if (!sentinel) {
      console.warn('⚠️ 哨兵元素不存在，无法设置Intersection Observer');
      return;
    }

    console.log('🔧 初始化Intersection Observer:', {
      sentinel,
      rootMargin,
      threshold,
      hasMore,
      loading
    });

    // 创建Intersection Observer
    observerRef.current = new IntersectionObserver(handleIntersection, {
      rootMargin,
      threshold
    });

    observerRef.current.observe(sentinel);
    
    console.log('✅ Intersection Observer已启动');

    return () => {
      if (observerRef.current) {
        console.log('🔄 清理Intersection Observer');
        observerRef.current.disconnect();
      }
    };
  }, [handleIntersection, rootMargin, threshold]);

  return {
    sentinelRef,
    isIntersecting
  };
};

/**
 * 防抖Hook
 * 用于防止频繁的API调用
 */
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * 图片懒加载Hook
 */
export const useLazyImage = (src: string): { 
  imageSrc: string | null; 
  imageRef: React.RefObject<HTMLImageElement>;
  isLoaded: boolean;
} => {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const imageElement = imageRef.current;
    if (!imageElement) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setImageSrc(src);
          observer.unobserve(imageElement);
        }
      },
      { threshold: 0.1 }
    );

    observer.observe(imageElement);

    return () => {
      observer.disconnect();
    };
  }, [src]);

  const handleImageLoad = useCallback(() => {
    setIsLoaded(true);
  }, []);

  useEffect(() => {
    const imageElement = imageRef.current;
    if (imageElement && imageSrc) {
      imageElement.addEventListener('load', handleImageLoad);
      return () => {
        imageElement.removeEventListener('load', handleImageLoad);
      };
    }
  }, [imageSrc, handleImageLoad]);

  return { imageSrc, imageRef, isLoaded };
};