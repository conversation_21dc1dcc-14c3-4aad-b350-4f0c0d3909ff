/**
 * useExerciseData Hook
 * 封装Exercise数据管理逻辑，提供统一的数据接口
 */

import { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { Exercise } from '../utils/exerciseDataMapper';
import { exerciseService, ExerciseFilters } from '../services/exerciseService';
import { useDebounce } from './useInfiniteScroll';

export interface UseExerciseDataOptions {
  initialFilters?: ExerciseFilters;
  enableAutoLoad?: boolean;
  debounceMs?: number;
}

export interface UseExerciseDataReturn {
  // 数据状态
  exercises: Exercise[];
  loading: boolean;
  loadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  
  // 操作方法
  loadExercises: (filters?: ExerciseFilters, forceRefresh?: boolean) => Promise<void>;
  loadMoreExercises: () => Promise<void>;
  refreshData: () => Promise<void>;
  clearData: () => void;
  
  // 筛选方法
  setFilters: (filters: ExerciseFilters) => void;
  currentFilters: ExerciseFilters;
  
  // 缓存管理
  clearCache: (filters?: ExerciseFilters) => void;
  getCacheStats: () => { totalEntries: number; validEntries: number; expiredEntries: number };
}

export const useExerciseData = (options: UseExerciseDataOptions = {}): UseExerciseDataReturn => {
  const {
    initialFilters = {},
    enableAutoLoad = true,
    debounceMs = 300
  } = options;

  // 状态管理
  const [exercises, setExercises] = useState<Exercise[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [currentFilters, setCurrentFilters] = useState<ExerciseFilters>(initialFilters);

  // 🔧 修复：区分搜索词和其他筛选条件的防抖处理
  // 只对搜索词进行防抖，其他筛选条件立即生效
  const debouncedSearchTerm = useDebounce(currentFilters.searchTerm || '', debounceMs);

  // 构建实际使用的筛选条件：搜索词使用防抖值，其他条件立即生效
  const effectiveFilters = useMemo(() => ({
    ...currentFilters,
    searchTerm: debouncedSearchTerm
  }), [currentFilters, debouncedSearchTerm]);

  // 防止重复请求
  const loadingRef = useRef(false);

  /**
   * 加载动作数据
   */
  const loadExercises = useCallback(async (
    filters: ExerciseFilters = effectiveFilters,
    forceRefresh: boolean = false
  ) => {
    if (loadingRef.current) {
      console.log('🚫 请求进行中，跳过重复加载');
      return;
    }

    try {
      loadingRef.current = true;
      setLoading(true);
      setError(null);

      console.log('🔄 开始加载Exercise数据:', {
        filters,
        forceRefresh,
        effectiveFilters,
        currentFilters
      });

      const result = await exerciseService.getExercises(filters, 0, forceRefresh);

      setExercises(result.exercises);
      setHasMore(result.hasMore);

      console.log('✅ Exercise数据加载完成:', {
        count: result.exercises.length,
        hasMore: result.hasMore,
        appliedFilters: filters
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载动作数据失败';
      setError(errorMessage);
      console.error('❌ Exercise数据加载失败:', err);
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  }, [effectiveFilters]);

  /**
   * 加载更多数据
   */
  const loadMoreExercises = useCallback(async () => {
    if (loadingMore || !hasMore || loadingRef.current) {
      console.log('🚫 跳过加载更多:', { 
        loadingMore, 
        hasMore, 
        isLoading: loadingRef.current 
      });
      return;
    }

    try {
      loadingRef.current = true;
      setLoadingMore(true);
      setError(null);

      console.log('📄 开始加载更多数据:', {
        currentCount: exercises.length,
        filters: effectiveFilters
      });

      const result = await exerciseService.loadMore(effectiveFilters, exercises);
      
      setExercises(result.exercises);
      setHasMore(result.hasMore);

      console.log('✅ 更多数据加载完成:', {
        newCount: result.exercises.length,
        added: result.exercises.length - exercises.length,
        hasMore: result.hasMore
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载更多数据失败';
      setError(errorMessage);
      console.error('❌ 加载更多数据失败:', err);
    } finally {
      setLoadingMore(false);
      loadingRef.current = false;
    }
  }, [loadingMore, hasMore, exercises, effectiveFilters]);

  /**
   * 刷新数据
   */
  const refreshData = useCallback(async () => {
    exerciseService.clearCache(effectiveFilters);
    await loadExercises(effectiveFilters, true);
  }, [effectiveFilters, loadExercises]);

  /**
   * 清空数据
   */
  const clearData = useCallback(() => {
    setExercises([]);
    setHasMore(true);
    setError(null);
    setLoading(false);
    setLoadingMore(false);
  }, []);

  /**
   * 设置筛选条件
   */
  const setFilters = useCallback((filters: ExerciseFilters) => {
    console.log('🎯 设置新的筛选条件:', filters);
    setCurrentFilters(filters);
    clearData(); // 清空当前数据，等待新数据加载
  }, [clearData]);

  /**
   * 缓存管理方法
   */
  const clearCache = useCallback((filters?: ExerciseFilters) => {
    exerciseService.clearCache(filters);
  }, []);

  const getCacheStats = useCallback(() => {
    return exerciseService.getCacheStats();
  }, []);

  // 🔧 修复：当有效筛选条件变化时，自动加载数据
  useEffect(() => {
    if (enableAutoLoad) {
      console.log('🔄 筛选条件变化，重新加载数据:', {
        effectiveFilters,
        currentFilters,
        debouncedSearchTerm
      });
      loadExercises(effectiveFilters);
    }
  }, [effectiveFilters, enableAutoLoad, loadExercises]);

  // 定期清理过期缓存
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      exerciseService.cleanupExpiredCache();
    }, 5 * 60 * 1000); // 每5分钟清理一次

    return () => clearInterval(cleanupInterval);
  }, []);

  return {
    // 数据状态
    exercises,
    loading,
    loadingMore,
    error,
    hasMore,
    
    // 操作方法
    loadExercises,
    loadMoreExercises,
    refreshData,
    clearData,
    
    // 筛选方法
    setFilters,
    currentFilters: effectiveFilters,
    
    // 缓存管理
    clearCache,
    getCacheStats
  };
}; 