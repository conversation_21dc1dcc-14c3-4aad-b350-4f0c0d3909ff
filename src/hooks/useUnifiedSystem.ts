import { useCallback } from 'react';
import { useTheme, type Theme } from '../contexts/ThemeContext';  // 🔧 使用现有的ThemeContext系统
import { useLayout, type LayoutHook } from './useLayout';
import { useiOSStatusBar, type iOSStatusBarHook } from './useiOSStatusBar';

// 🔧 ThemeContext接口适配
interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

/**
 * 统一系统Hook返回类型
 */
export interface UnifiedSystemHook {
  // 🎨 主题相关
  theme: Theme;
  isLight: boolean;
  isDark: boolean;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
  
  // 📱 布局相关
  layoutReady: boolean;
  setupLayout: () => void;
  resetLayout: () => void;
  
  // 🍎 iOS相关
  platform: string;
  isIOSSupported: boolean;
  
  // 🛠️ 系统操作
  resetSystem: () => void;
  getSystemState: () => SystemState;
}

/**
 * 系统状态类型
 */
export interface SystemState {
  theme: Theme;
  layoutReady: boolean;
  platform: string;
  isIOSSupported: boolean;
  timestamp: number;
}

/**
 * 🎯 统一但模块化的系统管理Hook
 * 
 * 设计理念：
 * - ✅ 组合优于继承：通过组合现有Hook实现功能
 * - ✅ 保持模块化：每个功能仍然可以独立使用
 * - ✅ 统一接口：提供便捷的操作方法
 * - ✅ 可选使用：需要时才使用，不强制
 * 
 * 核心功能：
 * - 集成主题管理 (useTheme)
 * - 集成布局管理 (useLayout)  
 * - 集成iOS状态栏 (useiOSStatusBar)
 * - 提供统一的系统操作接口
 * 
 * @example
 * ```typescript
 * // 🚀 一键启用完整系统
 * function App() {
 *   const system = useUnifiedSystem();
 *   
 *   return (
 *     <div className={`app theme-${system.theme}`}>
 *       <header>
 *         <button onClick={system.toggleTheme}>
 *           {system.isDark ? '🌞' : '🌙'}
 *         </button>
 *       </header>
 *       
 *       {system.layoutReady && <AppContent />}
 *     </div>
 *   );
 * }
 * ```
 */
export function useUnifiedSystem(): UnifiedSystemHook {
  // 📦 组合现有模块，而不是重新实现
  const themeHook: ThemeContextType = useTheme();
  const layoutHook: LayoutHook = useLayout();
  const statusBarHook: iOSStatusBarHook = useiOSStatusBar(themeHook.theme);

  // 🔄 统一的系统重置操作
  const resetSystem = useCallback(() => {
    try {
      console.log('🔄 开始重置iOS主题和布局系统...');
      
      // 重置布局
      layoutHook.resetLayout();
      
      // 短暂延迟后重新设置
      setTimeout(() => {
        layoutHook.setupLayout();
        console.log('✅ iOS系统重置完成');
      }, 100);
    } catch (error) {
      console.error('❌ 系统重置失败:', error);
    }
  }, [layoutHook]);

  // 📊 获取完整系统状态
  const getSystemState = useCallback((): SystemState => {
    return {
      theme: themeHook.theme,
      layoutReady: layoutHook.isReady,
      platform: statusBarHook.platform,
      isIOSSupported: statusBarHook.isSupported,
      timestamp: Date.now()
    };
  }, [themeHook.theme, layoutHook.isReady, statusBarHook.platform, statusBarHook.isSupported]);

  // 🚀 返回统一的系统接口
  return {
    // 🎨 主题相关（手动计算isLight/isDark）
    theme: themeHook.theme,
    isLight: themeHook.theme === 'light',
    isDark: themeHook.theme === 'dark',
    toggleTheme: themeHook.toggleTheme,
    setTheme: themeHook.setTheme,
    
    // 📱 布局相关（直接暴露useLayout的接口）
    layoutReady: layoutHook.isReady,
    setupLayout: layoutHook.setupLayout,
    resetLayout: layoutHook.resetLayout,
    
    // 🍎 iOS相关（直接暴露useiOSStatusBar的接口）
    platform: statusBarHook.platform,
    isIOSSupported: statusBarHook.isSupported,
    
    // 🛠️ 系统级操作（新增的便捷方法）
    resetSystem,
    getSystemState
  };
} 