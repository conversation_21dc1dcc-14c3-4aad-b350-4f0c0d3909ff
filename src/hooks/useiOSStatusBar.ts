import { useEffect } from 'react';
import { Capacitor } from '@capacitor/core';
import { StatusBar, Style } from '@capacitor/status-bar';
import type { Theme } from '../contexts/ThemeContext';

/**
 * iOS状态栏Hook返回类型
 */
export interface iOSStatusBarHook {
  platform: string;
  isSupported: boolean;
}

/**
 * 📱 极简的iOS状态栏管理Hook
 * 
 * 功能特点：
 * - ✅ 自动同步，主题变化时自动更新状态栏
 * - ✅ 平台检测，自动判断是否为iOS设备
 * - ✅ 错误处理，API调用失败不会崩溃
 * - ✅ 无侵入，不影响其他功能
 * 
 * 工作原理：
 * - 监听主题变化，自动调用Capacitor StatusBar API
 * - 暗色主题 -> Style.Dark (浅色文字)
 * - 浅色主题 -> Style.Light (深色文字)
 * - 非iOS设备自动跳过，不执行任何操作
 * 
 * 重要提醒：
 * - iOS不支持setBackgroundColor()，背景色通过CSS处理
 * - 只能设置文字颜色（Style.Light/Dark）
 * 
 * @param theme 当前主题 ('light' | 'dark')
 * 
 * @example
 * ```typescript
 * function App() {
 *   const { theme } = useTheme();
 *   const statusBar = useiOSStatusBar(theme);
 *   
 *   return <AppContent />;
 * }
 * ```
 */
export function useiOSStatusBar(theme: Theme): iOSStatusBarHook {
  const platform = Capacitor.getPlatform();
  const isSupported = Capacitor.isNativePlatform() && platform === 'ios';

  // 🎯 自动响应主题变化
  useEffect(() => {
    // 🚫 非iOS设备直接返回
    if (!isSupported) {
      console.log('🌐 非iOS平台，跳过状态栏设置');
      return;
    }

    const updateStatusBar = async () => {
      try {
        // 🌙 暗色主题 = 浅色文字 (Style.Dark)
        // 🌞 浅色主题 = 深色文字 (Style.Light)
        // 根据Capacitor官方文档：
        // Style.Dark = 浅色文字，用于深色背景
        // Style.Light = 深色文字，用于浅色背景
        const style = theme === 'dark' ? Style.Dark : Style.Light;
        
        console.log(`🎨 设置iOS状态栏文字颜色: ${theme}主题 -> ${style}`);
        
        await StatusBar.setStyle({ style });
        
        // 确保状态栏显示
        await StatusBar.show();
        
        console.log('✅ iOS状态栏文字颜色设置完成');
      } catch (error) {
        // 🔧 容错处理：API调用失败不应该影响应用运行
        console.warn('⚠️ StatusBar API调用失败（这不会影响应用功能）:', error);
        
        // 检查是否是"not implemented"错误
        if (error && typeof error === 'object' && 'message' in error) {
          const message = (error as Error).message;
          if (message.includes('not implemented')) {
            console.warn('💡 提示：当前API在此平台不支持，这是正常情况');
          }
        }
      }
    };

    // 执行状态栏更新
    updateStatusBar();
  }, [theme, isSupported]);

  // 📋 组件挂载时输出调试信息
  useEffect(() => {
    if (isSupported) {
      console.log(`🍎 初始化iOS状态栏管理器，当前主题: ${theme}`);
    }
  }, []); // 只在组件挂载时执行一次

  return {
    platform,
    isSupported
  };
} 