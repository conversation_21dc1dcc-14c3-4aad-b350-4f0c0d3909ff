/**
 * 网络诊断Hook
 * 专门用于检测和诊断iOS端网络连接问题
 */

import { useState, useEffect, useCallback } from 'react';
import { Capacitor } from '@capacitor/core';
import { Network } from '@capacitor/network';
import { getApiConfig, getPlatformNetworkConfig } from '../config/api.config';

// 网络诊断状态
export interface NetworkDiagnostics {
  isOnline: boolean;
  connectionType: string;
  platform: string;
  isNative: boolean;
  serverReachable: boolean;
  httpAllowed: boolean;
  lastError: string | null;
  diagnosticMessages: string[];
  suggestions: string[];
}

// 诊断结果
export interface DiagnosticResult {
  success: boolean;
  message: string;
  details?: any;
  suggestions?: string[];
}

export const useNetworkDiagnostics = () => {
  const [diagnostics, setDiagnostics] = useState<NetworkDiagnostics>({
    isOnline: true,
    connectionType: 'unknown',
    platform: 'web',
    isNative: false,
    serverReachable: false,
    httpAllowed: false,
    lastError: null,
    diagnosticMessages: [],
    suggestions: []
  });

  const [isRunningDiagnostics, setIsRunningDiagnostics] = useState(false);
  const config = getApiConfig();
  const platformConfig = getPlatformNetworkConfig();

  // 检查网络连接状态
  const checkNetworkStatus = useCallback(async (): Promise<DiagnosticResult> => {
    try {
      if (Capacitor.isNativePlatform()) {
        const status = await Network.getStatus();
        return {
          success: status.connected,
          message: status.connected ? '网络连接正常' : '网络连接断开',
          details: {
            connectionType: status.connectionType,
            connected: status.connected
          }
        };
      } else {
        const online = navigator.onLine;
        return {
          success: online,
          message: online ? '网络连接正常' : '网络连接断开',
          details: { online }
        };
      }
    } catch (error) {
      return {
        success: false,
        message: '无法检查网络状态',
        details: error
      };
    }
  }, []);

  // 检查服务器可达性
  const checkServerReachability = useCallback(async (): Promise<DiagnosticResult> => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch(`${config.baseURL}/health`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        return {
          success: true,
          message: '服务器连接正常',
          details: {
            status: response.status,
            statusText: response.statusText
          }
        };
      } else {
        return {
          success: false,
          message: `服务器响应异常: ${response.status}`,
          details: {
            status: response.status,
            statusText: response.statusText
          }
        };
      }
    } catch (error: any) {
      let message = '服务器连接失败';
      const suggestions: string[] = [];

      if (error.name === 'AbortError') {
        message = '服务器连接超时';
        suggestions.push('检查网络连接速度');
        suggestions.push('尝试使用其他网络');
      } else if (error.message.includes('Failed to fetch')) {
        message = '无法连接到服务器';
        if (platformConfig.platform === 'ios' && config.baseURL.startsWith('http:')) {
          suggestions.push('iOS检测到HTTP连接，请确保Info.plist已配置App Transport Security');
          suggestions.push('建议将服务器升级为HTTPS');
        }
        suggestions.push('检查服务器地址是否正确');
        suggestions.push('检查防火墙设置');
      }

      return {
        success: false,
        message,
        details: error,
        suggestions
      };
    }
  }, [config.baseURL, platformConfig.platform]);

  // 检查HTTP协议支持
  const checkHttpSupport = useCallback(async (): Promise<DiagnosticResult> => {
    const isHttps = config.baseURL.startsWith('https:');
    const isIOS = platformConfig.platform === 'ios';

    if (isHttps) {
      return {
        success: true,
        message: '使用HTTPS协议，安全连接',
        details: { protocol: 'https' }
      };
    }

    if (isIOS) {
      return {
        success: false,
        message: 'iOS平台检测到HTTP连接',
        details: { protocol: 'http', platform: 'ios' },
        suggestions: [
          '检查Info.plist中的App Transport Security配置',
          '确保NSAllowsArbitraryLoads设置为true',
          '或者为特定域名配置NSExceptionDomains',
          '建议升级服务器为HTTPS以提高安全性'
        ]
      };
    }

    return {
      success: true,
      message: 'HTTP协议连接（非iOS平台）',
      details: { protocol: 'http', platform: platformConfig.platform },
      suggestions: ['建议升级服务器为HTTPS以提高安全性']
    };
  }, [config.baseURL, platformConfig.platform]);

  // 运行完整诊断
  const runDiagnostics = useCallback(async () => {
    setIsRunningDiagnostics(true);
    
    const messages: string[] = [];
    const suggestions: string[] = [];
    let serverReachable = false;
    let httpAllowed = true;

    try {
      // 1. 检查网络状态
      const networkResult = await checkNetworkStatus();
      messages.push(`🌐 网络状态: ${networkResult.message}`);
      
      if (!networkResult.success) {
        suggestions.push('请检查设备网络连接');
        setDiagnostics(prev => ({
          ...prev,
          isOnline: false,
          diagnosticMessages: messages,
          suggestions,
          lastError: networkResult.message
        }));
        return;
      }

      // 2. 检查HTTP协议支持
      const httpResult = await checkHttpSupport();
      messages.push(`🔒 协议检查: ${httpResult.message}`);
      httpAllowed = httpResult.success;
      
      if (httpResult.suggestions) {
        suggestions.push(...httpResult.suggestions);
      }

      // 3. 检查服务器可达性
      const serverResult = await checkServerReachability();
      messages.push(`🖥️ 服务器连接: ${serverResult.message}`);
      serverReachable = serverResult.success;
      
      if (serverResult.suggestions) {
        suggestions.push(...serverResult.suggestions);
      }

      // 4. 平台特定检查
      if (platformConfig.platform === 'ios') {
        messages.push('📱 iOS平台特定检查完成');
        if (!httpAllowed && !serverReachable) {
          suggestions.push('iOS平台HTTP连接被阻止，请按照上述建议配置App Transport Security');
        }
      }

      setDiagnostics(prev => ({
        ...prev,
        isOnline: networkResult.success,
        connectionType: networkResult.details?.connectionType || 'unknown',
        platform: platformConfig.platform,
        isNative: platformConfig.isNative,
        serverReachable,
        httpAllowed,
        diagnosticMessages: messages,
        suggestions: [...new Set(suggestions)], // 去重
        lastError: serverReachable ? null : serverResult.message
      }));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '诊断过程中发生未知错误';
      messages.push(`❌ 诊断错误: ${errorMessage}`);
      
      setDiagnostics(prev => ({
        ...prev,
        diagnosticMessages: messages,
        lastError: errorMessage
      }));
    } finally {
      setIsRunningDiagnostics(false);
    }
  }, [checkNetworkStatus, checkHttpSupport, checkServerReachability, platformConfig]);

  // 快速连接测试
  const quickConnectionTest = useCallback(async (): Promise<boolean> => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000);

      await fetch(`${config.baseURL}`, {
        method: 'HEAD',
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return true;
    } catch {
      return false;
    }
  }, [config.baseURL]);

  // 获取网络修复建议
  const getFixSuggestions = useCallback((): string[] => {
    const suggestions: string[] = [];

    if (!diagnostics.isOnline) {
      suggestions.push('1. 检查设备网络连接');
      suggestions.push('2. 尝试切换WiFi或移动网络');
    }

    if (diagnostics.platform === 'ios' && !diagnostics.httpAllowed) {
      suggestions.push('3. iOS HTTP连接配置:');
      suggestions.push('   - 在Info.plist中添加NSAppTransportSecurity配置');
      suggestions.push('   - 设置NSAllowsArbitraryLoads为true');
      suggestions.push('   - 或为124.222.91.101配置域名例外');
    }

    if (!diagnostics.serverReachable) {
      suggestions.push('4. 检查服务器配置:');
      suggestions.push('   - 确认服务器地址: 124.222.91.101:8000');
      suggestions.push('   - 检查服务器防火墙设置');
      suggestions.push('   - 确认服务器正在运行');
    }

    suggestions.push('5. 运行完整诊断以获取详细信息');

    return suggestions;
  }, [diagnostics]);

  // 组件挂载时进行初始检查
  useEffect(() => {
    runDiagnostics();
  }, [runDiagnostics]);

  // 监听网络状态变化（仅原生平台）
  useEffect(() => {
    if (!Capacitor.isNativePlatform()) return;

    const networkListener = Network.addListener('networkStatusChange', (status) => {
      setDiagnostics(prev => ({
        ...prev,
        isOnline: status.connected,
        connectionType: status.connectionType
      }));

      // 网络恢复时重新检查服务器
      if (status.connected) {
        setTimeout(() => {
          checkServerReachability();
        }, 1000);
      }
    });

    return () => {
      networkListener.remove();
    };
  }, [checkServerReachability]);

  return {
    diagnostics,
    isRunningDiagnostics,
    runDiagnostics,
    quickConnectionTest,
    getFixSuggestions,
    checkNetworkStatus,
    checkServerReachability,
    checkHttpSupport
  };
}; 