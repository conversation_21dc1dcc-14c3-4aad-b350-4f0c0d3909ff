/**
 * FitMaster 离线数据同步Hook
 * 处理网络异常时的数据缓存、队列管理和自动重试同步
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { apiService } from '../services/api'

// 同步队列项类型
export interface SyncQueueItem {
  id: string
  endpoint: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  timestamp: number
  retryCount: number
  maxRetries: number
  priority: 'high' | 'medium' | 'low'
}

// 同步状态类型
export interface SyncStatus {
  isOnline: boolean
  isSyncing: boolean
  pendingCount: number
  failedCount: number
  lastSyncTime: Date | null
}

// 缓存项类型
export interface CacheItem<T = any> {
  data: T
  timestamp: number
  expiry: number
  key: string
}

const SYNC_QUEUE_KEY = 'fitmaster_sync_queue'
const CACHE_PREFIX = 'fitmaster_cache_'
const MAX_CACHE_SIZE = 100 // 最大缓存条目数
const DEFAULT_CACHE_EXPIRY = 5 * 60 * 1000 // 5分钟默认缓存时间

export const useOfflineSync = () => {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isOnline: navigator.onLine,
    isSyncing: false,
    pendingCount: 0,
    failedCount: 0,
    lastSyncTime: null
  })

  const syncIntervalRef = useRef<number>()
  const retryTimeoutRef = useRef<number>()

  /**
   * 网络状态监听
   */
  useEffect(() => {
    const handleOnline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: true }))
      startAutoSync()
    }

    const handleOffline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: false }))
      stopAutoSync()
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // 初始化时检查网络状态
    if (navigator.onLine) {
      startAutoSync()
    }

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      stopAutoSync()
    }
  }, [])

  /**
   * 同步队列管理
   */
  const getSyncQueue = (): SyncQueueItem[] => {
    try {
      const queue = localStorage.getItem(SYNC_QUEUE_KEY)
      return queue ? JSON.parse(queue) : []
    } catch (error) {
      console.warn('Failed to parse sync queue:', error)
      return []
    }
  }

  const saveSyncQueue = (queue: SyncQueueItem[]): void => {
    try {
      localStorage.setItem(SYNC_QUEUE_KEY, JSON.stringify(queue))
      updateSyncStatus()
    } catch (error) {
      console.error('Failed to save sync queue:', error)
    }
  }

  const addToSyncQueue = (item: Omit<SyncQueueItem, 'id' | 'timestamp' | 'retryCount'>): void => {
    const queue = getSyncQueue()
    const newItem: SyncQueueItem = {
      ...item,
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      retryCount: 0
    }

    // 按优先级排序插入
    const priorityOrder = { high: 0, medium: 1, low: 2 }
    const insertIndex = queue.findIndex(
      queueItem => priorityOrder[queueItem.priority] > priorityOrder[newItem.priority]
    )

    if (insertIndex === -1) {
      queue.push(newItem)
    } else {
      queue.splice(insertIndex, 0, newItem)
    }

    saveSyncQueue(queue)
  }

  // const removeFromSyncQueue = (itemId: string): void => {
  //   const queue = getSyncQueue().filter(item => item.id !== itemId)
  //   saveSyncQueue(queue)
  // }

  const updateSyncStatus = (): void => {
    const queue = getSyncQueue()
    const pendingCount = queue.length
    const failedCount = queue.filter(item => item.retryCount >= item.maxRetries).length

    setSyncStatus(prev => ({
      ...prev,
      pendingCount,
      failedCount
    }))
  }

  /**
   * 缓存管理
   */
  const getCacheKey = (key: string): string => `${CACHE_PREFIX}${key}`

  const setCache = <T>(key: string, data: T, expiryMs = DEFAULT_CACHE_EXPIRY): void => {
    try {
      const cacheItem: CacheItem<T> = {
        data,
        timestamp: Date.now(),
        expiry: Date.now() + expiryMs,
        key
      }

      localStorage.setItem(getCacheKey(key), JSON.stringify(cacheItem))
      cleanupExpiredCache()
    } catch (error) {
      console.warn('Failed to set cache:', error)
      // 如果存储空间不足，清理旧缓存
      cleanupOldCache()
    }
  }

  const getCache = <T>(key: string): T | null => {
    try {
      const cached = localStorage.getItem(getCacheKey(key))
      if (!cached) return null

      const cacheItem: CacheItem<T> = JSON.parse(cached)
      
      // 检查是否过期
      if (Date.now() > cacheItem.expiry) {
        localStorage.removeItem(getCacheKey(key))
        return null
      }

      return cacheItem.data
    } catch (error) {
      console.warn('Failed to get cache:', error)
      return null
    }
  }

  const clearCache = (pattern?: string): void => {
    try {
      const keys = Object.keys(localStorage)
      const cacheKeys = keys.filter(key => {
        if (!key.startsWith(CACHE_PREFIX)) return false
        if (pattern) return key.includes(pattern)
        return true
      })

      cacheKeys.forEach(key => localStorage.removeItem(key))
    } catch (error) {
      console.warn('Failed to clear cache:', error)
    }
  }

  const cleanupExpiredCache = (): void => {
    try {
      const keys = Object.keys(localStorage)
      const cacheKeys = keys.filter(key => key.startsWith(CACHE_PREFIX))

      cacheKeys.forEach(key => {
        const cached = localStorage.getItem(key)
        if (cached) {
          try {
            const cacheItem: CacheItem = JSON.parse(cached)
            if (Date.now() > cacheItem.expiry) {
              localStorage.removeItem(key)
            }
          } catch (error) {
            localStorage.removeItem(key)
          }
        }
      })
    } catch (error) {
      console.warn('Failed to cleanup expired cache:', error)
    }
  }

  const cleanupOldCache = (): void => {
    try {
      const keys = Object.keys(localStorage)
      const cacheKeys = keys.filter(key => key.startsWith(CACHE_PREFIX))

      if (cacheKeys.length <= MAX_CACHE_SIZE) return

      // 按时间戳排序，删除最旧的缓存
      const cacheItems = cacheKeys.map(key => {
        const cached = localStorage.getItem(key)
        if (cached) {
          try {
            const cacheItem: CacheItem = JSON.parse(cached)
            return { key, timestamp: cacheItem.timestamp }
          } catch (error) {
            return { key, timestamp: 0 }
          }
        }
        return { key, timestamp: 0 }
      }).sort((a, b) => a.timestamp - b.timestamp)

      const itemsToDelete = cacheItems.slice(0, cacheItems.length - MAX_CACHE_SIZE)
      itemsToDelete.forEach(item => localStorage.removeItem(item.key))
    } catch (error) {
      console.warn('Failed to cleanup old cache:', error)
    }
  }

  /**
   * 同步执行
   */
  const processSyncQueue = async (): Promise<void> => {
    if (!navigator.onLine || syncStatus.isSyncing) return

    setSyncStatus(prev => ({ ...prev, isSyncing: true }))

    try {
      const queue = getSyncQueue()
      const itemsToSync = queue.filter(item => item.retryCount < item.maxRetries)

      if (itemsToSync.length === 0) {
        setSyncStatus(prev => ({
          ...prev,
          isSyncing: false,
          lastSyncTime: new Date()
        }))
        return
      }

      const results = await Promise.allSettled(
        itemsToSync.map(item => syncItem(item))
      )

      // 处理同步结果
      const updatedQueue = getSyncQueue()
      results.forEach((result, index) => {
        const item = itemsToSync[index]
        const queueIndex = updatedQueue.findIndex(q => q.id === item.id)

        if (result.status === 'fulfilled') {
          // 同步成功，从队列中移除
          if (queueIndex !== -1) {
            updatedQueue.splice(queueIndex, 1)
          }
        } else {
          // 同步失败，增加重试次数
          if (queueIndex !== -1) {
            updatedQueue[queueIndex].retryCount++
          }
        }
      })

      saveSyncQueue(updatedQueue)

      setSyncStatus(prev => ({
        ...prev,
        isSyncing: false,
        lastSyncTime: new Date()
      }))

    } catch (error) {
      console.error('Sync process failed:', error)
      setSyncStatus(prev => ({ ...prev, isSyncing: false }))
    }
  }

  const syncItem = async (item: SyncQueueItem): Promise<void> => {
    try {
      switch (item.method) {
        case 'GET':
          await apiService.publicRequest(item.endpoint)
          break
        case 'POST':
          await apiService.publicRequest(item.endpoint, {
            method: 'POST',
            body: JSON.stringify(item.data)
          })
          break
        case 'PUT':
          await apiService.publicRequest(item.endpoint, {
            method: 'PUT',
            body: JSON.stringify(item.data)
          })
          break
        case 'DELETE':
          await apiService.publicRequest(item.endpoint, {
            method: 'DELETE'
          })
          break
      }
    } catch (error) {
      throw new Error(`Failed to sync ${item.method} ${item.endpoint}: ${error}`)
    }
  }

  /**
   * 自动同步控制
   */
  const startAutoSync = (): void => {
    if (syncIntervalRef.current) return

    // 立即执行一次同步
    processSyncQueue()

    // 设置定时同步
    syncIntervalRef.current = window.setInterval(() => {
      processSyncQueue()
    }, 30000) // 每30秒同步一次
  }

  const stopAutoSync = (): void => {
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current)
      syncIntervalRef.current = undefined
    }
  }

  /**
   * 公共方法
   */
  const syncNow = useCallback((): Promise<void> => {
    return processSyncQueue()
  }, [])

  const clearSyncQueue = useCallback((): void => {
    localStorage.removeItem(SYNC_QUEUE_KEY)
    updateSyncStatus()
  }, [])

  const retryFailed = useCallback((): void => {
    const queue = getSyncQueue()
    const updatedQueue = queue.map(item => ({
      ...item,
      retryCount: 0 // 重置重试次数
    }))
    saveSyncQueue(updatedQueue)
    
    if (navigator.onLine) {
      processSyncQueue()
    }
  }, [])

  // 导出的API调用包装器，自动处理离线缓存
  const apiCall = useCallback(async <T>(
    endpoint: string,
    options: RequestInit = {},
    cacheKey?: string,
    priority: SyncQueueItem['priority'] = 'medium'
  ): Promise<T> => {
    try {
      if (!navigator.onLine) {
        // 离线状态，尝试从缓存获取数据
        if (cacheKey && options.method !== 'POST' && options.method !== 'PUT' && options.method !== 'DELETE') {
          const cachedData = getCache<T>(cacheKey)
          if (cachedData) {
            return cachedData
          }
        }
        throw new Error('Offline and no cached data available')
      }

      // 在线状态，正常API调用
      const response = await apiService.publicRequest<T>(endpoint, options)

      // 缓存GET请求的响应
      if (cacheKey && (!options.method || options.method === 'GET')) {
        setCache(cacheKey, response)
      }

      return response

    } catch (error) {
      // API调用失败，如果是非读取操作，加入同步队列
      if (options.method === 'POST' || options.method === 'PUT' || options.method === 'DELETE') {
        addToSyncQueue({
          endpoint,
          method: options.method as 'POST' | 'PUT' | 'DELETE',
          data: options.body ? JSON.parse(options.body as string) : undefined,
          maxRetries: 3,
          priority
        })
      }

      // 如果有缓存键且是读取操作，尝试返回缓存数据
      if (cacheKey && (!options.method || options.method === 'GET')) {
        const cachedData = getCache<T>(cacheKey)
        if (cachedData) {
          return cachedData
        }
      }

      throw error
    }
  }, [])

  // 清理资源
  useEffect(() => {
    return () => {
      if (syncIntervalRef.current) {
        clearInterval(syncIntervalRef.current)
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current)
      }
    }
  }, [])

  // 初始化时更新状态
  useEffect(() => {
    updateSyncStatus()
    cleanupExpiredCache()
  }, [])

  return {
    syncStatus,
    syncNow,
    clearSyncQueue,
    retryFailed,
    apiCall,
    setCache,
    getCache,
    clearCache
  }
}

export default useOfflineSync 