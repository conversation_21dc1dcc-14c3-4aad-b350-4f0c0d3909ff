import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

export type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>(() => {
    // 从本地存储获取主题设置，默认为明亮主题
    const savedTheme = localStorage.getItem('fitmaster-theme') as Theme;
    return savedTheme === 'dark' ? 'dark' : 'light';
  });

  // 应用主题到 DOM
  useEffect(() => {
    const root = document.documentElement;
    
    // 移除之前的主题类
    root.classList.remove('theme-light', 'theme-dark');
    
    // 添加当前主题类
    root.classList.add(`theme-${theme}`);
    
    // 设置 data 属性供 CSS 使用
    root.setAttribute('data-theme', theme);
    
    // 更新 meta theme-color
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute(
        'content', 
        theme === 'dark' ? '#0f172a' : '#ffffff'
      );
    }
  }, [theme]);

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
          localStorage.setItem('fitmaster-theme', newTheme);
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  const value: ThemeContextType = {
    theme,
    setTheme,
    toggleTheme,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
}; 