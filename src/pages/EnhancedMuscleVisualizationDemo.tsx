import React, { useState } from 'react';
import { MuscleGroupEnum, MUSCLE_DISPLAY_NAMES } from '../types/muscle.types';
import { 
  MuscleIllustration 
} from '../components/fitness/MuscleVisualization';
import '../styles/enhanced-muscle-visualization.scss';

/**
 * 🏋️‍♀️ 增强版肌肉可视化演示页面
 * 展示集成muscle-selector-complete后的效果对比
 */
const EnhancedMuscleVisualizationDemo: React.FC = () => {
  const [selectedMuscles, setSelectedMuscles] = useState<MuscleGroupEnum[]>([]);
  const [currentTab, setCurrentTab] = useState<'enhanced' | 'original' | 'comparison'>('enhanced');
  const [theme, setTheme] = useState<'light' | 'dark'>('light');

  const handleMuscleToggle = (muscle: MuscleGroupEnum) => {
    setSelectedMuscles(prev => 
      prev.includes(muscle) 
        ? prev.filter(m => m !== muscle)
        : [...prev, muscle]
    );
  };

  const handleQuickSelect = (muscles: MuscleGroupEnum[]) => {
    setSelectedMuscles(muscles);
  };

  const clearSelection = () => {
    setSelectedMuscles([]);
  };

  // 预设的肌肉群组合
  const musclePresets = {
    upperBodyPush: [MuscleGroupEnum.CHEST, MuscleGroupEnum.SHOULDERS, MuscleGroupEnum.TRICEPS],
    upperBodyPull: [MuscleGroupEnum.BACK, MuscleGroupEnum.LATS, MuscleGroupEnum.BICEPS],
    lowerBody: [MuscleGroupEnum.QUADRICEPS, MuscleGroupEnum.GLUTES, MuscleGroupEnum.HAMSTRINGS, MuscleGroupEnum.CALVES],
    core: [MuscleGroupEnum.ABDOMINALS, MuscleGroupEnum.OBLIQUES, MuscleGroupEnum.LOWER_BACK],
    fullBody: [
      MuscleGroupEnum.CHEST, MuscleGroupEnum.BACK, MuscleGroupEnum.SHOULDERS,
      MuscleGroupEnum.BICEPS, MuscleGroupEnum.TRICEPS, MuscleGroupEnum.QUADRICEPS,
      MuscleGroupEnum.HAMSTRINGS, MuscleGroupEnum.GLUTES
    ]
  };

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50'}`}>
      {/* 页面标题 */}
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">
            🏋️‍♀️ 增强版肌肉可视化演示
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-6">
            集成muscle-selector-complete的高精度人体解剖图
          </p>
          
          {/* 主题切换 */}
          <div className="flex justify-center gap-4 mb-6">
            <button
              onClick={() => setTheme('light')}
              className={`px-4 py-2 rounded-md transition-colors ${
                theme === 'light'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              ☀️ 浅色主题
            </button>
            <button
              onClick={() => setTheme('dark')}
              className={`px-4 py-2 rounded-md transition-colors ${
                theme === 'dark'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              🌙 深色主题
            </button>
          </div>
        </div>

        {/* 选项卡切换 */}
        <div className="flex justify-center mb-8">
          <div className="flex space-x-1 bg-gray-200 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setCurrentTab('enhanced')}
              className={`px-6 py-2 rounded-md transition-colors ${
                currentTab === 'enhanced'
                  ? 'bg-white dark:bg-gray-800 shadow-sm'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-600'
              }`}
            >
              ✨ 增强版
            </button>
            <button
              onClick={() => setCurrentTab('original')}
              className={`px-6 py-2 rounded-md transition-colors ${
                currentTab === 'original'
                  ? 'bg-white dark:bg-gray-800 shadow-sm'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-600'
              }`}
            >
              📝 原版
            </button>
            <button
              onClick={() => setCurrentTab('comparison')}
              className={`px-6 py-2 rounded-md transition-colors ${
                currentTab === 'comparison'
                  ? 'bg-white dark:bg-gray-800 shadow-sm'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-600'
              }`}
            >
              🔄 对比
            </button>
          </div>
        </div>

        {/* 已选择的肌肉群显示 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
          <h3 className="text-xl font-semibold mb-4">
            已选择的肌肉群 ({selectedMuscles.length}/15)
          </h3>
          <div className="flex flex-wrap gap-2 mb-4">
            {selectedMuscles.length > 0 ? (
              selectedMuscles.map(muscle => (
                <span
                  key={muscle}
                  className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium"
                >
                  {MUSCLE_DISPLAY_NAMES[muscle]}
                </span>
              ))
            ) : (
              <span className="text-gray-500 italic">
                请点击下方人体图选择肌肉群
              </span>
            )}
          </div>
          
          {/* 快速选择按钮 */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleQuickSelect(musclePresets.upperBodyPush)}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
            >
              上半身推
            </button>
            <button
              onClick={() => handleQuickSelect(musclePresets.upperBodyPull)}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors text-sm"
            >
              上半身拉
            </button>
            <button
              onClick={() => handleQuickSelect(musclePresets.lowerBody)}
              className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors text-sm"
            >
              下半身
            </button>
            <button
              onClick={() => handleQuickSelect(musclePresets.core)}
              className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors text-sm"
            >
              核心
            </button>
            <button
              onClick={() => handleQuickSelect(musclePresets.fullBody)}
              className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors text-sm"
            >
              全身
            </button>
            <button
              onClick={clearSelection}
              className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors text-sm"
            >
              清除
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          {currentTab === 'enhanced' && (
            <div>
              <h2 className="text-2xl font-semibold mb-4 text-center">
                ✨ 增强版肌肉可视化
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-center mb-6">
                使用muscle-selector-complete的高精度SVG路径，支持13个精准肌肉群
              </p>
              <div className="flex justify-center">
                <MuscleIllustration
                  selectedMuscles={selectedMuscles}
                  onToggleMuscle={handleMuscleToggle}
                  theme={theme}
                />
              </div>
            </div>
          )}

          {currentTab === 'original' && (
            <div>
              <h2 className="text-2xl font-semibold mb-4 text-center">
                📝 原版肌肉可视化
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-center mb-6">
                原有的肌肉可视化实现，支持15个肌肉群但精度较低
              </p>
              <div className="flex justify-center">
                <MuscleIllustration
                  selectedMuscles={selectedMuscles}
                  onToggleMuscle={handleMuscleToggle}
                  theme={theme}
                />
              </div>
            </div>
          )}

          {currentTab === 'comparison' && (
            <div>
              <h2 className="text-2xl font-semibold mb-6 text-center">
                🔄 功能对比
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                {/* 增强版 */}
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-center text-blue-600">
                    ✨ 增强版
                  </h3>
                  <div className="border-2 border-blue-200 rounded-lg p-4">
                    <MuscleIllustration
                      selectedMuscles={selectedMuscles}
                      onToggleMuscle={handleMuscleToggle}
                      theme={theme}
                    />
                  </div>
                  <div className="text-sm space-y-2">
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✅</span>
                      高精度SVG人体轮廓
                    </div>
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✅</span>
                      专业解剖学结构
                    </div>
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✅</span>
                      13个精准肌肉群
                    </div>
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✅</span>
                      优化的性能表现
                    </div>
                    <div className="flex items-center">
                      <span className="text-blue-500 mr-2">➕</span>
                      支持LATS和LOWER_BACK
                    </div>
                  </div>
                </div>

                {/* 原版 */}
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-center text-gray-600">
                    📝 原版
                  </h3>
                  <div className="border-2 border-gray-200 rounded-lg p-4">
                    <MuscleIllustration
                      selectedMuscles={selectedMuscles}
                      onToggleMuscle={handleMuscleToggle}
                      theme={theme}
                    />
                  </div>
                  <div className="text-sm space-y-2">
                    <div className="flex items-center">
                      <span className="text-yellow-500 mr-2">⚠️</span>
                      基础SVG实现
                    </div>
                    <div className="flex items-center">
                      <span className="text-yellow-500 mr-2">⚠️</span>
                      简化的肌肉结构
                    </div>
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✅</span>
                      15个肌肉群支持
                    </div>
                    <div className="flex items-center">
                      <span className="text-yellow-500 mr-2">⚠️</span>
                      性能待优化
                    </div>
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✅</span>
                      完整的肌肉群覆盖
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 技术说明 */}
        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">🔧 技术实现说明</h3>
          <div className="grid md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-medium mb-2">增强功能：</h4>
              <ul className="space-y-1 text-gray-600 dark:text-gray-300">
                <li>• 集成muscle-selector-complete的精确SVG路径</li>
                <li>• 统一的类型映射系统</li>
                <li>• 保持API接口完全兼容</li>
                <li>• 自定义LATS和LOWER_BACK组件</li>
                <li>• 响应式和主题支持</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">性能优化：</h4>
              <ul className="space-y-1 text-gray-600 dark:text-gray-300">
                <li>• iOS硬件加速支持</li>
                <li>• 使用useCallback和useMemo优化</li>
                <li>• 类型转换缓存机制</li>
                <li>• SVG路径矢量优化</li>
                <li>• 移动端触摸优化</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedMuscleVisualizationDemo; 