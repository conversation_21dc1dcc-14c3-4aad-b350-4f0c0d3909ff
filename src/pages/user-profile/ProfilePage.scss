// Profile Page Styles
// Based on FitMaster Design System

.profile-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

// Profile Header Section
.profile-header {
  background: linear-gradient(135deg, var(--bg-surface) 0%, var(--bg-card) 100%);
  border: 1px solid var(--primary-500, #334155);
  border-radius: var(--radius-lg, 0.5rem);
  padding: var(--space-8, 2rem);
  margin-bottom: var(--space-8, 2rem);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-500, #3b82f6), var(--success-500, #22c55e));
  }
}

.profile-header-content {
  display: flex;
  gap: var(--space-8, 2rem);
  align-items: flex-start;
}

// Avatar Section
.profile-avatar-section {
  position: relative;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4, 1rem);
}

.profile-avatar {
  width: 7.5rem; // 120px
  height: 7.5rem;
  border-radius: var(--radius-full, 9999px);
  overflow: hidden;
  border: 4px solid var(--accent-500, #3b82f6);
  position: relative;
  background: var(--primary-600, #1e293b);
  
  .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// Profile Info Section (under avatar)
.profile-info {
  text-align: center;
  
  .profile-name {
    font-size: var(--text-2xl, 1.5rem);
    font-weight: var(--font-bold, 700);
    color: var(--text-primary, #f8fafc);
    margin: 0 0 var(--space-1, 0.25rem) 0;
    line-height: 1.2;
  }
  
  .profile-username {
    font-size: var(--text-base, 1rem);
    color: var(--text-secondary, #94a3b8);
    margin: 0 0 var(--space-1, 0.25rem) 0;
    font-weight: var(--font-medium, 500);
  }
  
  .profile-fitness-level {
    font-size: var(--text-sm, 0.875rem);
    color: var(--accent-400, #60a5fa);
    margin: 0;
    font-weight: var(--font-medium, 500);
    padding: var(--space-1, 0.25rem) var(--space-3, 0.75rem);
    background-color: var(--primary-600, #1e293b);
    border-radius: var(--radius-full, 9999px);
    border: 1px solid var(--accent-500, #3b82f6);
    display: inline-block;
  }
}

.avatar-status {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: var(--radius-full, 9999px);
  border: 3px solid var(--bg-surface, #1e293b);
  
  &.online {
    background-color: var(--success-500, #22c55e);
  }
  
  &.offline {
    background-color: var(--text-tertiary, #94a3b8);
  }
}

.change-avatar-btn {
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-full, 9999px);
  background-color: var(--accent-500, #3b82f6);
  border: 2px solid var(--bg-surface, #1e293b);
  color: var(--text-on-accent, #ffffff);
  cursor: pointer;
  transition: all var(--transition-normal, 0.2s) var(--ease-in-out, cubic-bezier(0.4, 0, 0.2, 1));
  
  svg {
    width: 1.125rem;
    height: 1.125rem;
  }
  
  &:hover {
    background-color: var(--accent-400, #60a5fa);
    transform: translateX(-50%) translateY(-2px);
  }
}

// Profile Info
.profile-info {
  flex: 1;
  min-width: 0;
}

.profile-basic-info {
  margin-bottom: var(--space-4, 1rem);
}

.profile-name {
  font-size: var(--text-3xl, 1.875rem);
  font-weight: var(--font-bold, 700);
  color: var(--text-primary, #f8fafc);
  margin: 0 0 var(--space-2, 0.5rem) 0;
  font-family: var(--font-display, 'Inter', sans-serif);
}

.profile-username {
  font-size: var(--text-base, 1rem);
  color: var(--text-secondary, #cbd5e1);
  margin: 0 0 var(--space-3, 0.75rem) 0;
  font-family: var(--font-mono, 'SF Mono', monospace);
}

.profile-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4, 1rem);
  font-size: var(--text-sm, 0.875rem);
  color: var(--text-tertiary, #94a3b8);
}

.profile-level {
  background-color: var(--success-500, #22c55e);
  color: var(--text-on-accent, #ffffff);
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full, 9999px);
  font-weight: var(--font-medium, 500);
  font-size: var(--text-xs, 0.75rem);
}

.profile-bio {
  color: var(--text-secondary, #cbd5e1);
  font-size: var(--text-base, 1rem);
  line-height: 1.6;
  margin: 0 0 var(--space-6, 1.5rem) 0;
}

.profile-actions {
  display: flex;
  gap: var(--space-3, 0.75rem);
  flex-wrap: wrap;
}

// Buttons
.btn-primary {
  background-color: var(--accent-500, #3b82f6);
  color: var(--text-on-accent, #ffffff);
  padding: var(--space-3, 0.75rem) var(--space-6, 1.5rem);
  border-radius: var(--radius-md, 0.375rem);
  font-weight: var(--font-medium, 500);
  font-size: var(--text-sm, 0.875rem);
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal, 0.2s) var(--ease-in-out, cubic-bezier(0.4, 0, 0.2, 1));
  
  &:hover {
    background-color: var(--accent-400, #60a5fa);
    transform: translateY(-1px);
  }
}

.btn-secondary {
  background-color: transparent;
  color: var(--text-secondary, #cbd5e1);
  border: 1px solid var(--primary-500, #334155);
  padding: var(--space-3, 0.75rem) var(--space-6, 1.5rem);
  border-radius: var(--radius-md, 0.375rem);
  font-weight: var(--font-medium, 500);
  font-size: var(--text-sm, 0.875rem);
  cursor: pointer;
  transition: all var(--transition-normal, 0.2s) var(--ease-in-out, cubic-bezier(0.4, 0, 0.2, 1));
  display: flex;
  align-items: center;
  gap: var(--space-2, 0.5rem);
  
  svg {
    width: 1rem;
    height: 1rem;
  }
  
  &:hover {
    background-color: var(--primary-600, #1e293b);
    color: var(--text-primary, #f8fafc);
    border-color: var(--accent-500, #3b82f6);
  }
}

.btn-text {
  background: none;
  border: none;
  color: var(--accent-500, #3b82f6);
  font-size: var(--text-sm, 0.875rem);
  font-weight: var(--font-medium, 500);
  cursor: pointer;
  transition: color var(--transition-normal, 0.2s) var(--ease-in-out, cubic-bezier(0.4, 0, 0.2, 1));
  
  &:hover {
    color: var(--accent-400, #60a5fa);
  }
}

// Section Styling
.section-title {
  font-size: var(--text-xl, 1.25rem);
  font-weight: var(--font-semibold, 600);
  color: var(--text-primary, #f8fafc);
  margin: 0 0 var(--space-6, 1.5rem) 0;
  font-family: var(--font-display, 'Inter', sans-serif);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6, 1.5rem);
}

// Stats Section
.stats-section {
  margin-bottom: var(--space-8, 2rem);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4, 1rem);
}

.stat-card {
  background: var(--card-bg);  /* 使用统一的卡片背景色 */
  border: 1px solid var(--card-border);  /* 使用统一的卡片边框色 */
  border-radius: var(--radius-lg, 0.5rem);
  padding: var(--space-6, 1.5rem);
  text-align: center;
  transition: all var(--transition-normal, 0.2s) var(--ease-in-out, cubic-bezier(0.4, 0, 0.2, 1));
  
  &:hover {
    border-color: var(--accent-500, #3b82f6);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1);
  }
  
  &.highlight {
    border-color: var(--success-500, #22c55e);
    background: linear-gradient(135deg, var(--bg-surface, #1e293b) 0%, rgba(34, 197, 94, 0.1) 100%);
  }
}

.stat-icon {
  font-size: 2rem;
  line-height: 1;
  margin-bottom: var(--space-3, 0.75rem);
}

.stat-value {
  font-size: var(--text-2xl, 1.5rem);
  font-weight: var(--font-bold, 700);
  color: var(--text-primary, #f8fafc);
  font-family: var(--font-mono, 'SF Mono', monospace);
  margin-bottom: var(--space-1, 0.25rem);
}

.stat-label {
  font-size: var(--text-sm, 0.875rem);
  color: var(--text-secondary, #cbd5e1);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: var(--font-medium, 500);
}

// Workouts Section
.workouts-section {
  margin-bottom: var(--space-8, 2rem);
}

.workouts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-6, 1.5rem);
}

.workout-card {
  background: var(--card-bg);  /* 使用统一的卡片背景色 */
  border: 1px solid var(--card-border);  /* 使用统一的卡片边框色 */
  border-radius: var(--radius-lg, 0.5rem);
  padding: var(--space-6, 1.5rem);
  transition: all var(--transition-normal, 0.2s) var(--ease-in-out, cubic-bezier(0.4, 0, 0.2, 1));
  
  &:hover {
    border-color: var(--accent-500, #3b82f6);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1);
  }
}

.workout-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4, 1rem);
}

.workout-name {
  font-size: var(--text-lg, 1.125rem);
  font-weight: var(--font-semibold, 600);
  color: var(--text-primary, #f8fafc);
  margin: 0;
}

.workout-date {
  font-size: var(--text-sm, 0.875rem);
  color: var(--text-tertiary, #94a3b8);
  font-family: var(--font-mono, 'SF Mono', monospace);
}

.workout-stats {
  display: flex;
  gap: var(--space-6, 1.5rem);
  margin-bottom: var(--space-4, 1rem);
  padding: var(--space-4, 1rem);
  background-color: var(--primary-600, #1e293b);
  border-radius: var(--radius-md, 0.375rem);
}

.workout-stat {
  text-align: center;
  flex: 1;
}

.workout-stat-value {
  font-size: var(--text-lg, 1.125rem);
  font-weight: var(--font-semibold, 600);
  color: var(--text-primary, #f8fafc);
  font-family: var(--font-mono, 'SF Mono', monospace);
}

.workout-stat-label {
  font-size: var(--text-xs, 0.75rem);
  color: var(--text-secondary, #cbd5e1);
  margin-top: var(--space-1, 0.25rem);
}

.workout-exercises {
  margin-bottom: var(--space-4, 1rem);
  font-size: var(--text-sm, 0.875rem);
  
  .exercises-label {
    color: var(--text-secondary, #cbd5e1);
    font-weight: var(--font-medium, 500);
  }
  
  .exercises-list {
    color: var(--text-primary, #f8fafc);
    margin-left: var(--space-2, 0.5rem);
  }
}

.workout-view-btn {
  width: 100%;
  background-color: transparent;
  color: var(--accent-500, #3b82f6);
  border: 1px solid var(--accent-500, #3b82f6);
  padding: var(--space-2, 0.5rem) var(--space-4, 1rem);
  border-radius: var(--radius-md, 0.375rem);
  font-weight: var(--font-medium, 500);
  font-size: var(--text-sm, 0.875rem);
  cursor: pointer;
  transition: all var(--transition-normal, 0.2s) var(--ease-in-out, cubic-bezier(0.4, 0, 0.2, 1));
  
  &:hover {
    background-color: var(--accent-500, #3b82f6);
    color: var(--text-on-accent, #ffffff);
  }
}

// Achievements Section
.achievements-section {
  margin-bottom: var(--space-8, 2rem);
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-4, 1rem);
}

.achievement-card {
  background: var(--card-bg);  /* 使用统一的卡片背景色 */
  border: 1px solid var(--card-border);  /* 使用统一的卡片边框色 */
  border-radius: var(--radius-lg, 0.5rem);
  padding: var(--space-6, 1.5rem);
  display: flex;
  align-items: center;
  gap: var(--space-4, 1rem);
  position: relative;
  transition: all var(--transition-normal, 0.2s) var(--ease-in-out, cubic-bezier(0.4, 0, 0.2, 1));
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
  }
  
  &.achievement-common {
    border-color: var(--text-tertiary, #94a3b8);
  }
  
  &.achievement-rare {
    border-color: var(--accent-500, #3b82f6);
    background: linear-gradient(135deg, var(--bg-surface, #1e293b) 0%, rgba(59, 130, 246, 0.05) 100%);
  }
  
  &.achievement-epic {
    border-color: var(--warning-500, #f59e0b);
    background: linear-gradient(135deg, var(--bg-surface, #1e293b) 0%, rgba(245, 158, 11, 0.05) 100%);
  }
}

.achievement-icon {
  font-size: 2.5rem;
  line-height: 1;
  flex-shrink: 0;
}

.achievement-content {
  flex: 1;
  min-width: 0;
}

.achievement-title {
  font-size: var(--text-base, 1rem);
  font-weight: var(--font-semibold, 600);
  color: var(--text-primary, #f8fafc);
  margin: 0 0 var(--space-1, 0.25rem) 0;
}

.achievement-description {
  font-size: var(--text-sm, 0.875rem);
  color: var(--text-secondary, #cbd5e1);
  margin: 0 0 var(--space-2, 0.5rem) 0;
  line-height: 1.4;
}

.achievement-date {
  font-size: var(--text-xs, 0.75rem);
  color: var(--text-tertiary, #94a3b8);
  font-family: var(--font-mono, 'SF Mono', monospace);
}

.achievement-rarity {
  position: absolute;
  top: var(--space-2, 0.5rem);
  right: var(--space-2, 0.5rem);
  font-size: var(--text-xs, 0.75rem);
  font-weight: var(--font-medium, 500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm, 0.125rem);
  
  .achievement-common & {
    background-color: var(--primary-500, #334155);
    color: var(--text-secondary, #cbd5e1);
  }
  
  .achievement-rare & {
    background-color: var(--accent-500, #3b82f6);
    color: var(--text-on-accent, #ffffff);
  }
  
  .achievement-epic & {
    background-color: var(--warning-500, #f59e0b);
    color: var(--text-on-accent, #ffffff);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .profile-page {
    padding: 0;
  }
  
  .profile-header {
    padding: var(--space-6, 1.5rem);
    margin-bottom: var(--space-6, 1.5rem);
  }
  
  .profile-header-content {
    flex-direction: column;
    text-align: center;
    gap: var(--space-6, 1.5rem);
  }
  
  .profile-avatar {
    width: 6rem;
    height: 6rem;
  }
  
  .profile-name {
    font-size: var(--text-2xl, 1.5rem);
  }
  
  .profile-meta {
    justify-content: center;
  }
  
  .profile-actions {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3, 0.75rem);
  }
  
  .stat-card {
    padding: var(--space-4, 1rem);
  }
  
  .stat-value {
    font-size: var(--text-xl, 1.25rem);
  }
  
  .workouts-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4, 1rem);
  }
  
  .workout-stats {
    gap: var(--space-4, 1rem);
  }
  
  .achievements-grid {
    grid-template-columns: 1fr;
  }
  
  .achievement-card {
    padding: var(--space-4, 1rem);
    gap: var(--space-3, 0.75rem);
  }
  
  .achievement-icon {
    font-size: 2rem;
  }
}

// Tablet Responsive
@media (min-width: 769px) and (max-width: 1024px) {
  .profile-header-content {
    gap: var(--space-6, 1.5rem);
  }
  
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .workouts-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

// Loading States
.loading-skeleton {
  background: linear-gradient(90deg, var(--primary-600, #1e293b) 25%, var(--primary-500, #334155) 50%, var(--primary-600, #1e293b) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Focus states for accessibility
.btn-primary:focus,
.btn-secondary:focus,
.workout-view-btn:focus,
.change-avatar-btn:focus {
  outline: 2px solid var(--accent-500, #3b82f6);
  outline-offset: 2px;
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .stat-card:hover,
  .workout-card:hover,
  .achievement-card:hover {
    transform: none;
  }
  
  .loading-skeleton {
    animation: none;
  }
} 