import React from 'react';
import './ProfilePage.scss';

// Mock data for FitMaster profile
const mockUserProfile = {
  id: "user_123",
  username: "fitness_enthusiast",
  displayName: "<PERSON>",
  avatar: "/api/placeholder/120/120",
  joinDate: new Date("2023-01-15"),
  fitnessLevel: "Intermediate",
  bio: "健身爱好者，专注于力量训练和体能提升。目标是在2025年完成第一次健美比赛。",
  location: "上海, 中国",
  stats: {
    workoutDays: 156,
    totalVolume: 2100, // kg
    personalRecords: 45,
    followers: 128,
    following: 89,
    currentStreak: 7
  },
  recentWorkouts: [
    {
      id: "workout_1",
      name: "Push Day - 胸肩三头",
      date: new Date("2025-01-16"),
      duration: 75, // minutes
      exercises: ["卧推", "肩上推举", "双杠臂屈伸"],
      volume: 1250, // kg
      sets: 16
    },
    {
      id: "workout_2",
      name: "Pull Day - 背二头",
      date: new Date("2025-01-14"),
      duration: 68,
      exercises: ["引体向上", "杠铃划船", "弯举"],
      volume: 980,
      sets: 14
    },
    {
      id: "workout_3",
      name: "Leg Day - 腿部训练",
      date: new Date("2025-01-12"),
      duration: 82,
      exercises: ["深蹲", "硬拉", "腿举"],
      volume: 1560,
      sets: 18
    }
  ],
  achievements: [
    {
      id: "ach_1",
      title: "连续训练一周",
      description: "连续7天完成训练计划",
      icon: "🔥",
      unlockedAt: new Date("2025-01-16"),
      rarity: "common"
    },
    {
      id: "ach_2",
      title: "深蹲达人",
      description: "深蹲重量突破100kg",
      icon: "🏋️",
      unlockedAt: new Date("2025-01-10"),
      rarity: "rare"
    },
    {
      id: "ach_3",
      title: "社交达人",
      description: "获得100个关注者",
      icon: "👥",
      unlockedAt: new Date("2025-01-05"),
      rarity: "epic"
    }
  ]
};

const ProfilePage: React.FC = () => {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getAchievementClass = (rarity: string) => {
    switch (rarity) {
      case 'epic': return 'achievement-epic';
      case 'rare': return 'achievement-rare';
      case 'common': 
      default: return 'achievement-common';
    }
  };

  return (
    <div className="profile-page">
        {/* Profile Content */}
        <section className="profile-content">
          <div className="profile-summary">
            <div className="profile-avatar-section">
              <div className="profile-avatar">
                <img 
                  src={mockUserProfile.avatar} 
                  alt={`${mockUserProfile.displayName}的头像`}
                  className="avatar-image"
                />
                <div className="avatar-status online"></div>
              </div>
              {/* 个人信息位于头像之下 */}
              <div className="profile-info">
                <h2 className="profile-name">{mockUserProfile.displayName}</h2>
                <p className="profile-username">@{mockUserProfile.username}</p>
                <p className="profile-fitness-level">{mockUserProfile.fitnessLevel}</p>
              </div>
            </div>

            <div className="profile-info">
              <div className="profile-meta">
                <span className="profile-location">{mockUserProfile.location}</span>
                <span className="profile-join-date">
                  加入于 {formatDate(mockUserProfile.joinDate)}
                </span>
              </div>
              
              <p className="profile-bio">{mockUserProfile.bio}</p>
              
              <div className="profile-actions">
                <button className="btn-primary">编辑资料</button>
                <button className="btn-secondary">分享资料</button>
                <button className="btn-secondary" aria-label="更多选项">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <circle cx="12" cy="12" r="1"/>
                    <circle cx="19" cy="12" r="1"/>
                    <circle cx="5" cy="12" r="1"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Grid */}
        <section className="stats-section">
          <h2 className="section-title">健身数据统计</h2>
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">🏃‍♂️</div>
              <div className="stat-content">
                <div className="stat-value">{mockUserProfile.stats.workoutDays}</div>
                <div className="stat-label">训练天数</div>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="stat-icon">💪</div>
              <div className="stat-content">
                <div className="stat-value">{mockUserProfile.stats.totalVolume}kg</div>
                <div className="stat-label">总训练量</div>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="stat-icon">🏆</div>
              <div className="stat-content">
                <div className="stat-value">{mockUserProfile.stats.personalRecords}</div>
                <div className="stat-label">个人记录</div>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="stat-icon">👥</div>
              <div className="stat-content">
                <div className="stat-value">{mockUserProfile.stats.followers}</div>
                <div className="stat-label">关注者</div>
              </div>
            </div>
            
            <div className="stat-card">
              <div className="stat-icon">➕</div>
              <div className="stat-content">
                <div className="stat-value">{mockUserProfile.stats.following}</div>
                <div className="stat-label">正在关注</div>
              </div>
            </div>
            
            <div className="stat-card highlight">
              <div className="stat-icon">🔥</div>
              <div className="stat-content">
                <div className="stat-value">{mockUserProfile.stats.currentStreak}</div>
                <div className="stat-label">连续训练</div>
              </div>
            </div>
          </div>
        </section>

        {/* Recent Workouts */}
        <section className="workouts-section">
          <div className="section-header">
            <h2 className="section-title">最近训练</h2>
            <button className="btn-text">查看全部</button>
          </div>
          
          <div className="workouts-grid">
            {mockUserProfile.recentWorkouts.map((workout) => (
              <div key={workout.id} className="workout-card">
                <div className="workout-header">
                  <h3 className="workout-name">{workout.name}</h3>
                  <span className="workout-date">{formatDate(workout.date)}</span>
                </div>
                
                <div className="workout-stats">
                  <div className="workout-stat">
                    <div className="workout-stat-value">{workout.duration}分钟</div>
                    <div className="workout-stat-label">训练时长</div>
                  </div>
                  <div className="workout-stat">
                    <div className="workout-stat-value">{workout.volume}kg</div>
                    <div className="workout-stat-label">训练量</div>
                  </div>
                  <div className="workout-stat">
                    <div className="workout-stat-value">{workout.sets}组</div>
                    <div className="workout-stat-label">总组数</div>
                  </div>
                </div>
                
                <div className="workout-exercises">
                  <span className="exercises-label">主要动作:</span>
                  <span className="exercises-list">{workout.exercises.join(' • ')}</span>
                </div>
                
                <button className="workout-view-btn">查看详情</button>
              </div>
            ))}
          </div>
        </section>

        {/* Achievements */}
        <section className="achievements-section">
          <div className="section-header">
            <h2 className="section-title">近期成就</h2>
            <button className="btn-text">查看全部</button>
          </div>
          
          <div className="achievements-grid">
            {mockUserProfile.achievements.map((achievement) => (
              <div key={achievement.id} className={`achievement-card ${getAchievementClass(achievement.rarity)}`}>
                <div className="achievement-icon">{achievement.icon}</div>
                <div className="achievement-content">
                  <h3 className="achievement-title">{achievement.title}</h3>
                  <p className="achievement-description">{achievement.description}</p>
                  <span className="achievement-date">
                    {formatDate(achievement.unlockedAt)}
                  </span>
                </div>
                <div className="achievement-rarity">{achievement.rarity}</div>
              </div>
            ))}
          </div>
        </section>
      </div>
    );
};

export default ProfilePage;