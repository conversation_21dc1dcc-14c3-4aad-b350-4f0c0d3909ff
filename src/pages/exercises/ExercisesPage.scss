// Exercises Page Styles - iOS优化版本
.exercises-page {
  // 移除负margin，使用合理的布局方式
  padding: 0;
  min-height: 100vh;
  background: var(--bg-primary);
  
  // 设置最大宽度和居中，防止页面过宽
  max-width: 100vw;
  width: 100%;
  margin: 0 auto;
  
  // 防止水平滚动
  overflow-x: hidden;
}

// 新的Header设计
.exercises-header {
  background: var(--bg-surface);
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--space-4);
  
  .header-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--space-4) var(--space-4) var(--space-3) var(--space-4);
    padding-top: calc(env(safe-area-inset-top) + var(--space-4));
    
    .title-section {
      flex: 1;
      
      h1 {
        font-size: var(--text-3xl);
        font-weight: var(--font-bold);
        color: var(--text-primary);
        margin: 0 0 var(--space-1) 0;
        background: var(--gradient-brand);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      p {
        color: var(--text-secondary);
        font-size: var(--text-base);
        margin: 0;
      }
    }
    
    .header-actions {
      display: flex;
      gap: var(--space-2);
      
      .action-icon-btn {
        width: 44px;
        height: 44px;
        border-radius: var(--radius-full);
        background: var(--accent-500);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all var(--transition-normal) var(--ease-in-out);
        
        svg {
          width: 20px;
          height: 20px;
          color: var(--text-on-accent);
          stroke-width: 2;
        }
        
        &:hover {
          background: var(--accent-400);
          transform: scale(1.05);
        }
        
        &:active {
          transform: scale(0.95);
        }
        
        &.search-btn {
          background: var(--primary-500);
          
          &:hover {
            background: var(--primary-400);
          }
        }
        
        &.filter-btn {
          background: var(--secondary-500);
          
          &:hover {
            background: var(--secondary-400);
          }
        }
      }
    }
  }
  
  // 水平肌肉群筛选栏
  .muscle-filter-bar {
    padding: var(--space-3) var(--space-4);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    
    .filter-icon-fixed {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 56px;
      height: 56px;
      background: var(--bg-surface);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-lg);
      color: var(--text-secondary);
      flex-shrink: 0;
      
      svg {
        width: 24px;
        height: 24px;
      }
    }
    
    .muscle-filter-scroll {
      display: flex;
      gap: var(--space-3);
      overflow-x: auto;
      padding: var(--space-2) 0;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none;
      -ms-overflow-style: none;
      align-items: center;
      flex: 1;
      
      &::-webkit-scrollbar {
        display: none;
      }
      
      .muscle-filter-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56px;
        height: 56px;
        background: var(--bg-surface);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        cursor: pointer;
        transition: all var(--transition-normal) var(--ease-in-out);
        flex-shrink: 0;
        
        .muscle-icon {
    width: 56px !important;
    height: 56px !important;
    max-width: none !important;
    max-height: none !important;
    object-fit: fill;
    display: block;
  }
        
        &:hover {
          border-color: var(--accent-300);
          background: var(--bg-hover);
          transform: scale(1.02);
        }
        
        &.active {
          background: var(--bg-surface);
          border-color: var(--accent-500);
          border-width: 2px;
          transform: scale(1.02);
          
          .muscle-icon {
            // 保持原始颜色，不使用滤镜
          }
        }
      }
    }
  }
}

// 最近执行区域
.recent-performed-section {
  margin-bottom: var(--space-6);
  padding: 0 var(--space-4);
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-3);
    
    h2 {
      font-size: var(--text-xl);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin: 0;
    }
    
    .view-all-btn {
      background: none;
      border: none;
      color: var(--accent-500);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      cursor: pointer;
      padding: var(--space-1) var(--space-2);
      border-radius: var(--radius-md);
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:hover {
        background: var(--bg-hover);
      }
    }
  }
  
  .recent-exercises-scroll {
    display: flex;
    gap: var(--space-3);
    overflow-x: auto;
    padding-bottom: var(--space-2);
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
    
    &::-webkit-scrollbar {
      display: none;
    }
    
    .recent-exercise-card {
      flex-shrink: 0;
      width: 120px;
      background: var(--bg-surface);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-lg);
      padding: var(--space-3);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:hover {
        border-color: var(--accent-500);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
      }
      
      .recent-exercise-image {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 48px;
        height: 48px;
        background: var(--accent-100);
        border-radius: var(--radius-full);
        margin: 0 auto var(--space-2) auto;
        
        .muscle-icon {
          font-size: 24px;
        }
      }
      
      .recent-exercise-info {
        text-align: center;
        
        h4 {
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          color: var(--text-primary);
          margin: 0 0 var(--space-1) 0;
          line-height: 1.2;
        }
        
        p {
          font-size: var(--text-xs);
          color: var(--text-tertiary);
          margin: 0;
        }
      }
    }
  }
}

// Results Info
.results-info {
  margin-bottom: var(--space-4);
  padding: 0 var(--space-4);
  
  .results-count {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
  }
  
  // 新增：动作列表标题样式，与最近执行保持一致
  .section-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
  }
}

// 无限滚动相关样式
.scroll-sentinel {
  height: 20px; // 增加高度便于检测
  width: 100%;
  margin: 10px 0;
  opacity: 0; // 使用opacity而非visibility，确保可检测
  pointer-events: none; // 防止用户交互
  
  // 调试模式下可见
  @media (prefers-reduced-motion: reduce) {
    opacity: 0.1;
    background: rgba(255, 0, 0, 0.1);
  }
}

// Exercises Grid - iOS优化（修复双列布局）
.exercises-grid {
  display: grid;
  margin-bottom: var(--space-8);
  
  // 性能优化
  contain: layout style;
  will-change: contents;
  
  // === 修复后的双列布局系统 ===
  // 使用固定的双列布局，确保在所有移动设备上都显示两列
  grid-template-columns: 1fr 1fr;
  
  // 优化的间距系统：与其他组件保持一致的页面边距
  column-gap: clamp(16px, 4vw, 24px); // 增加列间距
  row-gap: clamp(20px, 5vw, 28px); // 增加行间距
  padding: 0 var(--space-4); // 使用标准组件间距
  
  // === 防止内容溢出的安全措施 ===
  overflow-x: hidden; // 防止水平滚动
  width: 100%; // 使用完整宽度
  max-width: 100%;
  
  // 垂直分散对齐
  align-items: stretch; // 确保卡片高度一致
  
  // === iOS设备特定优化 ===
  
  // iPhone SE (320px) - 最小iOS设备  
  @media (max-width: 320px) {
    grid-template-columns: 1fr 1fr; // 保持双列
    column-gap: 12px; // 卡片间距
    row-gap: 16px; // 行间距
    padding: 0 var(--space-3); // 与其他组件一致的边距
  }
  
  // iPhone 12 mini, iPhone SE 3rd gen (375px)
  @media (min-width: 321px) and (max-width: 375px) {
    grid-template-columns: 1fr 1fr; // 保持双列
    column-gap: 14px; // 卡片间距
    row-gap: 18px; // 行间距
    padding: 0 var(--space-3); // 与其他组件一致的边距
  }
  
  // iPhone 12/13/14 标准尺寸 (390px)
  @media (min-width: 376px) and (max-width: 390px) {
    grid-template-columns: 1fr 1fr; // 保持双列
    column-gap: 16px; // 卡片间距
    row-gap: 20px; // 行间距
    padding: 0 var(--space-4); // 与其他组件一致的边距
  }
  
  // iPhone 14 Pro Max 等大屏手机 (430px+)
  @media (min-width: 391px) and (max-width: 480px) {
    grid-template-columns: 1fr 1fr; // 保持双列
    column-gap: 18px; // 卡片间距
    row-gap: 22px; // 行间距
    padding: 0 var(--space-4); // 与其他组件一致的边距
  }
  
  // === 平板和桌面端 ===
  
  // 小平板端 (iPad mini横屏等)
  @media (min-width: 481px) and (max-width: 767px) {
    grid-template-columns: repeat(3, 1fr); // 三列
    column-gap: var(--space-4);
    row-gap: var(--space-5);
    padding: 0 var(--space-4); // 与其他组件一致
  }
  
  // 平板端 (iPad)
  @media (min-width: 768px) and (max-width: 1023px) {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    column-gap: var(--space-4);
    row-gap: var(--space-5);
    padding: 0 var(--space-5); // 与其他组件一致
  }
  
  // 桌面端
  @media (min-width: 1024px) {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    column-gap: var(--space-5);
    row-gap: var(--space-6);
    padding: 0 var(--space-6); // 与其他组件一致
  }
  
  // === iOS横屏模式优化 ===
  @media (max-width: 768px) and (orientation: landscape) {
    // 横屏时使用更紧凑的布局
    column-gap: clamp(14px, 3vw, 18px);
    row-gap: clamp(16px, 4vw, 20px);
    padding: 0 var(--space-4); // 与其他组件一致
    
    // 在横屏模式下显示3列（如果空间足够）
    @media (min-width: 568px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

// Exercise Card - iOS优化版本
.exercise-card {
  background: var(--bg-surface);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: all var(--transition-normal) var(--ease-in-out);
  display: flex;
  flex-direction: column;
  
  // 基础属性（必须在嵌套规则之前）
  aspect-ratio: 3/4; // 默认比例
  min-width: 0; // 防止flex item溢出
  min-height: 180px; // 保证最小高度以确保内容可读性
  
  // iOS触摸优化
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  
  // iOS设备特定的宽高比优化
  @media (max-width: 320px) {
    aspect-ratio: 4/5; // 在最小屏幕上使用更方形的比例
  }
  
  @media (min-width: 321px) and (max-width: 375px) {
    aspect-ratio: 3.2/4; // 稍微调整比例
  }
  
  &:hover {
    border-color: var(--accent-500);
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
  }
  
  // iOS触摸反馈
  &:active {
    transform: translateY(-2px) scale(0.98);
  }
  
  .exercise-image {
    position: relative;
    flex: 1;
    background: var(--primary-600);
    min-height: 120px; // 确保图片区域最小高度
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .favorite-btn {
      position: absolute;
      top: var(--space-2);
      right: var(--space-2);
      background: rgba(0, 0, 0, 0.5);
      border: none;
      border-radius: var(--radius-full);
      color: white;
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(4px);
      
      // iOS自适应按钮尺寸
      width: clamp(32px, 8vw, 40px);
      height: clamp(32px, 8vw, 40px);
      
      svg {
        width: clamp(16px, 4vw, 20px);
        height: clamp(16px, 4vw, 20px);
        stroke-width: 2;
      }
      
      &:hover {
        background: rgba(0, 0, 0, 0.7);
        transform: scale(1.1);
      }
      
      &:active {
        transform: scale(0.95);
      }
      
      &.favorited {
        color: var(--error-500);
        background: rgba(255, 255, 255, 0.9);
      }
    }
  }
  
  .exercise-info {
    flex-shrink: 0;
    
    // iOS自适应padding
    padding: clamp(8px, 2.5vw, 12px);
    
    .exercise-name {
      color: var(--text-primary);
      margin: 0 0 var(--space-2) 0;
      line-height: var(--leading-tight);
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      
      // iOS自适应字体大小
      font-size: clamp(0.875rem, 3.5vw, 1rem);
      font-weight: var(--font-semibold);
      
      // 确保在小屏幕上文字不会太小
      @media (max-width: 320px) {
        font-size: 0.875rem; // 14px
        -webkit-line-clamp: 1; // 在最小屏幕上只显示一行
      }
    }
    
    .exercise-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--space-1);
      gap: var(--space-1);
      
      .difficulty {
        color: var(--accent-500);
        font-size: clamp(0.75rem, 3vw, 0.875rem);
        
        .difficulty-plate {
          width: clamp(16px, 4vw, 20px);
          height: clamp(16px, 4vw, 20px);
          object-fit: contain;
          vertical-align: middle;
        }
      }
      
      .category {
        background: var(--primary-100);
        color: var(--primary-700);
        font-weight: var(--font-medium);
        border-radius: var(--radius-md);
        
        // iOS自适应标签
        font-size: clamp(0.625rem, 2.5vw, 0.75rem);
        padding: clamp(2px, 1vw, 4px) clamp(4px, 2vw, 8px);
        
        // 在小屏幕上可能隐藏或缩短
        @media (max-width: 320px) {
          display: none; // 在最小屏幕上隐藏分类标签以节省空间
        }
      }
    }
  }
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--space-16) var(--space-8);
  
  .empty-icon {
    width: 4rem;
    height: 4rem;
    margin-bottom: var(--space-4);
    color: var(--text-tertiary);
    
    svg {
      width: 100%;
      height: 100%;
      stroke-width: 1.5;
    }
  }
  
  h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
  }
  
  p {
    color: var(--text-secondary);
    margin: 0;
  }
}

// Exercise Detail Modal
.exercise-detail-modal {
  background: var(--bg-surface);
  border: 1px solid var(--primary-500);
  border-radius: var(--radius-xl);
  width: 100%;
  max-width: 700px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-2xl);
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--space-6);
    border-bottom: 1px solid var(--primary-500);
    
    .title-group {
      flex: 1;
      
      h2 {
        font-size: var(--text-2xl);
        font-weight: var(--font-bold);
        color: var(--text-primary);
        margin: 0 0 var(--space-1) 0;
      }
      
      .english-name {
        font-size: var(--text-base);
        color: var(--text-tertiary);
        margin: 0;
        font-style: italic;
      }
    }
    
    .close-btn {
      padding: var(--space-2);
      background: transparent;
      border: none;
      border-radius: var(--radius-md);
      color: var(--text-tertiary);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      
      svg {
        width: 1.5rem;
        height: 1.5rem;
        stroke-width: 2;
      }
      
      &:hover {
        background: var(--primary-600);
        color: var(--text-primary);
      }
    }
  }
  
  .modal-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-6);
    
    .exercise-images {
      margin-bottom: var(--space-6);
      border-radius: var(--radius-lg);
      overflow: hidden;
      
      img {
        width: 100%;
        height: auto;
        display: block;
      }
    }
    
    .exercise-details {
      .detail-section {
        margin-bottom: var(--space-6);
        
        .instructions-list {
          list-style: none;
          padding: 0;
          margin: 0;
          
          li {
            padding: var(--space-2) 0;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
            
            &:last-child {
              border-bottom: none;
            }
            
            &::before {
              content: counter(step-counter);
              counter-increment: step-counter;
              background: var(--accent-500);
              color: var(--text-on-accent);
              width: 1.5rem;
              height: 1.5rem;
              border-radius: var(--radius-full);
              display: inline-flex;
              align-items: center;
              justify-content: center;
              font-size: var(--text-xs);
              font-weight: var(--font-bold);
              margin-right: var(--space-3);
            }
          }
        }
        
        .tips-list {
          list-style: none;
          padding: 0;
          margin: 0;
          
          li {
            padding: var(--space-2) 0;
            color: var(--text-primary);
            position: relative;
            padding-left: var(--space-6);
            
            &::before {
              content: '💡';
              position: absolute;
              left: 0;
              top: var(--space-2);
            }
          }
        }
        
        .alternatives-list {
          display: flex;
          flex-wrap: wrap;
          gap: var(--space-2);
          
          .alternative-tag {
            background: var(--primary-100);
            color: var(--primary-700);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-full);
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
          }
        }
      }
    }
  }
}

// Modal Overlay
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
  z-index: 1000;
  backdrop-filter: blur(4px);
}

// 搜索模态框
.search-modal {
  background: var(--bg-surface);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  width: 100%;
  max-width: 500px;
  box-shadow: var(--shadow-2xl);
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4);
    border-bottom: 1px solid var(--border-color);
    
    h3 {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin: 0;
    }
    
    .close-btn {
      width: 32px;
      height: 32px;
      background: transparent;
      border: none;
      border-radius: var(--radius-md);
      color: var(--text-tertiary);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      display: flex;
      align-items: center;
      justify-content: center;
      
      svg {
        width: 18px;
        height: 18px;
        stroke-width: 2;
      }
      
      &:hover {
        background: var(--bg-hover);
        color: var(--text-primary);
      }
    }
  }
  
  .search-content {
    padding: var(--space-4);
    
    .search-bar {
      position: relative;
      
      svg {
        position: absolute;
        left: var(--space-3);
        top: 50%;
        transform: translateY(-50%);
        width: 20px;
        height: 20px;
        color: var(--text-tertiary);
        stroke-width: 2;
      }
      
      input {
        width: 100%;
        padding: var(--space-3) var(--space-3) var(--space-3) 3rem;
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-lg);
        color: var(--text-primary);
        font-size: var(--text-base);
        transition: all var(--transition-normal) var(--ease-in-out);
        
        &::placeholder {
          color: var(--text-tertiary);
        }
        
        &:focus {
          outline: 2px solid var(--accent-500);
          outline-offset: 2px;
          border-color: var(--accent-500);
        }
      }
    }
  }
}

// 筛选模态框
.filter-modal {
  background: var(--bg-surface);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-2xl);
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4);
    border-bottom: 1px solid var(--border-color);
    
    h3 {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin: 0;
    }
    
    .close-btn {
      width: 32px;
      height: 32px;
      background: transparent;
      border: none;
      border-radius: var(--radius-md);
      color: var(--text-tertiary);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      display: flex;
      align-items: center;
      justify-content: center;
      
      svg {
        width: 18px;
        height: 18px;
        stroke-width: 2;
      }
      
      &:hover {
        background: var(--bg-hover);
        color: var(--text-primary);
      }
    }
  }
  
  .filter-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-4);
    display: grid;
    gap: var(--space-4);
    
    .filter-group {
      display: flex;
      flex-direction: column;
      gap: var(--space-2);
      
      label {
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        color: var(--text-secondary);
      }
      
      select {
        padding: var(--space-3);
        background: var(--bg-primary);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-md);
        color: var(--text-primary);
        font-size: var(--text-sm);
        cursor: pointer;
        transition: all var(--transition-normal) var(--ease-in-out);
        
        &:hover {
          border-color: var(--accent-500);
        }
        
        &:focus {
          outline: 2px solid var(--accent-500);
          outline-offset: 2px;
          border-color: var(--accent-500);
        }
      }
    }
    
    .filter-toggles {
      .toggle-label {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        cursor: pointer;
        padding: var(--space-2);
        border-radius: var(--radius-md);
        transition: background-color var(--transition-normal) var(--ease-in-out);
        
        &:hover {
          background: var(--bg-hover);
        }
        
        input[type="checkbox"] {
          width: 18px;
          height: 18px;
          accent-color: var(--accent-500);
        }
        
        span {
          font-size: var(--text-sm);
          color: var(--text-primary);
          font-weight: var(--font-medium);
        }
      }
    }
    
    .sort-section {
      display: flex;
      flex-direction: column;
      gap: var(--space-2);
      
      label {
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        color: var(--text-secondary);
      }
      
      select {
        padding: var(--space-3);
        background: var(--accent-500);
        border: 1px solid var(--accent-500);
        border-radius: var(--radius-md);
        color: var(--text-on-accent);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        cursor: pointer;
        transition: all var(--transition-normal) var(--ease-in-out);
        
        &:hover {
          background: var(--accent-400);
          border-color: var(--accent-400);
        }
        
        &:focus {
          outline: 2px solid var(--accent-500);
          outline-offset: 2px;
        }
      }
    }
  }
}

// iOS响应式优化
@media (max-width: 768px) {
  .exercises-header {
    .header-top {
      padding: var(--space-3) var(--space-3) var(--space-2) var(--space-3);
      padding-top: calc(env(safe-area-inset-top) + var(--space-3));
      
      .title-section h1 {
        font-size: var(--text-2xl);
      }
      
      .header-actions {
        gap: var(--space-1);
        
        .action-icon-btn {
          width: 40px;
          height: 40px;
          
          svg {
            width: 18px;
            height: 18px;
          }
        }
      }
    }
    
    .muscle-filter-bar {
      padding: 0 var(--space-3) var(--space-3) var(--space-3);
      
      .muscle-filter-scroll {
        .muscle-filter-btn {
          min-width: 55px;
          padding: var(--space-1) var(--space-2);
          
          .muscle-icon {
            font-size: 18px;
          }
          
          .muscle-name {
            font-size: 10px;
          }
        }
      }
    }
  }
  
  .recent-performed-section {
    padding: 0 var(--space-3);
    
    .recent-exercises-scroll {
      .recent-exercise-card {
        width: 100px;
        padding: var(--space-2);
        
        .recent-exercise-image {
          width: 40px;
          height: 40px;
          
          .muscle-icon {
            font-size: 20px;
          }
        }
        
        .recent-exercise-info {
          h4 {
            font-size: 11px;
          }
          
          p {
            font-size: 10px;
          }
        }
      }
    }
  }
  
  .results-info,
  .exercises-grid {
    padding: 0 var(--space-3);
  }
  
  .modal-overlay {
    padding: var(--space-3);
  }
  
  .search-modal,
  .filter-modal {
    max-width: none;
    width: 100%;
  }
}

// Focus states
.search-bar input:focus,
.filter-group select:focus,
.sort-section select:focus,
.action-btn:focus,
.favorite-btn:focus,
.close-btn:focus {
  outline: 2px solid var(--accent-500);
  outline-offset: 2px;
}

// Loading State
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-4);
  text-align: center;
  
  .loading-spinner {
    width: 48px;
    height: 48px;
    margin-bottom: var(--space-4);
    
    svg {
      width: 100%;
      height: 100%;
      color: var(--accent-500);
      animation: spin 1s linear infinite;
    }
  }
  
  p {
    color: var(--text-secondary);
    font-size: var(--text-base);
    margin: 0;
  }
}

// Error State
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-4);
  text-align: center;
  
  .error-icon {
    width: 48px;
    height: 48px;
    margin-bottom: var(--space-4);
    
    svg {
      width: 100%;
      height: 100%;
      color: var(--error-500);
    }
  }
  
  h3 {
    color: var(--text-primary);
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    margin: 0 0 var(--space-2) 0;
  }
  
  p {
    color: var(--text-secondary);
    font-size: var(--text-base);
    margin: 0 0 var(--space-4) 0;
    max-width: 300px;
  }
  
  .retry-btn {
    background: var(--accent-500);
    color: var(--text-on-accent);
    border: none;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &:hover {
      background: var(--accent-400);
      transform: translateY(-1px);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    &:focus {
      outline: 2px solid var(--accent-500);
      outline-offset: 2px;
    }
  }
}

// Spin animation for loading spinner
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .exercise-card,
  .action-btn,
  .favorite-btn {
    transition: none;
  }
  
  .exercise-card:hover,
  .action-btn:hover,
  .favorite-btn:hover {
    transform: none;
  }
  
  .loading-spinner svg {
    animation: none;
  }
  
  .retry-btn:hover {
    transform: none;
  }
}

// 调试面板样式
.debug-panel {
  margin: var(--space-6) var(--space-4);
  padding: var(--space-4);
  background: var(--bg-surface);
  border: 2px solid var(--error-500);
  border-radius: var(--radius-lg);
  
  h3 {
    margin: 0 0 var(--space-3) 0;
    color: var(--error-500);
    font-size: var(--text-lg);
  }
  
  .debug-info {
    margin-bottom: var(--space-3);
    
    p {
      margin: var(--space-1) 0;
      font-family: monospace;
      font-size: var(--text-sm);
      color: var(--text-secondary);
    }
  }
  
  .debug-buttons {
    display: flex;
    gap: var(--space-2);
    
    button {
      padding: var(--space-2) var(--space-3);
      background: var(--error-500);
      color: var(--text-on-error);
      border: none;
      border-radius: var(--radius-md);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:hover:not(:disabled) {
        background: var(--error-400);
        transform: translateY(-1px);
      }
      
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}