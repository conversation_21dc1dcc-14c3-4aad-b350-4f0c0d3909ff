// 动作详情页面样式 - iOS优化版本
.exercise-detail-page {
  height: 100vh;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
  overflow: visible; // 改为 visible，允许肌肉示意图溢出显示

  // iOS Safe Area支持
  @supports (-webkit-touch-callout: none) {
    height: -webkit-fill-available;
  }
}

// 固定头部 - iOS优化版本，始终显示动作名称
.exercise-detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  padding-top: calc(env(safe-area-inset-top) + var(--space-4));
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed); // 1030
  flex-shrink: 0;
  min-height: calc(env(safe-area-inset-top) + var(--space-11) + var(--space-8));

  // iOS毛玻璃效果
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(var(--bg-primary-rgb, 255, 255, 255), 0.95);
  box-shadow: var(--shadow-sm);

  .back-btn {
    width: var(--space-11); // 44px - iOS触摸目标标准
    height: var(--space-11);
    border-radius: var(--radius-full);
    background: var(--bg-secondary);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);

    svg {
      width: 20px;
      height: 20px;
      color: var(--text-primary);
      stroke-width: 2;
    }

    &:hover {
      background: var(--bg-hover);
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  // 固定标题 - 始终显示，增大字体并居中
  .fixed-title {
    flex: 1;
    text-align: center;
    margin: 0 var(--space-4);

    h1 {
      font-size: var(--text-xl); // 增大字体
      font-weight: var(--font-bold); // 增加字重
      color: var(--text-primary);
      margin: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.2;
    }
  }

  .favorite-btn {
    width: var(--space-11); // 44px - iOS触摸目标标准
    height: var(--space-11);
    border-radius: var(--radius-full);
    background: var(--bg-secondary);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);
    // 确保与返回按钮在同一水平线上
    align-self: center;

    svg {
      width: 20px;
      height: 20px;
      color: var(--text-secondary);
      stroke-width: 2;
    }

    &:hover {
      background: var(--accent-500);

      svg {
        color: var(--text-on-accent);
      }
    }

    &:active {
      transform: scale(0.95);
    }

    // 收藏状态
    &.favorited {
      background: var(--accent-500);

      svg {
        color: var(--text-on-accent);
      }
    }
  }
}

// 重新设计的滚动结构 - 固定 tabs 以上，只滚动 tab 内容
.exercise-detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: visible; // 改为 visible，允许肌肉示意图完整显示
  padding-top: calc(env(safe-area-inset-top) + var(--space-11) + var(--space-8)); // 为固定头部预留空间

  // iOS滚动优化
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: none;

  // 固定视频区域 - 不参与滚动
  .video-section.fixed-video {
    flex-shrink: 0;
    padding: var(--space-4);
    padding-bottom: 0;
  }

  // 固定的动作信息区域
  .exercise-info-section.fixed-info {
    flex-shrink: 0;
    padding: var(--space-4);
    padding-top: 0;
    padding-bottom: 0;
  }

  // 固定的 Tab 切换栏
  .exercise-tabs-section.fixed-tabs {
    flex-shrink: 0;
    padding: var(--space-4);
    padding-top: var(--space-2);
    padding-bottom: 0;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    z-index: 1;
  }

  // 可滚动的 Tab 内容区域
  .tab-content-scrollable {
    flex: 1;
    overflow-y: auto;
    overflow-x: visible; // 改为 visible，允许肌肉示意图水平溢出
    padding: var(--space-4);
    padding-top: var(--space-4);
    padding-bottom: calc(env(safe-area-inset-bottom) + var(--space-8));

    // iOS滚动优化
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: none;
    scroll-behavior: smooth;
  }
}

// 动作标题部分
.exercise-title-section {
  text-align: center;
  
  .exercise-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
  }
  
  .exercise-meta {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--space-3);
    margin-top: var(--space-2);
    
    .meta-item {
      font-size: var(--text-sm);
      color: var(--text-secondary);
      background: var(--bg-secondary);
      padding: var(--space-1) var(--space-2);
      border-radius: var(--radius-sm);
      
      @media (max-width: 768px) {
        font-size: var(--text-xs);
      }
    }
  }
}

// 动作信息区域
.exercise-info-section {
  .exercise-info-card {
    background: var(--bg-surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-3);
    
    .exercise-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: var(--space-3);
      
      @media (max-width: 768px) {
        flex-wrap: wrap;
        gap: var(--space-2);
      }
      
      .meta-item {
        font-size: var(--text-sm);
        color: var(--text-secondary);

        @media (max-width: 768px) {
          font-size: var(--text-xs);
        }

        // 器械信息高亮样式
        &.equipment-highlight {
          color: var(--accent-500);
          font-weight: var(--font-semibold);
        }
      }

      .difficulty {
        display: flex;
        align-items: center;

        .difficulty-display {
          display: flex;
          align-items: center;
          gap: var(--space-2);

          .difficulty-text {
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            color: var(--accent-500);

            @media (max-width: 768px) {
              font-size: var(--text-xs);
            }
          }
        }

        .difficulty-plate {
          width: 20px;
          height: 20px;
          object-fit: contain;
          vertical-align: middle;
        }
      }
    }
  }
}

// 视频播放区域 - 支持固定模式
.video-section {
  // 固定视频模式 - 不参与滚动
  &.fixed-video {
    position: relative;
    z-index: 1;
    background: var(--bg-primary);
  }

  .video-container {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    cursor: pointer;
    
    .exercise-video {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: var(--radius-lg);
    }
    
    // 视频控制覆盖层
    .video-controls-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.3);
      opacity: 0;
      transition: opacity var(--transition-normal) var(--ease-in-out);
      pointer-events: none;
      
      .play-pause-btn {
        width: 60px;
        height: 60px;
        border-radius: var(--radius-full);
        background: rgba(255, 255, 255, 0.9);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all var(--transition-normal) var(--ease-in-out);
        backdrop-filter: blur(4px);
        
        svg {
          width: 24px;
          height: 24px;
          color: var(--text-primary);
          stroke-width: 2;
        }
        
        &:hover {
          background: rgba(255, 255, 255, 1);
          transform: scale(1.1);
        }
        
        &.paused {
          svg {
            margin-left: 2px; // 播放图标向右偏移一点
          }
        }
      }
    }
    
    // 悬停时显示控制按钮
    &:hover .video-controls-overlay {
      opacity: 1;
      pointer-events: auto;
    }
    
    // 移动设备上点击时显示控制按钮
    &:active .video-controls-overlay {
      opacity: 1;
      pointer-events: auto;
    }
  }
}

// Tab 切换区域 - 固定布局
.exercise-tabs-section {
  margin-top: var(--space-6);

  // 固定 tabs 样式
  &.fixed-tabs {
    margin-top: 0;
  }

  .exercise-tabs {
    // HeroUI Tabs 自定义样式 - 符合图像设计
    --heroui-primary: var(--accent-500);
    --heroui-primary-foreground: var(--text-on-accent);

    // Tab 头部样式 - 简洁设计
    [role="tablist"] {
      // 移除分割线
      // border-bottom: 1px solid var(--border-color);
      margin-bottom: var(--space-2); // 减少垂直间距
      background: transparent; // 移除背景色
      padding: 0;

      // Tab 按钮样式 - 符合图像设计
      [role="tab"] {
        font-size: var(--text-base);
        font-weight: var(--font-medium);
        color: var(--text-secondary); // 未选中状态：灰色
        padding: var(--space-3) var(--space-4);
        min-height: var(--space-11); // iOS触摸目标标准
        transition: all var(--transition-normal) var(--ease-in-out);
        background: transparent;
        border: none;
        border-radius: 0;
        position: relative;

        // 选中状态 - 符合图像设计
        &[aria-selected="true"] {
          color: var(--accent-500); // 选中状态：蓝色
          font-weight: var(--font-semibold);
          background: transparent; // 移除背景色

          // 下划线指示器
          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--accent-500);
            border-radius: 1px;
          }
        }

        &:hover {
          color: var(--text-primary);
          background: transparent; // 保持透明背景
        }

        // iOS触摸反馈
        &:active {
          transform: scale(0.98);
        }
      }
    }

    // Tab 内容区域
    [role="tabpanel"] {
      padding-top: var(--space-2);
      animation: fadeIn var(--transition-normal) var(--ease-in-out);
    }

    // Tab 内容包装器样式 - 简洁设计
    .tab-content-wrapper {
      padding: var(--space-4) 0;
      animation: fadeIn var(--transition-normal) var(--ease-in-out);
    }
  }
}

// 淡入动画 - 简洁过渡效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 训练部位信息区域 - 移除标题（现在在Tab中）
.muscle-info-section {
  // 移除原有的h2标题样式，现在由Tab标题处理

  // 整体卡片容器 - 严格的左右布局
  .muscle-info-card {
    background: var(--bg-surface);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    display: flex;
    gap: var(--space-4);
    align-items: flex-start;
    min-height: 360px; // 增加最小高度，确保肌肉示意图完整显示
    overflow: visible; // 改为 visible，防止肌肉示意图被裁剪
    position: relative; // 为子元素层级控制做准备

    // iOS设备保持左右布局，不改为上下布局
    @media (max-width: 768px) {
      gap: var(--space-3);
      min-height: 320px; // 增加移动端最小高度
      padding: var(--space-3);
    }

    // 只有极小屏幕（iPhone SE等）才考虑上下布局
    @media (max-width: 375px) {
      flex-direction: column;
      align-items: stretch;
      gap: var(--space-4);
      min-height: auto;
    }
  }

  // 左侧肌肉列表边栏（严格20%宽度）
  .muscle-list-sidebar {
    flex: 0 0 20%;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
    min-width: 100px;
    height: 320px; // 与肌肉示意图高度保持一致
    overflow-y: auto; // 整体滚动设置
    overflow-x: hidden;
    background: transparent; // 设置背景为透明，防止遮挡肌肉示意图
    position: relative; // 为层级控制做准备
    z-index: 1; // 确保标签区域在肌肉示意图下方

    // 无痕滚动样式 - iOS优化
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE/Edge

    // 隐藏滚动条但保持滚动功能
    &::-webkit-scrollbar {
      display: none; // Chrome/Safari/WebKit
    }

    // 添加渐变遮罩效果，提示可滚动内容
    &::after {
      content: '';
      position: sticky;
      bottom: 0;
      left: 0;
      right: 0;
      height: 20px;
      background: linear-gradient(transparent, var(--bg-surface));
      pointer-events: none;
      opacity: 0;
      transition: opacity var(--transition-normal) var(--ease-in-out);
    }

    // 当内容可滚动时显示渐变遮罩
    &.scrollable::after {
      opacity: 1;
    }

    // iOS设备优化 - 保持20%比例
    @media (max-width: 768px) {
      flex: 0 0 20%;
      min-width: 80px;
      height: 240px; // 移动端稍微减少整体高度
    }

    // 只有极小屏幕才改为100%宽度
    @media (max-width: 375px) {
      flex: none;
      width: 100%;
      min-width: auto;
    }

    .muscle-category {
      margin-bottom: var(--space-1); // 进一步减少主要和次要标签之间的间隔
      flex-shrink: 0; // 不让分类标题收缩

      // 进一步减少最后一个分类的底部间距
      &:last-child {
        margin-bottom: 0;
      }

      h3 {
        font-size: var(--text-sm);
        font-weight: var(--font-bold);
        color: var(--text-primary);
        margin: 0 0 var(--space-2) 0;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        position: relative;
        padding-bottom: var(--space-2);
        display: inline-block;
        width: auto;

        // 使用CSS变量的下划线
        background-image: linear-gradient(transparent, transparent);
        background-repeat: no-repeat;
        background-size: 100% 3px;
        background-position: 0 calc(100% - 2px);

        // iOS小屏幕优化
        @media (max-width: 768px) {
          font-size: var(--text-xs);
          margin: 0 0 var(--space-1) 0;
          padding-bottom: var(--space-1);
        }
      }

      // 主要肌肉标题 - 使用设计系统颜色
      &:has(.muscle-tag.primary) h3 {
        background-image: linear-gradient(var(--accent-500), var(--accent-500));
      }

      // 次要肌肉标题 - 使用设计系统颜色
      &:has(.muscle-tag.secondary) h3 {
        background-image: linear-gradient(var(--accent-300), var(--accent-300));
      }

      .muscle-tags {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
        align-items: flex-start;
        width: 100%;
        // 移除独立滚动设置，使用父容器的整体滚动

        .muscle-tag {
          display: inline-block;
          padding: var(--space-1) var(--space-2); // 减少内边距，更紧凑包裹文字
          border-radius: var(--radius-xl); // 使用大圆角设计
          font-size: var(--text-xs); // 使用更小的字体
          font-weight: var(--font-medium);
          text-align: center;
          white-space: nowrap;
          width: fit-content;
          max-width: 100%;
          transition: all var(--transition-normal) var(--ease-in-out);
          line-height: 1.2; // 紧凑的行高

          // 统一所有肌肉标签的外观，无论主要还是次要
          background: var(--bg-tertiary); // 统一背景色：#d1d5db (gray-300)
          color: var(--text-secondary); // 统一文字色：#4b5563 (gray-600)
          border: none; // 移除边框，保持简洁

          // iOS触摸反馈 - 统一的悬停效果
          &:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
            background: var(--bg-hover); // 悬停时使用统一的悬停背景色
          }

          &:active {
            transform: scale(0.98);
          }

          // iOS小屏幕优化
          @media (max-width: 768px) {
            padding: var(--space-1) var(--space-2); // 保持紧凑的padding
            font-size: var(--text-xs); // 保持小字体
          }
        }
      }
    }
  }

  // 右侧肌肉图容器（严格80%宽度）
  .muscle-illustration-container {
    flex: 0 0 80%;
    display: flex;
    justify-content: flex-start; // 改为左对齐，减少间隔
    align-items: flex-start; // 将肌肉示意图定位到容器最上方
    min-height: 320px; // 增加最小高度，确保图像完整显示
    overflow: visible; // 移除 hidden，防止图像被遮挡
    position: relative; // 为层级控制做准备
    z-index: 1100; // 设置高 z-index，确保在所有其他元素之上

    // 移除过度限制，让肌肉图自然显示
    > * {
      width: 100%;
      height: auto;
      object-fit: contain;
    }

    // 优化肌肉示意图的显示，移除尺寸限制
    .static-muscle-illustration {
      width: 100%;
      max-width: none; // 移除最大宽度限制，让图像完整显示
      margin-top: 0; // 确保从容器顶部开始显示
      position: relative; // 为 iOS 层级优化做准备
      z-index: inherit; // 继承父容器的高 z-index

      svg {
        width: 100%;
        height: auto;
        display: block; // 移除默认的 inline 间距
        max-width: none; // 移除SVG的最大宽度限制
        position: relative; // iOS Safari 优化
        z-index: inherit; // 继承父容器的 z-index

        // iOS 特定优化：确保 SVG 不被裁剪
        @supports (-webkit-touch-callout: none) {
          transform: translateZ(0); // 启用硬件加速
          will-change: transform; // 优化渲染性能
        }
      }
    }

    // iOS设备保持80%比例
    @media (max-width: 768px) {
      flex: 0 0 80%;
      min-height: 320px; // 增加移动端最小高度，确保图像完整显示
      align-items: flex-start; // 移动端也保持顶部对齐

      .static-muscle-illustration {
        max-width: none; // 移除移动端的最大宽度限制
        margin-top: 0; // 确保移动端也从顶部开始

        svg {
          max-width: none; // 移除SVG的最大宽度限制

          // iOS 移动端特定优化
          @supports (-webkit-touch-callout: none) {
            transform: translateZ(0); // 启用硬件加速
            will-change: transform; // 优化渲染性能
            backface-visibility: hidden; // 防止渲染问题
          }
        }
      }
    }

    // 只有极小屏幕才改为100%宽度
    @media (max-width: 375px) {
      flex: none;
      width: 100%;
      min-height: 280px; // 小屏幕也保持足够高度

      .static-muscle-illustration {
        max-width: none; // 即使在小屏幕也不限制宽度

        svg {
          max-width: none; // 确保SVG在小屏幕也能完整显示

          // 小屏幕 iOS 优化
          @supports (-webkit-touch-callout: none) {
            transform: translateZ(0); // 启用硬件加速
            will-change: transform; // 优化渲染性能
            backface-visibility: hidden; // 防止渲染问题
          }
        }
      }
    }

    .no-muscle-data {
      text-align: center;
      color: var(--text-secondary);

      p {
        margin: 0;
        font-size: var(--text-sm);
      }
    }
  }
}

// 增强肌肉可视化组件样式
.enhanced-muscle-illustration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  
  .muscle-svg {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }
  
  .muscle-legend {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    align-items: center;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      
      .legend-color {
        width: 16px;
        height: 16px;
        border-radius: var(--radius-sm);
        border: 1px solid var(--border-color);
      }
      
      span {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: var(--font-medium);
      }
    }
  }
}

.enhanced-muscle-illustration-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

// 动作指导区域 - 移除标题（现在在Tab中）
.instructions-section {
  // 移除原有的h2标题样式，现在由Tab标题处理

  .instruction-steps {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    
    .instruction-step {
      display: flex;
      gap: var(--space-3);
      padding: var(--space-4);
      background: var(--bg-surface);
      border: 1px solid var(--border-color);
      border-radius: var(--radius-lg);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &:hover {
        border-color: var(--accent-300);
        background: var(--bg-hover);
      }
      
      &.active {
        border-color: var(--accent-500);
        background: var(--accent-50);
      }
      
      .step-number {
        width: 32px;
        height: 32px;
        border-radius: var(--radius-full);
        background: var(--accent-500);
        color: var(--text-on-accent);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-sm);
        font-weight: var(--font-semibold);
        flex-shrink: 0;
      }
      
      .step-content {
        flex: 1;
        color: var(--text-primary);
        font-size: var(--text-base);
        line-height: 1.6;
      }
    }
  }
}

// 注意事项区域 - 移除标题（现在在Tab中）
.tips-section {
  // 移除原有的h2标题样式，现在由Tab标题处理

  .exercise-tips {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    
    .tip-item {
      display: flex;
      gap: var(--space-3);
      padding: var(--space-4);
      border-radius: var(--radius-lg);
      border-left: 4px solid;
      
      &.warning {
        background: var(--warning-50);
        border-left-color: var(--warning-500);
        
        .tip-icon svg {
          color: var(--warning-600);
        }
      }
      
      &.info {
        background: var(--info-50);
        border-left-color: var(--info-500);
        
        .tip-icon svg {
          color: var(--info-600);
        }
      }
      
      .tip-icon {
        flex-shrink: 0;
        
        svg {
          width: 20px;
          height: 20px;
          stroke-width: 2;
        }
      }
      
      .tip-content {
        flex: 1;
        color: var(--text-primary);
        font-size: var(--text-base);
        line-height: 1.6;

        // 问题部分加粗样式
        .tip-question {
          font-weight: var(--font-semibold);
          color: var(--accent-500);
        }

        // 答案部分样式
        .tip-answer {
          font-weight: var(--font-normal);
          color: var(--text-primary);
        }
      }
    }
  }
}



// 头部占位符（用于Loading和Error状态）
.header-placeholder {
  width: var(--space-11); // 44px - 与按钮保持一致
  height: var(--space-11);
}

// Loading和Error状态 - iOS优化版本
.loading-state, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 200px); // 减去头部高度
  padding: var(--space-8);
  text-align: center;

  .loading-spinner, .error-icon {
    margin-bottom: var(--space-4);

    svg {
      width: 48px;
      height: 48px;
      color: var(--text-secondary);

      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }

      &.spinning {
        animation: spin 1s linear infinite;
      }
    }
  }

  h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
  }

  p {
    color: var(--text-secondary);
    font-size: var(--text-base);
    margin: 0 0 var(--space-4) 0;
    max-width: 300px;
    line-height: 1.5;
  }

  .error-actions {
    display: flex;
    gap: var(--space-3);

    .retry-btn, .back-btn-secondary {
      padding: var(--space-3) var(--space-6);
      border-radius: var(--radius-lg);
      border: none;
      font-size: var(--text-base);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      min-height: var(--space-11); // iOS触摸目标标准
    }

    .retry-btn {
      background: var(--accent-500);
      color: var(--text-on-accent);

      &:hover {
        background: var(--accent-400);
      }

      &:active {
        transform: scale(0.98);
      }
    }

    .back-btn-secondary {
      background: var(--bg-secondary);
      color: var(--text-primary);

      &:hover {
        background: var(--bg-hover);
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }
}

// iOS响应式设计优化
@media (max-width: 768px) {
  .exercise-detail-page {
    // iOS设备特殊处理
    @supports (-webkit-touch-callout: none) {
      height: -webkit-fill-available;
    }
  }

  .exercise-detail-header {
    padding: var(--space-3);
    padding-top: calc(env(safe-area-inset-top) + var(--space-3));

    .back-btn,
    .favorite-btn {
      width: 40px;
      height: 40px;

      svg {
        width: 18px;
        height: 18px;
      }
    }

    .fixed-title h1 {
      font-size: var(--text-base);
    }
  }

  .exercise-detail-content {
    padding-top: calc(env(safe-area-inset-top) + 40px + var(--space-6));

    .video-section.fixed-video {
      padding: var(--space-3);
      padding-bottom: 0;
    }

    .exercise-info-section.fixed-info {
      padding: var(--space-3);
      padding-top: 0;
      padding-bottom: 0;
    }

    .exercise-tabs-section.fixed-tabs {
      padding: var(--space-3);
      padding-top: var(--space-2);
      padding-bottom: 0;
    }

    .tab-content-scrollable {
      padding: var(--space-3);
      padding-top: var(--space-4);
    }
  }

  // Tab 移动端优化
  .exercise-tabs-section {
    .exercise-tabs {
      [role="tablist"] {
        // 移动端Tab按钮优化
        [role="tab"] {
          font-size: var(--text-sm);
          padding: var(--space-2) var(--space-3);
          min-height: var(--space-11); // 保持iOS触摸目标标准
        }
      }
    }
  }
}

// 平板和桌面端优化
@media (min-width: 768px) {
  .exercise-detail-content {
    max-width: 800px;
    margin: 0 auto;
    padding-left: var(--space-6);
    padding-right: var(--space-6);
  }
}

@media (min-width: 1024px) {
  .exercise-detail-content {
    max-width: 1000px;
    padding-left: var(--space-8);
    padding-right: var(--space-8);
  }
}

// iOS暗色主题支持
.theme-dark {
  .exercise-detail-header {
    background: rgba(var(--bg-primary-rgb, 15, 23, 42), 0.95);
  }

  .exercise-tabs-section.fixed-tabs {
    background: rgba(var(--bg-primary-rgb, 15, 23, 42), 0.95);
  }

  .muscle-info-card {
    background: var(--bg-surface);
    border-color: var(--border-color);
  }

  .muscle-tag {
    // 暗色主题下统一所有肌肉标签的外观
    background: var(--bg-tertiary) !important; // 在暗色主题下会自动使用对应的暗色值
    color: var(--text-secondary) !important; // 在暗色主题下会自动使用对应的暗色值

  }

  // HeroUI Tabs 暗色主题
  .exercise-tabs {
    [role="tablist"] {
      border-bottom-color: var(--border-color);

      [role="tab"] {
        color: var(--text-secondary);

        &[aria-selected="true"] {
          color: var(--accent-400); // 暗色主题下使用稍浅的accent色

          // 暗色主题下的下划线
          &::after {
            background: var(--accent-400);
          }
        }

        &:hover {
          color: var(--text-primary);
        }
      }
    }
  }
}

// iOS特有的滚动优化
@supports (-webkit-touch-callout: none) {
  .exercise-detail-content {
    // 启用iOS动量滚动
    -webkit-overflow-scrolling: touch;

    // 防止橡皮筋效果
    overscroll-behavior: none;

    // 优化滚动性能
    will-change: scroll-position;
  }
}