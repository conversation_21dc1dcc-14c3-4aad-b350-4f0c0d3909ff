import React, { useState } from 'react';
import { ExerciseMuscleCard } from '../../components/fitness/MuscleVisualization';
import { MuscleGroupEnum } from '../../types/muscle.types';
import './ExerciseMuscleCardDemo.scss';

export const ExerciseMuscleCardDemo: React.FC = () => {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [size, setSize] = useState<'sm' | 'md' | 'lg'>('md');

  // 示例动作配置
  const exerciseExamples = [
    {
      name: '卧推',
      primaryMuscles: [MuscleGroupEnum.CHEST],
      secondaryMuscles: [MuscleGroupEnum.TRICEPS, MuscleGroupEnum.SHOULDERS_FRONT]
    },
    {
      name: '引体向上',
      primaryMuscles: [MuscleGroupEnum.LATS, MuscleGroupEnum.BACK],
      secondaryMuscles: [MuscleGroupEnum.BICEPS, MuscleGroupEnum.SHOULDERS_BACK]
    },
    {
      name: '深蹲',
      primaryMuscles: [MuscleGroupEnum.QUADRICEPS, MuscleGroupEnum.GLUTES],
      secondaryMuscles: [MuscleGroupEnum.HAMSTRINGS, MuscleGroupEnum.CALVES]
    },
    {
      name: '硬拉',
      primaryMuscles: [MuscleGroupEnum.HAMSTRINGS, MuscleGroupEnum.GLUTES, MuscleGroupEnum.LOWER_BACK],
      secondaryMuscles: [MuscleGroupEnum.TRAPS, MuscleGroupEnum.QUADRICEPS]
    },
    {
      name: '肩推',
      primaryMuscles: [MuscleGroupEnum.SHOULDERS_FRONT],
      secondaryMuscles: [MuscleGroupEnum.TRICEPS, MuscleGroupEnum.CHEST]
    },
    {
      name: '弯举',
      primaryMuscles: [MuscleGroupEnum.BICEPS],
      secondaryMuscles: [MuscleGroupEnum.FOREARMS_FRONT]
    }
  ];

  return (
    <div className={`exercise-muscle-card-demo ${theme}`}>
      <div className="demo-header">
        <h1>动作肌肉卡片演示</h1>
        <p>ExerciseMuscleCard 组件用于动作详情页面，展示训练部位信息</p>
        
        {/* 控制面板 */}
        <div className="demo-controls">
          <div className="control-group">
            <label>主题:</label>
            <div className="button-group">
              <button 
                className={theme === 'light' ? 'active' : ''}
                onClick={() => setTheme('light')}
              >
                浅色
              </button>
              <button 
                className={theme === 'dark' ? 'active' : ''}
                onClick={() => setTheme('dark')}
              >
                深色
              </button>
            </div>
          </div>

          <div className="control-group">
            <label>尺寸:</label>
            <div className="button-group">
              <button 
                className={size === 'sm' ? 'active' : ''}
                onClick={() => setSize('sm')}
              >
                小
              </button>
              <button 
                className={size === 'md' ? 'active' : ''}
                onClick={() => setSize('md')}
              >
                中
              </button>
              <button 
                className={size === 'lg' ? 'active' : ''}
                onClick={() => setSize('lg')}
              >
                大
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="demo-content">
        {/* 动作示例网格 */}
        <div className="exercises-grid">
          {exerciseExamples.map((exercise, index) => (
            <div key={index} className="exercise-item">
              <h3 className="exercise-title">{exercise.name}</h3>
              <ExerciseMuscleCard
                primaryMuscles={exercise.primaryMuscles}
                secondaryMuscles={exercise.secondaryMuscles}
                theme={theme}
                size={size}
              />
            </div>
          ))}
        </div>

        {/* 使用说明 */}
        <div className="demo-info">
          <h2>使用说明</h2>
          <div className="info-grid">
            <div className="info-card">
              <h3>颜色说明</h3>
              <ul>
                <li><span className="color-primary"></span> 主要肌肉 (#3b82f6)</li>
                <li><span className="color-secondary"></span> 次要肌肉 (#93c5fd)</li>
              </ul>
            </div>

            <div className="info-card">
              <h3>功能特点</h3>
              <ul>
                <li>卡片式布局设计</li>
                <li>左侧文本，右侧肌肉视图</li>
                <li>不支持点击选中（只读模式）</li>
                <li>支持多种尺寸和主题</li>
                <li>iOS原生体验优化</li>
              </ul>
            </div>

            <div className="info-card">
              <h3>使用场景</h3>
              <ul>
                <li>动作详情页面</li>
                <li>训练计划展示</li>
                <li>肌肉训练说明</li>
                <li>健身教程配图</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 代码示例 */}
        <div className="demo-code">
          <h2>代码示例</h2>
          <pre className="code-block">
            <code>
{`import { ExerciseMuscleCard } from '@/components/fitness/MuscleVisualization';
import { MuscleGroupEnum } from '@/types/muscle.types';

// 使用示例
<ExerciseMuscleCard
  primaryMuscles={[MuscleGroupEnum.CHEST]}
  secondaryMuscles={[MuscleGroupEnum.TRICEPS, MuscleGroupEnum.SHOULDERS_FRONT]}
  theme="light"
  size="md"
/>`}
            </code>
          </pre>
        </div>
      </div>
    </div>
  );
};

export default ExerciseMuscleCardDemo; 