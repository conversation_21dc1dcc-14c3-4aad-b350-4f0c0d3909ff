// ExerciseMuscleCardDemo 演示页面样式
@import '../../styles/design-system.css';

.exercise-muscle-card-demo {
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  padding: 20px;
  transition: all 0.3s ease;

  // 主题样式
  &.light {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1a1a1a;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    --button-bg: #f8fafc;
    --button-active-bg: #3b82f6;
    --button-active-color: #ffffff;
  }

  &.dark {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --border-color: #475569;
    --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    --button-bg: #334155;
    --button-active-bg: #3b82f6;
    --button-active-color: #ffffff;
  }

  .demo-header {
    max-width: 1200px;
    margin: 0 auto 40px;
    text-align: center;

    h1 {
      font-size: 2.5rem;
      font-weight: bold;
      margin-bottom: 12px;
      color: var(--text-primary);
    }

    p {
      font-size: 1.1rem;
      color: var(--text-secondary);
      margin-bottom: 32px;
    }

    .demo-controls {
      display: flex;
      justify-content: center;
      gap: 32px;
      margin-bottom: 20px;

      .control-group {
        display: flex;
        align-items: center;
        gap: 12px;

        label {
          font-weight: 500;
          color: var(--text-primary);
          font-size: 14px;
        }

        .button-group {
          display: flex;
          border: 1px solid var(--border-color);
          border-radius: 8px;
          overflow: hidden;
          background: var(--button-bg);

          button {
            padding: 8px 16px;
            border: none;
            background: transparent;
            color: var(--text-primary);
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 44px; // iOS触摸目标

            &:not(:last-child) {
              border-right: 1px solid var(--border-color);
            }

            &:hover {
              background: var(--bg-tertiary);
            }

            &.active {
              background: var(--button-active-bg);
              color: var(--button-active-color);
            }

            // iOS触摸反馈
            @supports (-webkit-touch-callout: none) {
              -webkit-tap-highlight-color: transparent;
              -webkit-touch-callout: none;
              -webkit-user-select: none;
              user-select: none;

              &:active {
                transform: scale(0.98);
              }
            }
          }
        }
      }

      // 移动端响应式
      @media (max-width: 768px) {
        flex-direction: column;
        gap: 16px;

        .control-group {
          justify-content: center;
        }
      }
    }
  }

  .demo-content {
    max-width: 1200px;
    margin: 0 auto;

    // 动作示例网格
    .exercises-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
      margin-bottom: 60px;

      .exercise-item {
        .exercise-title {
          font-size: 1.25rem;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: 12px;
          text-align: center;
        }
      }

      // 移动端响应式
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 20px;

        .exercise-item {
          .exercise-title {
            font-size: 1.1rem;
          }
        }
      }
    }

    // 使用说明区域
    .demo-info {
      margin-bottom: 40px;

      h2 {
        font-size: 1.875rem;
        font-weight: bold;
        color: var(--text-primary);
        margin-bottom: 24px;
        text-align: center;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;

        .info-card {
          background: var(--bg-secondary);
          border: 1px solid var(--border-color);
          border-radius: 12px;
          padding: 20px;
          box-shadow: var(--card-shadow);

          h3 {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
          }

          ul {
            list-style: none;
            margin: 0;
            padding: 0;

            li {
              display: flex;
              align-items: center;
              padding: 6px 0;
              color: var(--text-secondary);
              font-size: 14px;

              .color-primary,
              .color-secondary {
                width: 16px;
                height: 16px;
                border-radius: 4px;
                margin-right: 8px;
                flex-shrink: 0;
              }

              .color-primary {
                background: #3b82f6;
              }

              .color-secondary {
                background: #93c5fd;
              }
            }
          }
        }
      }
    }

    // 代码示例区域
    .demo-code {
      margin-bottom: 40px;

      h2 {
        font-size: 1.875rem;
        font-weight: bold;
        color: var(--text-primary);
        margin-bottom: 24px;
        text-align: center;
      }

      .code-block {
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 20px;
        overflow-x: auto;
        box-shadow: var(--card-shadow);

        code {
          color: var(--text-primary);
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 13px;
          line-height: 1.5;
          white-space: pre;
          word-wrap: break-word;
        }
      }
    }
  }

  // iOS硬件加速
  @supports (-webkit-touch-callout: none) {
    -webkit-transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    will-change: transform;
  }

  // 滚动优化
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
} 