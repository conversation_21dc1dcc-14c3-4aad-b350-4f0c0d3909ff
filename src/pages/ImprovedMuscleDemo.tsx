import React, { useState } from 'react';
import { MuscleGroupEnum, MUSCLE_DISPLAY_NAMES } from '../types/muscle.types';
import { 
  MuscleIllustration
} from '../components/fitness/MuscleVisualization';
// 暂时移除不存在的导入
// ImprovedMuscleIllustration, MuscleVisualizationModule

/**
 * 🎯 改进版肌肉可视化演示页面
 * 基于原版人体轮廓，逐步集成muscle-selector-complete的精确肌肉群
 */
const ImprovedMuscleDemo: React.FC = () => {
  const [selectedMuscles, setSelectedMuscles] = useState<MuscleGroupEnum[]>([]);
  const [currentView, setCurrentView] = useState<'improved' | 'original' | 'comparison'>('improved');
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [enhancedMuscles, setEnhancedMuscles] = useState<MuscleGroupEnum[]>([
    MuscleGroupEnum.CHEST, 
    MuscleGroupEnum.BICEPS
  ]);

  const handleMuscleToggle = (muscle: MuscleGroupEnum) => {
    setSelectedMuscles(prev => 
      prev.includes(muscle) 
        ? prev.filter(m => m !== muscle)
        : [...prev, muscle]
    );
  };

  const handleQuickSelect = (muscles: MuscleGroupEnum[]) => {
    setSelectedMuscles(muscles);
  };

  const toggleEnhancedMuscle = (muscle: MuscleGroupEnum) => {
    setEnhancedMuscles(prev => 
      prev.includes(muscle)
        ? prev.filter(m => m !== muscle)
        : [...prev, muscle]
    );
  };

  // 预设肌肉群组合
  const musclePresets = {
    upperBodyPush: [MuscleGroupEnum.CHEST, MuscleGroupEnum.SHOULDERS, MuscleGroupEnum.TRICEPS],
    upperBodyPull: [MuscleGroupEnum.BACK, MuscleGroupEnum.LATS, MuscleGroupEnum.BICEPS],
    core: [MuscleGroupEnum.ABDOMINALS, MuscleGroupEnum.OBLIQUES],
    enhanced: [MuscleGroupEnum.CHEST, MuscleGroupEnum.BICEPS] // 已增强的肌肉群
  };

  // 所有可用的肌肉群
  const allMuscleGroups = Object.values(MuscleGroupEnum);

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50'}`}>
      <div className="container mx-auto px-4 py-8">
        
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">
            🎯 改进版肌肉可视化演示
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-6">
            基于原版人体轮廓，逐步集成muscle-selector-complete的精确肌肉群
          </p>
          
          {/* 主题切换 */}
          <div className="flex justify-center gap-4 mb-6">
            <button
              onClick={() => setTheme('light')}
              className={`px-4 py-2 rounded-md transition-colors ${
                theme === 'light'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              ☀️ 浅色主题
            </button>
            <button
              onClick={() => setTheme('dark')}
              className={`px-4 py-2 rounded-md transition-colors ${
                theme === 'dark'
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              🌙 深色主题
            </button>
          </div>
        </div>

        {/* 视图选择 */}
        <div className="flex justify-center mb-8">
          <div className="flex space-x-1 bg-gray-200 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setCurrentView('improved')}
              className={`px-6 py-2 rounded-md transition-colors ${
                currentView === 'improved'
                  ? 'bg-white dark:bg-gray-800 shadow-sm'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-600'
              }`}
            >
              🎯 改进版
            </button>
            <button
              onClick={() => setCurrentView('original')}
              className={`px-6 py-2 rounded-md transition-colors ${
                currentView === 'original'
                  ? 'bg-white dark:bg-gray-800 shadow-sm'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-600'
              }`}
            >
              📝 原版
            </button>
            <button
              onClick={() => setCurrentView('comparison')}
              className={`px-6 py-2 rounded-md transition-colors ${
                currentView === 'comparison'
                  ? 'bg-white dark:bg-gray-800 shadow-sm'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-600'
              }`}
            >
              🔄 对比
            </button>
          </div>
        </div>

        {/* 选中的肌肉群显示 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
          <h3 className="text-xl font-semibold mb-4">
            已选择的肌肉群 ({selectedMuscles.length}/15)
          </h3>
          <div className="flex flex-wrap gap-2 mb-4">
            {selectedMuscles.length > 0 ? (
              selectedMuscles.map(muscle => (
                <span
                  key={muscle}
                  className={`px-3 py-1 rounded-full text-sm font-medium ${
                    enhancedMuscles.includes(muscle)
                      ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                      : 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                  }`}
                >
                  {MUSCLE_DISPLAY_NAMES[muscle]}
                  {enhancedMuscles.includes(muscle) && ' ✨'}
                </span>
              ))
            ) : (
              <span className="text-gray-500 italic">
                请点击下方人体图选择肌肉群
              </span>
            )}
          </div>
          
          {/* 快速选择按钮 */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleQuickSelect(musclePresets.upperBodyPush)}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors text-sm"
            >
              上半身推
            </button>
            <button
              onClick={() => handleQuickSelect(musclePresets.upperBodyPull)}
              className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors text-sm"
            >
              上半身拉
            </button>
            <button
              onClick={() => handleQuickSelect(musclePresets.core)}
              className="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors text-sm"
            >
              核心
            </button>
            <button
              onClick={() => handleQuickSelect(musclePresets.enhanced)}
              className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors text-sm"
            >
              增强版 ✨
            </button>
            <button
              onClick={() => setSelectedMuscles([])}
              className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors text-sm"
            >
              清除
            </button>
          </div>
        </div>

        {/* 增强肌肉群控制 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
          <h3 className="text-xl font-semibold mb-4">
            🎛️ 增强肌肉群控制
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            选择哪些肌肉群使用muscle-selector-complete的高精度SVG路径：
          </p>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
            {allMuscleGroups.map(muscle => (
              <button
                key={muscle}
                onClick={() => toggleEnhancedMuscle(muscle)}
                className={`px-3 py-2 rounded-md text-sm transition-colors ${
                  enhancedMuscles.includes(muscle)
                    ? 'bg-green-500 text-white'
                    : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'
                }`}
              >
                {MUSCLE_DISPLAY_NAMES[muscle]}
                {enhancedMuscles.includes(muscle) && ' ✨'}
              </button>
            ))}
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
          {currentView === 'improved' && (
            <div>
              <h2 className="text-2xl font-semibold mb-4 text-center">
                🎯 改进版肌肉可视化
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-center mb-6">
                基于原版人体轮廓，选择性地使用muscle-selector-complete的精确路径
              </p>
              <div className="flex justify-center">
                <MuscleIllustration
                  selectedMuscles={selectedMuscles}
                  onToggleMuscle={handleMuscleToggle}
                  theme={theme}
                />
              </div>
            </div>
          )}

          {currentView === 'original' && (
            <div>
              <h2 className="text-2xl font-semibold mb-4 text-center">
                📝 原版肌肉可视化
              </h2>
              <p className="text-gray-600 dark:text-gray-300 text-center mb-6">
                原有的肌肉可视化实现
              </p>
              <div className="flex justify-center">
                <MuscleIllustration
                  selectedMuscles={selectedMuscles}
                  onToggleMuscle={handleMuscleToggle}
                  theme={theme}
                />
              </div>
            </div>
          )}

          {currentView === 'comparison' && (
            <div>
              <h2 className="text-2xl font-semibold mb-6 text-center">
                🔄 渐进式增强对比
              </h2>
              <div className="grid md:grid-cols-2 gap-8">
                {/* 改进版 */}
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-center text-green-600">
                    🎯 改进版（渐进增强）
                  </h3>
                  <div className="border-2 border-green-200 rounded-lg p-4">
                    <MuscleIllustration
                      selectedMuscles={selectedMuscles}
                      onToggleMuscle={handleMuscleToggle}
                      theme={theme}
                    />
                  </div>
                  <div className="text-sm space-y-2">
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✅</span>
                      保持原版人体轮廓
                    </div>
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✅</span>
                      保持原版样式系统
                    </div>
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✅</span>
                      选择性精度增强
                    </div>
                    <div className="flex items-center">
                      <span className="text-blue-500 mr-2">🎛️</span>
                      可控的肌肉群升级
                    </div>
                  </div>
                </div>

                {/* 原版 */}
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-center text-gray-600">
                    📝 原版
                  </h3>
                  <div className="border-2 border-gray-200 rounded-lg p-4">
                    <MuscleIllustration
                      selectedMuscles={selectedMuscles}
                      onToggleMuscle={handleMuscleToggle}
                      theme={theme}
                    />
                  </div>
                  <div className="text-sm space-y-2">
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✅</span>
                      完整人体轮廓
                    </div>
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✅</span>
                      黑色描边分隔
                    </div>
                    <div className="flex items-center">
                      <span className="text-green-500 mr-2">✅</span>
                      蓝色肌肉填充
                    </div>
                    <div className="flex items-center">
                      <span className="text-yellow-500 mr-2">⚠️</span>
                      基础SVG精度
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 技术说明 */}
        <div className="mt-8 bg-green-50 dark:bg-green-900/20 rounded-xl p-6">
          <h3 className="text-xl font-semibold mb-4">🔧 改进版技术特点</h3>
          <div className="grid md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-medium mb-2">✅ 保持原版优势：</h4>
              <ul className="space-y-1 text-gray-600 dark:text-gray-300">
                <li>• 完整的人体轮廓基底</li>
                <li>• 原版的视觉风格和颜色</li>
                <li>• 黑色描边的肌肉分隔</li>
                <li>• 蓝色系的填充效果</li>
                <li>• 完全兼容现有API</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">🎯 选择性增强：</h4>
              <ul className="space-y-1 text-gray-600 dark:text-gray-300">
                <li>• 可控的肌肉群精度升级</li>
                <li>• 逐步集成高精度SVG路径</li>
                <li>• 保持样式系统一致性</li>
                <li>• 零破坏性变更</li>
                <li>• 渐进式功能增强</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 className="font-medium mb-2">🎛️ 当前增强状态：</h4>
            <div className="flex flex-wrap gap-2">
              {enhancedMuscles.map(muscle => (
                <span key={muscle} className="px-2 py-1 bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200 rounded text-sm">
                  {MUSCLE_DISPLAY_NAMES[muscle]} ✨
                </span>
              ))}
              {enhancedMuscles.length === 0 && (
                <span className="text-gray-500 italic">当前使用原版SVG路径</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImprovedMuscleDemo; 