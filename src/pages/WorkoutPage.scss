@use '../styles/variables' as *;

.workout-page {
  min-height: 100vh;
  background: var(--bg-primary);
  padding: 2rem 1rem;
  
  @include mobile {
    padding: 1rem 0.5rem;
  }

  // 页面标题
  .page-header {
    text-align: center;
    margin-bottom: 2rem;
    
    h1 {
      color: var(--text-primary);
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      background: var(--gradient-brand);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    p {
      color: var(--text-secondary);
      font-size: 1.1rem;
      opacity: 0.8;
    }
  }

  // 当前训练区域
  .active-workout-section {
    margin-bottom: 3rem;
    
    .active-workout-card {
      background: var(--bg-surface);
      border-radius: 16px;
      padding: 1.5rem;
      border: 1px solid var(--primary-500);
      box-shadow: var(--shadow-lg);
      
      .workout-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--primary-400);
        
        @include mobile {
          flex-direction: column;
          gap: 1rem;
          align-items: stretch;
        }
        
        .workout-info {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          
          h2 {
            color: var(--text-primary);
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
          }
          
          .workout-timer {
            display: flex;
            align-items: center;
            gap: 1rem;
            
            .timer-display {
              font-size: 1.25rem;
              font-weight: 600;
              color: var(--accent-500);
              font-family: 'Monaco', 'Menlo', monospace;
            }
            
            .timer-btn {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              background: var(--gradient-primary);
              border: none;
              color: var(--text-on-accent);
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.2s ease;
              
              svg {
                width: 18px;
                height: 18px;
              }
              
              &:hover {
                transform: scale(1.05);
                box-shadow: var(--shadow-md);
              }
              
              &.pause svg {
                stroke-width: 2.5;
              }
            }
          }
        }
        
        .finish-btn {
          background: var(--gradient-success);
          color: var(--text-on-success);
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
          }
        }
      }
      
      .workout-content {
        .empty-workout {
          text-align: center;
          padding: 3rem 1rem;
          
          .empty-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            
            svg {
              width: 40px;
              height: 40px;
              stroke: var(--text-on-accent);
              stroke-width: 2;
            }
          }
          
          h3 {
            color: var(--text-primary);
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
          }
          
          p {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            opacity: 0.8;
          }
          
          .add-exercise-btn {
            background: var(--gradient-primary);
            color: var(--text-on-accent);
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease;
            
            svg {
              width: 20px;
              height: 20px;
            }
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: var(--shadow-md);
            }
          }
        }
        
        .exercises-list {
          .exercise-item {
            background: var(--bg-surface);
            border: 1px solid var(--primary-400);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            
            h4 {
              color: var(--text-primary);
              margin-bottom: 1rem;
              font-weight: 600;
            }
            
            .sets-grid {
              .set-row {
                display: grid;
                grid-template-columns: 30px 1fr auto 1fr auto 40px;
                gap: 0.5rem;
                align-items: center;
                margin-bottom: 0.5rem;
                padding: 0.5rem;
                background: rgba(79, 195, 247, 0.03);
                border-radius: 8px;
                
                .set-number {
                  color: var(--color-text-secondary);
                  font-weight: 600;
                  text-align: center;
                }
                
                .weight-input,
                .reps-input {
                  background: var(--bg-surface);
                  border: 1px solid var(--primary-500);
                  border-radius: 6px;
                  padding: 0.5rem;
                  color: var(--text-primary);
                  text-align: center;
                  font-weight: 600;
                  
                  &:focus {
                    outline: none;
                    border-color: var(--accent-500);
                    box-shadow: var(--focus-ring);
                  }
                }
                
                .unit {
                  color: var(--text-secondary);
                  font-size: 0.9rem;
                }
                
                .complete-btn {
                  width: 32px;
                  height: 32px;
                  border-radius: 50%;
                  border: 2px solid var(--primary-400);
                  background: transparent;
                  color: transparent;
                  cursor: pointer;
                  transition: all 0.2s ease;
                  
                  &.completed {
                    background: var(--gradient-success);
                    border-color: var(--success-500);
                    color: var(--text-on-success);
                  }
                  
                  &:hover {
                    transform: scale(1.1);
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 快速开始区域
  .quick-start-section {
    margin-bottom: 3rem;
    
    h2 {
      color: var(--text-primary);
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      text-align: center;
    }
    
    .quick-start-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
      
      @media (max-width: 1024px) {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.25rem;
      }
      
      @include mobile {
        grid-template-columns: 1fr;
        gap: 1rem;
      }
      
      @media (max-width: 480px) {
        gap: 0.75rem;
      }
      
      .quick-start-card {
        background: var(--bg-surface);
        border: 1px solid var(--primary-500);
        border-radius: 16px;
        padding: 2rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          border-color: var(--accent-500);
          box-shadow: var(--shadow-lg);
        }
        
        .card-icon {
          width: 60px;
          height: 60px;
          margin: 0 auto 1rem;
          background: var(--gradient-primary);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          
          svg {
            width: 30px;
            height: 30px;
            stroke: var(--text-on-accent);
            stroke-width: 2;
          }
        }
        
        h3 {
          color: var(--text-primary);
          font-size: 1.25rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }
        
        p {
          color: var(--text-secondary);
          opacity: 0.8;
        }
        
        &.routine .card-icon {
          background: var(--gradient-brand);
        }
        
        &.previous .card-icon {
          background: var(--gradient-warning);
        }
      }
    }
  }

  // 统计数据区域
  .stats-section {
    margin-bottom: 3rem;
    
    h2 {
      color: var(--text-primary);
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1.5rem;
      text-align: center;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1.5rem;
      
      @media (max-width: 1024px) {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1.25rem;
      }
      
      @include mobile {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
      }
      
      @media (max-width: 480px) {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }
      
      .stat-card {
        background: var(--bg-surface);
        border: 1px solid var(--primary-500);
        border-radius: 16px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.2s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-md);
        }
        
        .stat-icon {
          width: 50px;
          height: 50px;
          margin: 0 auto 1rem;
          background: var(--gradient-primary);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          
          svg {
            width: 24px;
            height: 24px;
            stroke: var(--text-on-accent);
            stroke-width: 2;
          }
        }
        
        .stat-content {
          .stat-value {
            display: block;
            color: var(--text-primary);
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
          }
          
          .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
            opacity: 0.8;
          }
        }
      }
    }
  }

  // 最近训练区域
  .recent-workouts-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      
      h2 {
        color: var(--color-text-primary);
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
      }
      
      .view-all-btn {
        background: none;
        border: none;
        color: var(--color-primary);
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        transition: all 0.2s ease;
        
        svg {
          width: 16px;
          height: 16px;
        }
        
        &:hover {
          opacity: 0.8;
        }
      }
    }
    
    .workouts-list {
      .workout-item {
        background: var(--color-background-secondary);
        border: 1px solid rgba(79, 195, 247, 0.2);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        display: flex;
        gap: 1rem;
        align-items: center;
        transition: all 0.2s ease;
        
        &:hover {
          transform: translateX(4px);
          border-color: var(--color-primary);
        }
        
        .workout-date {
          min-width: 60px;
          text-align: center;
          background: linear-gradient(135deg, #4fc3f7, #29b6f6);
          color: white;
          border-radius: 12px;
          padding: 0.75rem 0.5rem;
          
          .month {
            display: block;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            opacity: 0.9;
          }
          
          .day {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            margin-top: 0.25rem;
          }
        }
        
        .workout-details {
          flex: 1;
          
          h3 {
            color: var(--color-text-primary);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
          }
          
          .workout-meta {
            display: flex;
            gap: 1rem;
            margin-bottom: 0.75rem;
            
            @include mobile {
              flex-direction: column;
              gap: 0.25rem;
            }
            
            .duration,
            .exercises-count {
              display: flex;
              align-items: center;
              gap: 0.25rem;
              color: var(--color-text-secondary);
              font-size: 0.9rem;
              
              svg {
                width: 14px;
                height: 14px;
              }
            }
          }
          
          .exercises-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            
            .exercise-tag {
              background: rgba(79, 195, 247, 0.1);
              color: var(--color-primary);
              padding: 0.25rem 0.5rem;
              border-radius: 4px;
              font-size: 0.8rem;
              font-weight: 500;
            }
            
            .more-exercises {
              color: var(--color-text-secondary);
              font-size: 0.8rem;
              opacity: 0.6;
            }
          }
        }
        
        .action-btn {
          background: none;
          border: none;
          color: var(--color-text-secondary);
          cursor: pointer;
          padding: 0.5rem;
          border-radius: 8px;
          transition: all 0.2s ease;
          
          svg {
            width: 20px;
            height: 20px;
          }
          
          &:hover {
            background: rgba(79, 195, 247, 0.1);
            color: var(--color-primary);
          }
        }
      }
    }
  }
}

// 响应式适配
@include tablet {
  .workout-page {
    padding: 1.5rem;
    
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .quick-start-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

@include mobile {
  .workout-page {
    .stats-grid {
      grid-template-columns: 1fr 1fr;
      gap: 0.75rem;
      
      .stat-card {
        padding: 1rem;
        
        .stat-icon {
          width: 40px;
          height: 40px;
          
          svg {
            width: 20px;
            height: 20px;
          }
        }
        
        .stat-content .stat-value {
          font-size: 1.5rem;
        }
      }
    }
    
    .workout-item {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 1rem;
      
      .workout-date {
        align-self: flex-start;
      }
    }
  }
} 