import React, { useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { Link } from 'react-router-dom';
import Icon from '../../components/common/Icon';
import { IconName /* , getIconClass */ } from '../../utils/iconMapping';
import './IconShowcase.scss';

/**
 * 图标展示页面
 * 展示Pixel Icon Library所有图标及其在项目中的使用方式
 */
const IconShowcase: React.FC = () => {
  const { theme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');

  // 导航类图标
  const navigationIcons: Array<{name: IconName, label: string}> = [
    { name: 'dashboard', label: '仪表盘' },
    { name: 'workout', label: '训练' },
    { name: 'profile', label: '个人资料' },
    { name: 'feed', label: '动态' },
    { name: 'routines', label: '计划' },
    { name: 'exercises', label: '运动库' },
    { name: 'settings', label: '设置' },
  ];

  // 功能按钮图标
  const actionIcons: Array<{name: IconName, label: string}> = [
    { name: 'add', label: '添加' },
    { name: 'remove', label: '删除' },
    { name: 'edit', label: '编辑' },
    { name: 'search', label: '搜索' },
    { name: 'check', label: '确认' },
    { name: 'close', label: '关闭' },
    { name: 'refresh', label: '刷新' },
  ];

  // 健身数据图标
  const fitnessIcons: Array<{name: IconName, label: string}> = [
    { name: 'heart', label: '心率' },
    { name: 'water', label: '水分' },
    { name: 'steps', label: '步数' },
    { name: 'nutrition', label: '营养' },
    { name: 'sleep', label: '睡眠' },
    { name: 'trophy', label: '成就' },
    { name: 'chart', label: '图表' },
    { name: 'calendar', label: '日历' },
  ];

  // 所有原始 Pixel Icon Library 图标 - 使用实际存在的图标
  const allPixelIcons = [
    'home', 'heart', 'fire', 'trophy', 'user', 'users', 'bell', 'cog', 
    'search', 'plus', 'minus', 'edit', 'trash', 'times', 'check', 
    'calender', 'clipboard', 'bookmark', 'star', 'moon', 'sun', 'bars',
    'arrow-left', 'arrow-right', 'arrow-up', 'arrow-down', 'chart-line',
    'refresh', 'globe', 'plane', 'lock', 'unlock', 'eye', 'download',
    'upload', 'share', 'comment', 'message', 'envelope', 'phone-ringing-high',
    'clock', 'folder', 'file-import', 'image', 'music', 'code',
    'lightbulb', 'sparkles', 'crown', 'flag', 'pen', 'save'
  ];

  // 过滤图标
  const filteredPixelIcons = searchTerm 
    ? allPixelIcons.filter(icon => icon.includes(searchTerm.toLowerCase()))
    : allPixelIcons;

  return (
          <div className={`icon-showcase theme-${theme}`}>
      <header className="showcase-header">
        <h1>Pixel Icon Library 图标展示</h1>
        <p className="description">此页面展示了FitMaster应用中使用的所有图标，包括导航图标、功能按钮图标和健身数据图标。</p>
        
        <div className="guide-link-container">
          <Link to="/icons/guide" className="guide-link">
            <Icon name="book" size="small" />
            <span>查看图标使用指南</span>
            <Icon name="arrow-right" size="small" />
          </Link>
        </div>
        
        <div className="search-container">
          <input
            type="text"
            placeholder="搜索图标..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
          <Icon name="search" className="search-icon" />
        </div>
      </header>

      <section className="icon-section">
        <h2>应用导航图标</h2>
        <p className="section-desc">这些图标用于应用的主导航和菜单。</p>
        <div className="icon-grid">
          {navigationIcons.map((icon) => (
            <div key={icon.name} className="icon-item">
              <Icon name={icon.name} size="medium" />
              <span className="icon-name">{icon.name}</span>
              <span className="icon-label">{icon.label}</span>
            </div>
          ))}
        </div>
      </section>

      <section className="icon-section">
        <h2>功能按钮图标</h2>
        <p className="section-desc">这些图标用于应用中的各种功能按钮。</p>
        <div className="icon-grid">
          {actionIcons.map((icon) => (
            <div key={icon.name} className="icon-item">
              <Icon name={icon.name} size="medium" />
              <span className="icon-name">{icon.name}</span>
              <span className="icon-label">{icon.label}</span>
            </div>
          ))}
        </div>
      </section>

      <section className="icon-section">
        <h2>健身数据图标</h2>
        <p className="section-desc">这些图标用于表示各种健身数据和指标。</p>
        <div className="icon-grid">
          {fitnessIcons.map((icon) => (
            <div key={icon.name} className="icon-item">
              <Icon name={icon.name} size="medium" />
              <span className="icon-name">{icon.name}</span>
              <span className="icon-label">{icon.label}</span>
            </div>
          ))}
        </div>
      </section>

      <section className="icon-section">
        <h2>所有 Pixel Icon Library 图标</h2>
        <p className="section-desc">以下是 Pixel Icon Library 中所有可用的图标。</p>
        <div className="icon-grid all-icons">
          {filteredPixelIcons.map((icon) => (
            <div key={icon} className="icon-item">
              <i className={`hn hn-${icon}`}></i>
              <span className="icon-name">{icon}</span>
            </div>
          ))}
        </div>
      </section>

      <section className="icon-section">
        <h2>图标尺寸示例</h2>
        <p className="section-desc">Icon组件支持多种尺寸，方便在不同场景下使用。</p>
        <div className="size-examples">
          <div className="size-example">
            <Icon name="workout" size="small" />
            <span className="size-label">Small</span>
            <code>size="small"</code>
          </div>
          <div className="size-example">
            <Icon name="workout" size="medium" />
            <span className="size-label">Medium</span>
            <code>size="medium"</code>
          </div>
          <div className="size-example">
            <Icon name="workout" size="large" />
            <span className="size-label">Large</span>
            <code>size="large"</code>
          </div>
          <div className="size-example">
            <Icon name="workout" size="xlarge" />
            <span className="size-label">XLarge</span>
            <code>size="xlarge"</code>
          </div>
        </div>
      </section>

      <section className="icon-section">
        <h2>使用方法</h2>
        <div className="usage-examples">
          <div className="usage-example">
            <h3>1. 使用Icon组件（推荐）</h3>
            <div className="code-block">
              <pre><code>{`import Icon from '../components/common/Icon';

// 在组件中使用
<Icon name="workout" size="medium" />`}</code></pre>
            </div>
          </div>
          
          <div className="usage-example">
            <h3>2. 直接使用CSS类</h3>
            <div className="code-block">
              <pre><code>{`// 在HTML中直接使用
<i className="hn hn-dumbbell"></i>`}</code></pre>
            </div>
          </div>
        </div>
      </section>

      <section className="icon-section">
        <h2>应用场景示例</h2>
        <div className="example-scenarios">
          <div className="scenario">
            <h3>底部导航</h3>
            <div className="bottom-nav-demo">
              {navigationIcons.slice(0, 5).map((icon) => (
                <div key={icon.name} className={`nav-item ${icon.name === 'dashboard' ? 'active' : ''}`}>
                  <Icon name={icon.name} size="small" />
                  <span>{icon.label}</span>
                </div>
              ))}
            </div>
          </div>
          
          <div className="scenario">
            <h3>功能按钮</h3>
            <div className="action-buttons-demo">
              <button className="action-btn primary">
                <Icon name="add" size="small" />
                <span>添加训练</span>
              </button>
              <button className="action-btn">
                <Icon name="edit" size="small" />
                <span>编辑</span>
              </button>
              <button className="action-btn danger">
                <Icon name="delete" size="small" />
                <span>删除</span>
              </button>
            </div>
          </div>
          
          <div className="scenario">
            <h3>数据卡片</h3>
            <div className="data-card-demo">
              <div className="data-card">
                <div className="card-header">
                  <Icon name="heart" size="medium" className="card-icon" />
                  <h4>心率</h4>
                </div>
                <div className="card-body">
                  <div className="data-value">75 <span className="unit">BPM</span></div>
                  <div className="data-desc">静息心率</div>
                </div>
              </div>
              
              <div className="data-card">
                <div className="card-header">
                  <Icon name="steps" size="medium" className="card-icon" />
                  <h4>步数</h4>
                </div>
                <div className="card-body">
                  <div className="data-value">8,542</div>
                  <div className="data-desc">今日步数</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default IconShowcase; 