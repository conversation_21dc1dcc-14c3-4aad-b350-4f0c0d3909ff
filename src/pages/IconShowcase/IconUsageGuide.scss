.icon-usage-guide {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  font-family: 'Arial', sans-serif;
  
  // 头部样式
  .guide-header {
    margin-bottom: 40px;
    
    h1 {
      font-size: 32px;
      margin-bottom: 16px;
      color: var(--primary-color, #5c6bc0);
      border-left: 4px solid var(--primary-color, #5c6bc0);
      padding-left: 16px;
    }
    
    .description {
      font-size: 16px;
      line-height: 1.6;
      color: var(--text-secondary, #666);
      max-width: 800px;
    }
  }
  
  // 部分样式
  .guide-section {
    margin-bottom: 48px;
    background: var(--surface-color, #fff);
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    h2 {
      font-size: 24px;
      margin-bottom: 16px;
      color: var(--text-primary, #333);
      border-bottom: 2px solid var(--border-color, #eee);
      padding-bottom: 8px;
    }
    
    p {
      margin-bottom: 20px;
      line-height: 1.6;
      color: var(--text-secondary, #666);
    }
  }
  
  // 类别框
  .category-box {
    margin-bottom: 32px;
    border: 1px solid var(--border-color, #eee);
    border-radius: 8px;
    padding: 20px;
    background-color: var(--surface-secondary, #f9fafb);
    
    h3 {
      font-size: 20px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--text-primary, #333);
      
      .icon {
        color: var(--primary-color, #5c6bc0);
      }
    }
    
    p {
      margin-bottom: 16px;
    }
    
    .example-box {
      background: var(--surface-color, #fff);
      border-radius: 8px;
      padding: 16px;
      
      h4 {
        font-size: 16px;
        margin-bottom: 12px;
        color: var(--text-primary, #333);
      }
    }
  }
  
  // 示例容器
  .bottom-nav-example {
    display: flex;
    background: var(--surface-color, #fff);
    border: 1px solid var(--border-color, #eee);
    border-radius: 24px;
    padding: 12px 24px;
    margin-bottom: 16px;
    
    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 8px 16px;
      
      .icon {
        margin-bottom: 4px;
        color: var(--text-secondary, #666);
      }
      
      span {
        font-size: 12px;
        color: var(--text-secondary, #666);
      }
      
      &.active {
        .icon, span {
          color: var(--primary-color, #5c6bc0);
        }
      }
    }
  }
  
  .action-buttons-example {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    
    .action-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      border-radius: 6px;
      border: none;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s;
      
      &.primary {
        background: var(--primary-color, #5c6bc0);
        color: white;
        
        .icon {
          color: white;
        }
        
        &:hover {
          background: color-mix(in srgb, #5c6bc0, black 10%);
        }
      }
      
      &.secondary {
        background: var(--surface-color, #fff);
        border: 1px solid var(--border-color, #e0e0e0);
        color: var(--text-primary, #333);
        
        &:hover {
          background: var(--surface-secondary, #f5f5f5);
        }
      }
    }
  }
  
  .data-card-example {
    margin-bottom: 16px;
    
    .data-card {
      display: flex;
      align-items: center;
      gap: 16px;
      background: var(--surface-secondary, #f9fafb);
      border-radius: 12px;
      padding: 16px;
      max-width: 220px;
      
      .icon {
        color: var(--primary-color, #5c6bc0);
        background: rgba(92, 107, 192, 0.1);
        padding: 12px;
        border-radius: 50%;
      }
      
      .card-content {
        display: flex;
        flex-direction: column;
        
        .data-value {
          font-size: 24px;
          font-weight: 700;
          color: var(--text-primary, #333);
        }
        
        .data-label {
          font-size: 14px;
          color: var(--text-secondary, #666);
        }
      }
    }
  }
  
  // 代码块样式
  .code-block {
    background: #1e1e1e;
    border-radius: 8px;
    margin: 16px 0;
    overflow: hidden;
    
    pre {
      margin: 0;
      padding: 16px;
      overflow-x: auto;
      
      code {
        color: #e0e0e0;
        font-family: 'Consolas', 'Courier New', monospace;
        line-height: 1.5;
      }
    }
  }
  
  // 尺寸表格
  .sizes-table {
    margin: 24px 0;
    overflow-x: auto;
    
    table {
      width: 100%;
      border-collapse: collapse;
      
      th, td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid var(--border-color, #eee);
      }
      
      th {
        background: var(--surface-secondary, #f9fafb);
        color: var(--text-primary, #333);
        font-weight: 600;
      }
      
      td {
        vertical-align: middle;
        
        code {
          background: var(--surface-secondary, #f9fafb);
          padding: 2px 6px;
          border-radius: 4px;
          font-family: 'Consolas', 'Courier New', monospace;
          font-size: 14px;
        }
        
        .icon {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
  
  // 颜色示例
  .colors-example {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 16px;
    margin: 24px 0;
    
    .color-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      
      .icon-wrapper {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        
        &.primary .icon {
          color: var(--primary-color, #5c6bc0);
        }
        
        &.secondary .icon {
          color: var(--secondary-color, #757575);
        }
        
        &.success .icon {
          color: var(--success-color, #4caf50);
        }
        
        &.warning .icon {
          color: var(--warning-color, #ff9800);
        }
        
        &.error .icon {
          color: var(--error-color, #f44336);
        }
        
        &.disabled .icon {
          color: var(--disabled-color, #bdbdbd);
          opacity: 0.5;
        }
      }
      
      span {
        font-size: 14px;
        color: var(--text-secondary, #666);
      }
    }
  }
  
  // 文本图标示例
  .text-icon-examples {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
    margin: 24px 0;
    
    .example {
      h4 {
        font-size: 16px;
        margin-bottom: 16px;
      }
      
      .example-item {
        display: flex;
        flex-direction: column;
        gap: 24px;
      }
      
      .good-example, .bad-example {
        padding: 16px;
        border-radius: 8px;
        
        .note {
          margin-top: 12px;
          font-size: 14px;
          color: var(--text-secondary, #666);
        }
      }
      
      .good-example {
        background: rgba(76, 175, 80, 0.1);
        border: 1px dashed #4caf50;
      }
      
      .bad-example {
        background: rgba(244, 67, 54, 0.1);
        border: 1px dashed #f44336;
      }
      
      .btn {
        display: inline-flex;
        align-items: center;
        padding: 8px 16px;
        border-radius: 4px;
        border: none;
        background: var(--primary-color, #5c6bc0);
        color: white;
        font-size: 14px;
        cursor: pointer;
        
        &.with-icon {
          .icon {
            margin-right: 8px;
          }
          
          &.bad-spacing .icon {
            margin-right: 32px;
          }
        }
        
        &.icon-only {
          padding: 8px;
        }
      }
      
      .aligned-item {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .misaligned-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        
        .icon {
          margin-top: 8px;
        }
      }
    }
  }
  
  // 用法示例
  .usage-examples {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
    margin: 24px 0;
    
    .usage-example {
      h3 {
        font-size: 18px;
        margin-bottom: 16px;
        color: var(--text-primary, #333);
      }
      
      .note {
        margin-top: 12px;
        font-size: 14px;
        font-style: italic;
        color: var(--text-secondary, #666);
      }
    }
  }
  
  // 无障碍性列表
  .accessibility-list {
    list-style: none;
    padding: 0;
    margin: 24px 0;
    
    li {
      margin-bottom: 24px;
      
      .item-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary, #333);
        display: block;
        margin-bottom: 12px;
      }
      
      p {
        margin: 12px 0;
      }
      
      .example-comparison {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 16px;
        margin-top: 16px;
      }
    }
  }
  
  // 暗色主题样式
  &.theme-dark {
    background-color: #121212;
    color: #e0e0e0;
    
    .guide-header {
      h1 {
        color: #bb86fc;
        border-left-color: #bb86fc;
      }
      
      .description {
        color: #bbbbbb;
      }
    }
    
    .guide-section {
      background: #1e1e1e;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      
      h2 {
        color: #e0e0e0;
        border-bottom-color: #333;
      }
      
      p {
        color: #bbbbbb;
      }
    }
    
    .category-box {
      background-color: #262626;
      border-color: #333;
      
      h3 {
        color: #e0e0e0;
        
        .icon {
          color: #bb86fc;
        }
      }
      
      .example-box {
        background: #1e1e1e;
        
        h4 {
          color: #e0e0e0;
        }
      }
    }
    
    .bottom-nav-example {
      background: #1e1e1e;
      border-color: #333;
      
      .nav-item {
        .icon, span {
          color: #bbbbbb;
        }
        
        &.active {
          .icon, span {
            color: #bb86fc;
          }
        }
      }
    }
    
    .action-btn {
      &.primary {
        background: #bb86fc;
      }
      
      &.secondary {
        background: #333;
        border-color: #444;
        color: #e0e0e0;
        
        &:hover {
          background: #444;
        }
      }
    }
    
    .data-card {
      background: #262626;
      
      .icon {
        color: #bb86fc;
        background: rgba(187, 134, 252, 0.1);
      }
      
      .data-value {
        color: #e0e0e0;
      }
      
      .data-label {
        color: #bbbbbb;
      }
    }
    
    .code-block {
      background: #000;
    }
    
    .sizes-table {
      table {
        th {
          background: #262626;
          color: #e0e0e0;
        }
        
        td {
          border-bottom-color: #333;
          
          code {
            background: #333;
            color: #e0e0e0;
          }
        }
      }
    }
    
    .good-example {
      background: rgba(76, 175, 80, 0.05);
    }
    
    .bad-example {
      background: rgba(244, 67, 54, 0.05);
    }
    
    .usage-example h3,
    .item-title {
      color: #e0e0e0;
    }
    
    .note {
      color: #bbbbbb;
    }
  }
  
  // 响应式调整
  @media (max-width: 768px) {
    padding: 16px;
    
    .guide-header h1 {
      font-size: 24px;
    }
    
    .text-icon-examples,
    .usage-examples {
      grid-template-columns: 1fr;
    }
    
    .sizes-table {
      table {
        min-width: 600px;
      }
    }
  }
} 