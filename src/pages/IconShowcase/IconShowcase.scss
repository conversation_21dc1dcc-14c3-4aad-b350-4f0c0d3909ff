.icon-showcase {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  font-family: 'Arial', sans-serif;
  
  // 头部样式
  .showcase-header {
    margin-bottom: 40px;
    
    h1 {
      font-size: 32px;
      margin-bottom: 12px;
      color: var(--primary-color);
    }
    
    .description {
      font-size: 16px;
      color: var(--text-secondary, #666);
      max-width: 800px;
      margin-bottom: 24px;
    }
    
    // 指南链接样式
    .guide-link-container {
      margin-bottom: 20px;
      
      .guide-link {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: var(--surface-color, #fff);
        border: 1px solid var(--border-color, #e0e0e0);
        border-radius: 8px;
        text-decoration: none;
        color: var(--primary-color, #5c6bc0);
        font-weight: 500;
        transition: all 0.2s;
        
        &:hover {
          background: var(--surface-secondary, #f9fafb);
          transform: translateY(-1px);
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        .icon {
          color: var(--primary-color, #5c6bc0);
        }
        
        span {
          flex: 1;
        }
      }
    }
    
    .search-container {
      position: relative;
      max-width: 500px;
      
      .search-input {
        width: 100%;
        padding: 12px 16px;
        padding-right: 40px;
        border-radius: 8px;
        border: 1px solid var(--border-color, #e0e0e0);
        font-size: 16px;
        
        &:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px rgba(92, 107, 192, 0.2);
        }
      }
      
      .search-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary, #666);
      }
    }
  }
  
  // 图标部分通用样式
  .icon-section {
    margin-bottom: 48px;
    
    h2 {
      font-size: 24px;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--border-color, #e0e0e0);
    }
    
    .section-desc {
      margin-bottom: 20px;
      color: var(--text-secondary, #666);
    }
  }
  
  // 图标网格
  .icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 16px;
    
    &.all-icons {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
    
    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px 8px;
      border-radius: 8px;
      background: var(--surface-color, #fff);
      border: 1px solid var(--border-color, #e0e0e0);
      transition: all 0.2s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-color: var(--primary-color);
      }
      
      .icon, i {
        font-size: 24px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12px;
        padding: 8px;
        background: rgba(0, 0, 0, 0.03);
        border-radius: 8px;
      }
      
      .icon-name {
        font-size: 12px;
        color: var(--primary-color);
        margin-bottom: 4px;
        font-weight: 500;
      }
      
      .icon-label {
        font-size: 12px;
        color: var(--text-secondary, #666);
      }
    }
  }
  
  // 尺寸示例
  .size-examples {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 24px;
    background: var(--surface-color, #fff);
    border: 1px solid var(--border-color, #e0e0e0);
    border-radius: 8px;
    padding: 24px;
    
    .size-example {
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .icon {
        margin-bottom: 12px;
        background: rgba(0, 0, 0, 0.03);
        border-radius: 8px;
        padding: 12px;
      }
      
      .size-label {
        font-weight: 500;
        margin-bottom: 4px;
        color: var(--primary-color);
      }
      
      code {
        font-size: 12px;
        background: #f5f5f5;
        padding: 4px 8px;
        border-radius: 4px;
      }
    }
  }
  
  // 使用方法
  .usage-examples {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
    
    .usage-example {
      h3 {
        font-size: 18px;
        margin-bottom: 16px;
      }
      
      .code-block {
        background: #1e1e1e;
        border-radius: 8px;
        overflow: hidden;
        
        pre {
          margin: 0;
          padding: 16px;
          overflow-x: auto;
          
          code {
            color: #e0e0e0;
            font-family: 'Consolas', 'Courier New', monospace;
          }
        }
      }
    }
  }
  
  // 应用场景示例
  .example-scenarios {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
    
    .scenario {
      h3 {
        font-size: 18px;
        margin-bottom: 16px;
      }
      
      // 底部导航示例
      .bottom-nav-demo {
        display: flex;
        justify-content: space-between;
        background: var(--surface-color, #fff);
        border: 1px solid var(--border-color, #e0e0e0);
        border-radius: 8px;
        padding: 12px;
        
        .nav-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 8px;
          min-width: 60px;
          
          .icon {
            margin-bottom: 4px;
          }
          
          span {
            font-size: 12px;
          }
          
          &.active {
            color: var(--primary-color);
          }
        }
      }
      
      // 功能按钮示例
      .action-buttons-demo {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
        
        .action-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          border-radius: 4px;
          border: none;
          background: #f5f5f5;
          cursor: pointer;
          transition: all 0.2s;
          
          &:hover {
            background: #e0e0e0;
          }
          
          &.primary {
            background: var(--primary-color);
            color: white;
            
            &:hover {
              background: color-mix(in srgb, #5c6bc0, black 10%);
            }
          }
          
          &.danger {
            background: var(--error-color, #f44336);
            color: white;
            
            &:hover {
              background: color-mix(in srgb, #f44336, black 10%);
            }
          }
        }
      }
      
      // 数据卡片示例
      .data-card-demo {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
        
        .data-card {
          flex: 1;
          min-width: 160px;
          border-radius: 8px;
          overflow: hidden;
          border: 1px solid var(--border-color, #e0e0e0);
          background: var(--surface-color, #fff);
          
          .card-header {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(0, 0, 0, 0.03);
            padding: 12px;
            
            .card-icon {
              color: var(--primary-color);
            }
            
            h4 {
              margin: 0;
              font-size: 16px;
            }
          }
          
          .card-body {
            padding: 16px;
            
            .data-value {
              font-size: 24px;
              font-weight: 700;
              
              .unit {
                font-size: 14px;
                font-weight: 400;
                opacity: 0.7;
              }
            }
            
            .data-desc {
              font-size: 12px;
              color: var(--text-secondary, #666);
              margin-top: 4px;
            }
          }
        }
      }
    }
  }
  
  // 暗色主题样式
  &.theme-dark {
    background-color: #121212;
    color: #e0e0e0;
    
    .showcase-header {
      .description {
        color: #aaa;
      }
      
      .guide-link-container {
        .guide-link {
          background: #222;
          border-color: #444;
          color: #bb86fc;
          
          &:hover {
            background: #333;
          }
          
          .icon {
            color: #bb86fc;
          }
        }
      }
      
      .search-container {
        .search-input {
          background: #222;
          color: #e0e0e0;
          border-color: #444;
          
          &:focus {
            border-color: var(--primary-color);
          }
        }
      }
    }
    
    .icon-section h2 {
      border-bottom-color: #444;
    }
    
    .icon-grid .icon-item {
      background: #222;
      border-color: #444;
      
      .icon, i {
        background: #333;
      }
      
      .icon-label {
        color: #aaa;
      }
    }
    
    .size-examples {
      background: #222;
      border-color: #444;
      
      .size-example {
        .icon {
          background: #333;
        }
        
        code {
          background: #333;
          color: #e0e0e0;
        }
      }
    }
    
    .usage-examples .usage-example .code-block {
      background: #000;
    }
    
    .example-scenarios {
      .bottom-nav-demo {
        background: #222;
        border-color: #444;
      }
      
      .action-buttons-demo .action-btn {
        background: #333;
        color: #e0e0e0;
        
        &:hover {
          background: #444;
        }
      }
      
      .data-card-demo .data-card {
        background: #222;
        border-color: #444;
        
        .card-header {
          background: #333;
        }
        
        .card-body .data-desc {
          color: #aaa;
        }
      }
    }
  }
  
  // 响应式调整
  @media (max-width: 768px) {
    padding: 16px;
    
    .showcase-header {
      margin-bottom: 32px;
      
      h1 {
        font-size: 28px;
      }
      
      .description {
        font-size: 14px;
      }
      
      .search-container {
        max-width: 100%;
        
        .search-input {
          font-size: 16px;
          padding: 10px 14px;
        }
      }
    }
    
    .icon-section {
      margin-bottom: 32px;
      
      h2 {
        font-size: 20px;
      }
    }
    
    .icon-grid {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 12px;
      
      &.all-icons {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
      }
      
      .icon-item {
        padding: 12px 6px;
        
        .icon, i {
          font-size: 20px;
          width: 32px;
          height: 32px;
          margin-bottom: 8px;
        }
        
        .icon-name {
          font-size: 11px;
        }
        
        .icon-label {
          font-size: 11px;
        }
      }
    }
    
    .size-examples {
      flex-direction: column;
      align-items: center;
      gap: 16px;
      padding: 16px;
      
      .size-example {
        .icon {
          margin-bottom: 8px;
          padding: 8px;
        }
      }
    }
    
    .usage-examples,
    .example-scenarios {
      grid-template-columns: 1fr;
    }
  }
  
  // 小屏幕进一步调整
  @media (max-width: 480px) {
    padding: 12px;
    
    .showcase-header {
      h1 {
        font-size: 24px;
      }
      
      .description {
        font-size: 13px;
      }
    }
    
    .icon-grid {
      grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
      gap: 8px;
      
      &.all-icons {
        grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
      }
      
      .icon-item {
        padding: 8px 4px;
        
        .icon, i {
          font-size: 18px;
          width: 28px;
          height: 28px;
          margin-bottom: 6px;
        }
        
        .icon-name {
          font-size: 10px;
        }
        
        .icon-label {
          font-size: 10px;
        }
      }
    }
  }
} 