import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import Icon from '../../components/common/Icon';
import './IconUsageGuide.scss';

/**
 * 图标使用指南页面
 * 提供图标在项目中的使用规范和最佳实践
 */
const IconUsageGuide: React.FC = () => {
  const { theme } = useTheme();

  return (
          <div className={`icon-usage-guide theme-${theme}`}>
      <header className="guide-header">
        <h1>图标使用指南</h1>
        <p className="description">本指南提供了在FitMaster应用中使用Pixel Icon Library图标的规范和最佳实践。</p>
      </header>

      <section className="guide-section">
        <h2>1. 图标用途分类</h2>
        <p>根据不同的使用场景和功能，我们将图标分为几个主要类别：</p>
        
        <div className="category-box">
          <h3><Icon name="dashboard" size="small" /> 导航图标</h3>
          <p>用于主导航栏、底部标签栏和次级导航，帮助用户理解各页面功能。</p>
          <div className="example-box">
            <div className="example">
              <h4>底部导航栏</h4>
              <div className="bottom-nav-example">
                <div className="nav-item active">
                  <Icon name="dashboard" size="small" />
                  <span>仪表盘</span>
                </div>
                <div className="nav-item">
                  <Icon name="workout" size="small" />
                  <span>训练</span>
                </div>
                <div className="nav-item">
                  <Icon name="feed" size="small" />
                  <span>动态</span>
                </div>
              </div>
              <div className="code-block">
                <pre><code>{`<div className="bottom-nav-item">
  <Icon name="dashboard" size="small" />
  <span>仪表盘</span>
</div>`}</code></pre>
              </div>
            </div>
          </div>
        </div>
        
        <div className="category-box">
          <h3><Icon name="add" size="small" /> 操作图标</h3>
          <p>用于按钮、工具栏和交互元素，表示用户可以执行的操作。</p>
          <div className="example-box">
            <div className="example">
              <h4>功能按钮</h4>
              <div className="action-buttons-example">
                <button className="action-btn primary">
                  <Icon name="add" size="small" />
                  <span>添加训练</span>
                </button>
                <button className="action-btn secondary">
                  <Icon name="edit" size="small" />
                  <span>编辑</span>
                </button>
              </div>
              <div className="code-block">
                <pre><code>{`<button className="action-btn primary">
  <Icon name="add" size="small" />
  <span>添加训练</span>
</button>`}</code></pre>
              </div>
            </div>
          </div>
        </div>
        
        <div className="category-box">
          <h3><Icon name="heart" size="small" /> 数据图标</h3>
          <p>用于展示健身和健康数据的卡片、图表和指标。</p>
          <div className="example-box">
            <div className="example">
              <h4>数据卡片</h4>
              <div className="data-card-example">
                <div className="data-card">
                  <Icon name="heart" size="medium" />
                  <div className="card-content">
                    <span className="data-value">75</span>
                    <span className="data-label">静息心率</span>
                  </div>
                </div>
              </div>
              <div className="code-block">
                <pre><code>{`<div className="data-card">
  <Icon name="heart" size="medium" />
  <div className="card-content">
    <span className="data-value">75</span>
    <span className="data-label">静息心率</span>
  </div>
</div>`}</code></pre>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="guide-section">
        <h2>2. 图标大小规范</h2>
        <p>我们定义了四种标准图标尺寸，以保持整个应用的一致性：</p>
        
        <div className="sizes-table">
          <table>
            <thead>
              <tr>
                <th>尺寸名称</th>
                <th>推荐用途</th>
                <th>示例</th>
                <th>CSS类</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>small</td>
                <td>次级导航、内联操作、小型按钮</td>
                <td><Icon name="workout" size="small" /></td>
                <td><code>icon-small</code></td>
              </tr>
              <tr>
                <td>medium</td>
                <td>主导航、标准按钮、列表项</td>
                <td><Icon name="workout" size="medium" /></td>
                <td><code>icon-medium</code></td>
              </tr>
              <tr>
                <td>large</td>
                <td>强调元素、卡片头部、页面标题</td>
                <td><Icon name="workout" size="large" /></td>
                <td><code>icon-large</code></td>
              </tr>
              <tr>
                <td>xlarge</td>
                <td>特殊强调、页面空状态、大型展示</td>
                <td><Icon name="workout" size="xlarge" /></td>
                <td><code>icon-xlarge</code></td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>

      <section className="guide-section">
        <h2>3. 图标颜色和状态</h2>
        <p>图标颜色应遵循应用的颜色系统，与周围UI元素保持一致：</p>
        
        <div className="colors-example">
          <div className="color-item">
            <div className="icon-wrapper primary">
              <Icon name="workout" size="medium" />
            </div>
            <span>主要颜色</span>
          </div>
          <div className="color-item">
            <div className="icon-wrapper secondary">
              <Icon name="workout" size="medium" />
            </div>
            <span>次要颜色</span>
          </div>
          <div className="color-item">
            <div className="icon-wrapper success">
              <Icon name="check" size="medium" />
            </div>
            <span>成功状态</span>
          </div>
          <div className="color-item">
            <div className="icon-wrapper warning">
              <Icon name="menu" size="medium" />
            </div>
            <span>警告状态</span>
          </div>
          <div className="color-item">
            <div className="icon-wrapper error">
              <Icon name="close" size="medium" />
            </div>
            <span>错误状态</span>
          </div>
          <div className="color-item">
            <div className="icon-wrapper disabled">
              <Icon name="workout" size="medium" />
            </div>
            <span>禁用状态</span>
          </div>
        </div>
        
        <div className="code-block">
          <pre><code>{`<!-- 使用CSS变量设置图标颜色 -->
.icon-wrapper.primary .icon {
  color: var(--primary-color);
}

.icon-wrapper.disabled .icon {
  color: var(--disabled-color);
  opacity: 0.5;
}`}</code></pre>
        </div>
      </section>

      <section className="guide-section">
        <h2>4. 图标与文本结合</h2>
        <p>当图标与文字结合使用时，应注意以下几点：</p>
        
        <div className="text-icon-examples">
          <div className="example">
            <h4>✅ 正确的做法</h4>
            <div className="example-item">
              <div className="good-example">
                <button className="btn with-icon">
                  <Icon name="add" size="small" />
                  <span>添加训练</span>
                </button>
                <p className="note">图标与文本间保持合适的间距（通常为4-8px）</p>
              </div>
              
              <div className="good-example">
                <div className="aligned-item">
                  <Icon name="calendar" size="medium" />
                  <span>2023年10月15日</span>
                </div>
                <p className="note">图标与文本垂直居中对齐</p>
              </div>
            </div>
          </div>
          
          <div className="example">
            <h4>❌ 错误的做法</h4>
            <div className="example-item">
              <div className="bad-example">
                <button className="btn with-icon bad-spacing">
                  <Icon name="add" size="small" />
                  <span>添加训练</span>
                </button>
                <p className="note">图标与文本间距过大或过小</p>
              </div>
              
              <div className="bad-example">
                <div className="misaligned-item">
                  <Icon name="calendar" size="medium" />
                  <span>2023年10月15日</span>
                </div>
                <p className="note">图标与文本未对齐</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="guide-section">
        <h2>5. 图标在项目中的引用方式</h2>
        
        <div className="usage-examples">
          <div className="usage-example">
            <h3>推荐：使用Icon组件</h3>
            <div className="code-block">
              <pre><code>{`import Icon from '../../components/common/Icon';

// 在JSX中使用
<Icon name="workout" size="medium" />

// 添加自定义类名
<Icon name="settings" size="small" className="settings-icon" />

// 动态设置图标
<Icon name={isActive ? 'check' : 'close'} size="medium" />`}</code></pre>
            </div>
            <p className="note">使用Icon组件可以确保一致性，便于后期维护和统一更改。</p>
          </div>
          
          <div className="usage-example">
            <h3>替代方案：直接使用CSS类</h3>
            <div className="code-block">
              <pre><code>{`<!-- 在HTML/JSX中直接使用CSS类 -->
<i className="icon hn hn-dumbbell icon-medium"></i>

<!-- 或者动态设置 -->
<i className={\`icon hn hn-\${iconName} icon-\${size}\`}></i>`}</code></pre>
            </div>
            <p className="note">仅在特殊情况下使用此方法，如需要自定义DOM结构或与第三方库集成。</p>
          </div>
        </div>
      </section>

      <section className="guide-section">
        <h2>6. 无障碍性考虑</h2>
        <p>为确保应用对所有用户友好，在使用图标时请考虑：</p>
        
        <ul className="accessibility-list">
          <li>
            <span className="item-title">添加aria-label属性</span>
            <div className="code-block">
              <pre><code>{`<button aria-label="添加新训练">
  <Icon name="add" size="medium" />
</button>`}</code></pre>
            </div>
          </li>
          <li>
            <span className="item-title">不要仅依靠图标传达信息</span>
            <div className="example-comparison">
              <div className="good-example">
                <button className="btn with-icon">
                  <Icon name="add" size="small" />
                  <span>添加训练</span>
                </button>
                <p className="note">✅ 图标+文字，明确表达意图</p>
              </div>
              <div className="bad-example">
                <button className="btn icon-only">
                  <Icon name="add" size="small" />
                </button>
                <p className="note">❌ 仅使用图标，用途不明确</p>
              </div>
            </div>
          </li>
          <li>
            <span className="item-title">确保足够的颜色对比度</span>
            <p>图标颜色与背景的对比度应至少为4.5:1，以确保视力障碍用户可以识别。</p>
          </li>
        </ul>
      </section>
    </div>
  );
};

export default IconUsageGuide; 