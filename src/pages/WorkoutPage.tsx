import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>, Modal } from '../components/common';
import './WorkoutPage.scss';

interface Exercise {
  id: string;
  name: string;
  sets: WorkoutSet[];
}

interface WorkoutSet {
  reps: number;
  weight: number;
  completed: boolean;
}

// interface WorkoutStats {
//   totalTime: number;
//   exercisesCount: number;
//   setsCompleted: number;
//   totalVolume: number;
// }

const WorkoutPage: React.FC = () => {
  const [isWorkoutActive, setIsWorkoutActive] = useState(false);
  const [workoutTimer, setWorkoutTimer] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [exercises, setExercises] = useState<Exercise[]>([]);
  // const [workoutStats, setWorkoutStats] = useState<WorkoutStats>({
  //   totalTime: 0,
  //   exercisesCount: 0,
  //   setsCompleted: 0,
  //   totalVolume: 0
  // });
  const [showFinishModal, setShowFinishModal] = useState(false);

  // 计时器逻辑
  useEffect(() => {
    let interval: number;
    if (isTimerRunning && isWorkoutActive) {
      interval = window.setInterval(() => {
        setWorkoutTimer(prev => prev + 1);
      }, 1000);
    }
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isTimerRunning, isWorkoutActive]);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const startWorkout = () => {
    setIsWorkoutActive(true);
    setIsTimerRunning(true);
    setWorkoutTimer(0);
  };

  const pauseResumeTimer = () => {
    setIsTimerRunning(!isTimerRunning);
  };

  const finishWorkout = () => {
    setShowFinishModal(true);
    setIsTimerRunning(false);
  };

  const confirmFinishWorkout = () => {
    setIsWorkoutActive(false);
    setWorkoutTimer(0);
    setExercises([]);
    setShowFinishModal(false);
    // 这里可以添加保存训练记录的逻辑
  };

  const addExercise = () => {
    // 模拟添加动作
    const newExercise: Exercise = {
      id: `exercise-${Date.now()}`,
      name: '新动作',
      sets: []
    };
    setExercises([...exercises, newExercise]);
  };

  // QuickStartCard component removed as it's not used

  return (
    <div className="workout-page">
      {!isWorkoutActive ? (
        // 训练开始前的界面
        <>
          {/* 快速开始区域 */}
          <section className="quick-start-section">
            <h2>快速开始</h2>
            <div className="quick-start-grid">
              <Card variant="default" className="quick-start-card">
                <Card.Content>
                  <h3>推拉腿训练</h3>
                  <p>6 个动作</p>
                  <Button 
                    variant="primary" 
                    size="sm" 
                    onClick={startWorkout}
                    fullWidth
                  >
                    开始训练
                  </Button>
                </Card.Content>
              </Card>
              
              <Card variant="default" className="quick-start-card">
                <Card.Content>
                  <h3>上下身分化</h3>
                  <p>8 个动作</p>
                  <Button 
                    variant="primary" 
                    size="sm" 
                    onClick={startWorkout}
                    fullWidth
                  >
                    开始训练
                  </Button>
                </Card.Content>
              </Card>
              
              <Card variant="default" className="quick-start-card">
                <Card.Content>
                  <h3>全身训练</h3>
                  <p>10 个动作</p>
                  <Button 
                    variant="primary" 
                    size="sm" 
                    onClick={startWorkout}
                    fullWidth
                  >
                    开始训练
                  </Button>
                </Card.Content>
              </Card>
              
              <Card variant="outlined" className="empty-workout-card">
                <Card.Content>
                  <div className="empty-workout-content">
                    <div className="empty-icon">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M12 6v6l4 2"/>
                      </svg>
                    </div>
                    <h3>开始空白训练</h3>
                    <p>自由添加动作</p>
                    <Button 
                      variant="primary" 
                      onClick={startWorkout}
                      leftIcon={
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <path d="M12 5v14M5 12h14"/>
                        </svg>
                      }
                      fullWidth
                    >
                      开始训练
                    </Button>
                  </div>
                </Card.Content>
              </Card>
            </div>
          </section>

          {/* 统计概览 */}
          <section className="stats-overview">
            <h2>本周概览</h2>
            <div className="stats-grid">
              <Card variant="default">
                <Card.Content>
                  <div className="stat-item">
                    <div className="stat-icon">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M22 12h-4l-3 9L9 3l-3 9H2"/>
                      </svg>
                    </div>
                    <div className="stat-content">
                      <span className="stat-value">4</span>
                      <span className="stat-label">训练次数</span>
                    </div>
                  </div>
                </Card.Content>
              </Card>
              
              <Card variant="default">
                <Card.Content>
                  <div className="stat-item">
                    <div className="stat-icon">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M12 6v6l4 2"/>
                      </svg>
                    </div>
                    <div className="stat-content">
                      <span className="stat-value">5h 23m</span>
                      <span className="stat-label">训练时长</span>
                    </div>
                  </div>
                </Card.Content>
              </Card>
              
              <Card variant="default">
                <Card.Content>
                  <div className="stat-item">
                    <div className="stat-icon">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M6.5 6.5h11M6.5 17.5h11"/>
                        <circle cx="12" cy="12" r="2"/>
                      </svg>
                    </div>
                    <div className="stat-content">
                      <span className="stat-value">12.5t</span>
                      <span className="stat-label">总重量</span>
                    </div>
                  </div>
                </Card.Content>
              </Card>
            </div>
          </section>

          {/* 最近训练记录 */}
          <section className="recent-workouts">
            <h2>最近训练</h2>
            <div className="recent-workout-list">
              <Card variant="default" className="recent-workout-item">
                <Card.Content>
                  <div className="workout-summary">
                    <div className="workout-date">
                      <span className="day">昨天</span>
                      <span className="time">16:30</span>
                    </div>
                    <div className="workout-details">
                      <h4>推拉腿 - 推</h4>
                      <p>6个动作 • 1小时25分钟</p>
                    </div>
                    <div className="workout-volume">
                      <span className="volume-value">3.2t</span>
                      <span className="volume-label">总量</span>
                    </div>
                  </div>
                </Card.Content>
              </Card>
            </div>
          </section>
        </>
      ) : (
        // 训练进行中的界面
        <section className="active-workout-section">
          <Card variant="elevated" className="active-workout-card">
            <Card.Header>
              <div className="workout-header">
                <div className="workout-info">
                  <h2>训练进行中</h2>
                  <div className="workout-timer">
                    <span className="timer-display">{formatTime(workoutTimer)}</span>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={pauseResumeTimer}
                      className="timer-btn"
                      leftIcon={
                        isTimerRunning ? (
                          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <rect x="6" y="4" width="4" height="16"/>
                            <rect x="14" y="4" width="4" height="16"/>
                          </svg>
                        ) : (
                          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <polygon points="5,3 19,12 5,21"/>
                          </svg>
                        )
                      }
                    />
                  </div>
                </div>
                <Button 
                  variant="success" 
                  onClick={finishWorkout}
                  className="finish-btn"
                >
                  完成训练
                </Button>
              </div>
            </Card.Header>
            
            <Card.Content>
              {exercises.length === 0 ? (
                <div className="empty-workout">
                  <div className="empty-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M6.5 6.5h11M6.5 17.5h11"/>
                      <circle cx="12" cy="12" r="2"/>
                    </svg>
                  </div>
                  <h3>添加你的第一个动作</h3>
                  <p>选择一个动作开始训练</p>
                  <Button 
                    variant="primary"
                    onClick={addExercise}
                    leftIcon={
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M12 5v14M5 12h14"/>
                      </svg>
                    }
                  >
                    添加动作
                  </Button>
                </div>
              ) : (
                <div className="exercises-list">
                  {exercises.map((exercise) => (
                    <div key={exercise.id} className="exercise-item">
                      <h4>{exercise.name}</h4>
                      {/* 这里可以添加组数设置逻辑 */}
                    </div>
                  ))}
                  <Button 
                    variant="secondary"
                    onClick={addExercise}
                    className="add-exercise-btn"
                    leftIcon={
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M12 5v14M5 12h14"/>
                      </svg>
                    }
                  >
                    添加动作
                  </Button>
                </div>
              )}
            </Card.Content>
          </Card>
        </section>
      )}

      {/* 完成训练确认模态框 */}
      <Modal
        open={showFinishModal}
        onClose={() => setShowFinishModal(false)}
        title="完成训练"
        size="md"
      >
        <div className="finish-workout-content">
          <p>确定要完成这次训练吗？</p>
          <div className="workout-summary-stats">
            <div className="summary-item">
              <span className="label">训练时长</span>
              <span className="value">{formatTime(workoutTimer)}</span>
            </div>
            <div className="summary-item">
              <span className="label">完成动作</span>
              <span className="value">{exercises.length} 个</span>
            </div>
          </div>
        </div>
        <div className="modal-actions">
          <Button 
            variant="secondary" 
            onClick={() => setShowFinishModal(false)}
          >
            继续训练
          </Button>
          <Button 
            variant="success" 
            onClick={confirmFinishWorkout}
          >
            完成训练
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default WorkoutPage; 