# FeedPage 动态页面功能说明

## 📋 功能概述

FeedPage 是一个现代化的健身社交动态页面，支持训练记录分享、肌肉示意图可视化和轮播图展示。

## ✨ 主要功能

### 1. 动态数据接入和UI布局
- 集成社区API服务，支持获取和展示训练动态
- 左右布局：左侧显示训练动作列表，右侧显示轮播图
- 响应式设计，移动端自动切换为上下布局

### 2. 肌肉示意图颜色计算
- 根据训练动作的重量和次数自动计算肌肉群训练强度
- 主要肌肉群权重100%，次要肌肉群权重50%
- 使用 `--accent-300` 到 `--accent-500` 颜色渐变表示强度
- 支持实时颜色更新和主题切换

### 3. 轮播图功能
- 支持肌肉示意图和用户上传图像的混合展示
- 流畅的左右滑动切换体验
- 支持触摸手势和键盘导航
- 自动播放和指示器显示

## 🏗️ 组件架构

### 核心组件

#### WorkoutCarousel
```tsx
import { WorkoutCarousel } from '../../components/fitness/WorkoutCarousel/WorkoutCarousel';

<WorkoutCarousel
  items={carouselItems}
  showIndicators={true}
  autoPlay={false}
  onItemChange={(index) => console.log('切换到:', index)}
/>
```

#### ExerciseList
```tsx
import { ExerciseList } from '../../components/fitness/ExerciseList/ExerciseList';

<ExerciseList
  exercises={workoutExercises}
  maxVisible={3}
  showImages={true}
  onViewMore={() => console.log('查看更多')}
/>
```

### 工具函数

#### 肌肉颜色计算
```tsx
import { 
  calculateMuscleIntensities,
  generateStaticMuscleColorConfig 
} from '../../utils/muscleColorCalculator';

// 计算肌肉强度
const intensities = calculateMuscleIntensities(exercises);

// 生成颜色配置
const colorConfig = generateStaticMuscleColorConfig(intensities);
```

## 📊 数据结构

### WorkoutExercise
```typescript
interface WorkoutExercise {
  id: string;
  name: string;
  image_url?: string;
  sets: number;
  reps: number;
  weight: number;
  primary_muscles: MuscleGroupEnum[];
  secondary_muscles?: MuscleGroupEnum[];
}
```

### CarouselItem
```typescript
interface CarouselItem {
  id: string;
  type: 'muscle_illustration' | 'user_image';
  content: {
    muscle_data?: {
      selectedMuscles: MuscleGroupEnum[];
      muscleColorConfig: { [key: string]: 'primary' | 'secondary' };
      intensities: MuscleGroupIntensity[];
    };
    image_data?: {
      url: string;
      alt: string;
      caption?: string;
    };
  };
}
```

## 🎨 样式系统

### CSS变量
- `--accent-300`: 轻度训练强度颜色
- `--accent-400`: 中等训练强度颜色  
- `--accent-500`: 高强度训练颜色

### 响应式断点
- 移动端: `max-width: 768px`
- 平板端: `768px - 1024px`
- 桌面端: `min-width: 1024px`

## 🔧 API集成

### 社区服务
```typescript
import { communityService } from '../../services/communityService';

// 获取动态列表
const posts = await communityService.getPosts({ skip: 0, limit: 20 });

// 点赞动态
await communityService.togglePostLike(postId);

// 分享训练
await communityService.shareWorkout(workoutId, shareData);
```

## 📱 iOS优化

- 支持Safe Area适配
- 44px最小触摸目标
- 硬件加速动画
- 触摸反馈优化
- 暗色主题支持

## 🧪 测试

运行测试：
```bash
npm test -- muscleColorCalculator.test.ts
```

## 🚀 性能优化

- 使用 `useMemo` 缓存计算结果
- 图片懒加载
- 组件级代码分割
- 减少不必要的重渲染

## 📝 使用示例

```tsx
// 基本使用
const FeedPage = () => {
  const [posts, setPosts] = useState<FeedPost[]>([]);
  
  useEffect(() => {
    loadPosts();
  }, []);
  
  const loadPosts = async () => {
    const response = await communityService.getPosts();
    setPosts(response.posts);
  };
  
  return (
    <div className="feed-page">
      {posts.map(post => (
        <FeedPostCard key={post.id} post={post} />
      ))}
    </div>
  );
};
```

## 🔍 故障排除

### 常见问题

1. **肌肉示意图不显示**
   - 检查 `StaticMuscleIllustration` 组件导入
   - 确认肌肉群数据格式正确

2. **轮播图滑动不流畅**
   - 检查CSS硬件加速设置
   - 确认触摸事件处理正确

3. **响应式布局异常**
   - 检查CSS媒体查询
   - 确认Flexbox属性设置

### 调试技巧

- 使用浏览器开发者工具检查CSS变量
- 在控制台查看肌肉强度计算结果
- 使用React DevTools检查组件状态
