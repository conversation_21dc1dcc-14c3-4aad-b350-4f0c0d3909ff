import React, { useState, useEffect } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import './SettingsPage.scss';

// 简化的用户资料接口
interface UserProfile {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  birthDate?: string;
  height?: number; // cm
  weight?: number; // kg
  fitnessGoal: 'lose_weight' | 'gain_muscle' | 'maintain' | 'strength' | 'endurance';
  activityLevel: 'sedentary' | 'lightly_active' | 'moderately_active' | 'very_active' | 'extremely_active';
}

// 用户偏好接口
interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh' | 'en';
  units: 'metric' | 'imperial';
  notifications: {
    pushEnabled: boolean;
    workoutReminders: boolean;
    progressUpdates: boolean;
    socialUpdates: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'friends' | 'private';
    shareWorkouts: boolean;
    shareProgress: boolean;
  };
  dataSync: {
    autoBackup: boolean;
    backupFrequency: 'daily' | 'weekly' | 'monthly';
  };
}

// 默认配置
const defaultProfile: UserProfile = {
  id: 'user_123',
  name: 'FitMaster用户',
  email: '<EMAIL>',
  avatar: '/api/placeholder/80/80',
  birthDate: '1990-01-01',
  height: 170,
  weight: 70,
  fitnessGoal: 'maintain',
  activityLevel: 'moderately_active'
};

const defaultPreferences: UserPreferences = {
  theme: 'dark',
  language: 'zh',
  units: 'metric',
  notifications: {
    pushEnabled: true,
    workoutReminders: true,
    progressUpdates: true,
    socialUpdates: false
  },
  privacy: {
    profileVisibility: 'friends',
    shareWorkouts: true,
    shareProgress: true
  },
  dataSync: {
    autoBackup: true,
    backupFrequency: 'weekly'
  }
};

const SettingsPage: React.FC = () => {
  const { theme, setTheme } = useTheme();
  const [userProfile, setUserProfile] = useState<UserProfile>(defaultProfile);
  const [preferences, setPreferences] = useState<UserPreferences>(defaultPreferences);
  const [isLoading, setIsLoading] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // 加载用户数据
  useEffect(() => {
    const savedProfile = localStorage.getItem('userProfile');
    const savedPreferences = localStorage.getItem('userPreferences');

    if (savedProfile) {
      try {
        setUserProfile(JSON.parse(savedProfile));
      } catch (error) {
        console.warn('Failed to parse saved profile:', error);
      }
    }

    if (savedPreferences) {
      try {
        setPreferences(JSON.parse(savedPreferences));
      } catch (error) {
        console.warn('Failed to parse saved preferences:', error);
      }
    }
  }, []);

  // 更新用户资料
  const updateProfile = (key: keyof UserProfile, value: any) => {
    setUserProfile(prev => ({ ...prev, [key]: value }));
    setHasUnsavedChanges(true);
  };

  // 更新用户偏好
  const updatePreference = (path: string, value: any) => {
    setPreferences(prev => {
      const newPreferences = { ...prev };
      const keys = path.split('.');
      let current: any = newPreferences;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newPreferences;
    });
    setHasUnsavedChanges(true);
  };

  // 保存设置
  const saveSettings = async () => {
    setIsLoading(true);
    try {
      localStorage.setItem('userProfile', JSON.stringify(userProfile));
      localStorage.setItem('userPreferences', JSON.stringify(preferences));
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setHasUnsavedChanges(false);
      alert('设置保存成功！');
    } catch (error) {
      console.error('保存设置失败:', error);
      alert('保存失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 重置设置
  const resetSettings = () => {
    setUserProfile(defaultProfile);
    setPreferences(defaultPreferences);
    setHasUnsavedChanges(true);
  };

  // 删除账户
  const deleteAccount = () => {
    console.log('删除账户');
    setShowDeleteModal(false);
  };

  // 导出数据
  const exportData = () => {
    const data = {
      profile: userProfile,
      preferences: preferences,
      exportDate: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'fitmaster-user-data.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className={`settings-page theme-${theme}`}>
      <div className="settings-container">
        <div className="settings-header">
          <h1>设置</h1>
          <div className="settings-actions">
            <button 
              className="pixel-btn btn-secondary"
              onClick={resetSettings}
              disabled={isLoading}
            >
              重置
            </button>
            <button 
              className={`pixel-btn btn-primary ${hasUnsavedChanges ? 'highlight' : ''}`}
              onClick={saveSettings}
              disabled={isLoading}
            >
              {isLoading ? '保存中...' : '保存设置'}
            </button>
          </div>
        </div>

        <div className="settings-content">
          {/* 用户资料部分 */}
          <section className="settings-section">
            <h2>用户资料</h2>
            <div className="settings-card">
              <div className="form-group">
                <label>姓名</label>
                <input
                  type="text"
                  value={userProfile.name}
                  onChange={(e) => updateProfile('name', e.target.value)}
                  className="form-input"
                />
              </div>
              
              <div className="form-group">
                <label>邮箱</label>
                <input
                  type="email"
                  value={userProfile.email}
                  onChange={(e) => updateProfile('email', e.target.value)}
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label>出生日期</label>
                <input
                  type="date"
                  value={userProfile.birthDate || ''}
                  onChange={(e) => updateProfile('birthDate', e.target.value)}
                  className="form-input"
                />
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label>身高 (cm)</label>
                  <input
                    type="number"
                    value={userProfile.height || ''}
                    onChange={(e) => updateProfile('height', Number(e.target.value))}
                    className="form-input"
                    min="120"
                    max="250"
                  />
                </div>
                
                <div className="form-group">
                  <label>体重 (kg)</label>
                  <input
                    type="number"
                    value={userProfile.weight || ''}
                    onChange={(e) => updateProfile('weight', Number(e.target.value))}
                    className="form-input"
                    min="30"
                    max="300"
                    step="0.1"
                  />
                </div>
              </div>

              <div className="form-group">
                <label>健身目标</label>
                <select
                  value={userProfile.fitnessGoal}
                  onChange={(e) => updateProfile('fitnessGoal', e.target.value)}
                  className="form-select"
                >
                  <option value="lose_weight">减重</option>
                  <option value="gain_muscle">增肌</option>
                  <option value="maintain">保持</option>
                  <option value="strength">力量提升</option>
                  <option value="endurance">耐力提升</option>
                </select>
              </div>

              <div className="form-group">
                <label>活动水平</label>
                <select
                  value={userProfile.activityLevel}
                  onChange={(e) => updateProfile('activityLevel', e.target.value)}
                  className="form-select"
                >
                  <option value="sedentary">久坐</option>
                  <option value="lightly_active">轻度活跃</option>
                  <option value="moderately_active">中度活跃</option>
                  <option value="very_active">高度活跃</option>
                  <option value="extremely_active">极度活跃</option>
                </select>
              </div>
            </div>
          </section>

          {/* 应用偏好部分 */}
          <section className="settings-section">
            <h2>应用偏好</h2>
            <div className="settings-card">
              <div className="form-group">
                <label>主题模式</label>
                <select
                  value={preferences.theme}
                  onChange={(e) => {
                    updatePreference('theme', e.target.value);
                    setTheme(e.target.value as 'light' | 'dark');
                  }}
                  className="form-select"
                >
                  <option value="light">浅色</option>
                  <option value="dark">深色</option>
                  <option value="auto">跟随系统</option>
                </select>
              </div>

              <div className="form-group">
                <label>语言</label>
                <select
                  value={preferences.language}
                  onChange={(e) => updatePreference('language', e.target.value)}
                  className="form-select"
                >
                  <option value="zh">中文</option>
                  <option value="en">English</option>
                </select>
              </div>

              <div className="form-group">
                <label>单位制</label>
                <select
                  value={preferences.units}
                  onChange={(e) => updatePreference('units', e.target.value)}
                  className="form-select"
                >
                  <option value="metric">公制 (kg, cm)</option>
                  <option value="imperial">英制 (lb, ft)</option>
                </select>
              </div>
            </div>
          </section>

          {/* 通知设置部分 */}
          <section className="settings-section">
            <h2>通知设置</h2>
            <div className="settings-card">
              <div className="form-group checkbox-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={preferences.notifications.pushEnabled}
                    onChange={(e) => updatePreference('notifications.pushEnabled', e.target.checked)}
                    className="form-checkbox"
                  />
                  启用推送通知
                </label>
              </div>

              <div className="form-group checkbox-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={preferences.notifications.workoutReminders}
                    onChange={(e) => updatePreference('notifications.workoutReminders', e.target.checked)}
                    className="form-checkbox"
                  />
                  训练提醒
                </label>
              </div>

              <div className="form-group checkbox-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={preferences.notifications.progressUpdates}
                    onChange={(e) => updatePreference('notifications.progressUpdates', e.target.checked)}
                    className="form-checkbox"
                  />
                  进度更新
                </label>
              </div>

              <div className="form-group checkbox-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={preferences.notifications.socialUpdates}
                    onChange={(e) => updatePreference('notifications.socialUpdates', e.target.checked)}
                    className="form-checkbox"
                  />
                  社交更新
                </label>
              </div>
            </div>
          </section>

          {/* 数据管理部分 */}
          <section className="settings-section">
            <h2>数据管理</h2>
            <div className="settings-card">
              <div className="form-group checkbox-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={preferences.dataSync.autoBackup}
                    onChange={(e) => updatePreference('dataSync.autoBackup', e.target.checked)}
                    className="form-checkbox"
                  />
                  自动备份
                </label>
              </div>

              <div className="form-group">
                <label>备份频率</label>
                <select
                  value={preferences.dataSync.backupFrequency}
                  onChange={(e) => updatePreference('dataSync.backupFrequency', e.target.value)}
                  className="form-select"
                  disabled={!preferences.dataSync.autoBackup}
                >
                  <option value="daily">每日</option>
                  <option value="weekly">每周</option>
                  <option value="monthly">每月</option>
                </select>
              </div>

              <div className="data-actions">
                <button 
                  className="pixel-btn btn-secondary"
                  onClick={exportData}
                >
                  导出数据
                </button>
                <button 
                  className="pixel-btn btn-danger"
                  onClick={() => setShowDeleteModal(true)}
                >
                  删除账户
                </button>
              </div>
            </div>
          </section>
        </div>
      </div>

      {/* 删除账户确认弹窗 */}
      {showDeleteModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>删除账户确认</h3>
            </div>
            <div className="modal-body">
              <p>此操作将永久删除您的账户和所有数据，无法恢复。</p>
              <p>您确定要继续吗？</p>
            </div>
            <div className="modal-footer">
              <button 
                className="pixel-btn btn-secondary"
                onClick={() => setShowDeleteModal(false)}
              >
                取消
              </button>
              <button 
                className="pixel-btn btn-danger"
                onClick={deleteAccount}
              >
                确认删除
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SettingsPage;