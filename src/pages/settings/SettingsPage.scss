/* Settings页面样式，基于设计系统 */

.settings-page {
  /* 移除全屏样式，适配Layout容器 */
  padding: 0;
}

.settings-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: var(--space-8, 2rem);
  
  @media (max-width: 1024px) {
    grid-template-columns: 240px 1fr;
    gap: var(--space-6, 1.5rem);
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--space-4, 1rem);
  }
}

/* 导航侧边栏 */
.settings-navigation {
  background: var(--bg-surface, #1e293b);
  border-radius: var(--radius-lg, 0.5rem);
  border: 1px solid var(--border-primary, #334155);
  height: fit-content;
  position: sticky;
  top: var(--space-6, 1.5rem);
  
  @media (max-width: 768px) {
    position: static;
    border-radius: var(--radius-md, 0.375rem);
  }
}

.navigation-header {
  padding: var(--space-6, 1.5rem);
  border-bottom: 1px solid var(--border-primary, #334155);
  
  h1 {
    font-size: var(--text-2xl, 1.5rem);
    font-weight: var(--font-bold, 700);
    color: var(--text-primary, #f8fafc);
    margin: 0 0 var(--space-2, 0.5rem) 0;
    background: linear-gradient(135deg, var(--accent-500, #3b82f6), var(--success-500, #22c55e));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  p {
    font-size: var(--text-sm, 0.875rem);
    color: var(--text-secondary, #cbd5e1);
    margin: 0;
  }
}

.navigation-menu {
  padding: var(--space-4, 1rem);
  
  @media (max-width: 768px) {
    display: flex;
    overflow-x: auto;
    gap: var(--space-2, 0.5rem);
    padding: var(--space-4, 1rem) var(--space-6, 1.5rem);
    
    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3, 0.75rem);
  padding: var(--space-3, 0.75rem) var(--space-4, 1rem);
  border-radius: var(--radius-md, 0.375rem);
  border: none;
  background: transparent;
  color: var(--text-secondary, #cbd5e1);
  cursor: pointer;
  transition: all var(--transition-normal, 0.2s) var(--ease-in-out, cubic-bezier(0.4, 0, 0.2, 1));
  width: 100%;
  text-align: left;
  margin-bottom: var(--space-1, 0.25rem);
  
  &:hover {
    background: var(--primary-600, #1e293b);
    color: var(--text-primary, #f8fafc);
    transform: translateX(4px);
  }
  
  &.active {
    background: linear-gradient(135deg, var(--accent-500, #3b82f6), var(--success-500, #22c55e));
    color: var(--text-on-accent, #ffffff);
    transform: translateX(4px);
    box-shadow: var(--shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, 0.05));
    
    .nav-icon {
      transform: scale(1.1);
    }
  }
  
  @media (max-width: 768px) {
    flex-direction: column;
    min-width: 80px;
    text-align: center;
    gap: var(--space-1, 0.25rem);
    
    &:hover,
    &.active {
      transform: translateY(-2px);
    }
  }
}

.nav-icon {
  font-size: var(--text-lg, 1.125rem);
  transition: transform var(--transition-normal, 0.2s) var(--ease-in-out, cubic-bezier(0.4, 0, 0.2, 1));
}

.nav-label {
  font-size: var(--text-sm, 0.875rem);
  font-weight: var(--font-medium, 500);
  
  @media (max-width: 768px) {
    font-size: var(--text-xs, 0.75rem);
  }
}

/* 主内容区域 */
.settings-main {
  display: flex;
  flex-direction: column;
  gap: var(--space-6, 1.5rem);
}

.settings-content {
  background: var(--bg-surface, #1e293b);
  border-radius: var(--radius-lg, 0.5rem);
  border: 1px solid var(--border-primary, #334155);
  overflow: hidden;
}

.section {
  padding: var(--space-8, 2rem);
  
  @media (max-width: 768px) {
    padding: var(--space-6, 1.5rem);
  }
}

.section-header {
  margin-bottom: var(--space-8, 2rem);
  
  h2 {
    font-size: var(--text-2xl, 1.5rem);
    font-weight: var(--font-bold, 700);
    color: var(--text-primary, #f8fafc);
    margin: 0 0 var(--space-2, 0.5rem) 0;
  }
  
  p {
    font-size: var(--text-base, 1rem);
    color: var(--text-secondary, #cbd5e1);
    margin: 0;
  }
}

.setting-group {
  margin-bottom: var(--space-8, 2rem);
  
  &:last-child {
    margin-bottom: 0;
  }
  
  h3 {
    font-size: var(--text-lg, 1.125rem);
    font-weight: var(--font-semibold, 600);
    color: var(--text-primary, #f8fafc);
    margin: 0 0 var(--space-6, 1.5rem) 0;
    padding-bottom: var(--space-2, 0.5rem);
    border-bottom: 2px solid var(--border-primary, #334155);
  }
}

.setting-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4) 0;
  border-bottom: 1px solid var(--border-light);
  gap: var(--space-4);
  
  &:last-child {
    border-bottom: none;
  }
  
  &.half {
    flex: 1;
  }
  
  &.danger {
    .setting-info label {
      color: var(--error-color);
    }
  }
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
}

.setting-info {
  flex: 1;
  
  label {
    display: block;
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
  }
  
  .setting-desc {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
  }
}

/* 输入控件样式 */
.setting-input,
.setting-select {
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background: var(--surface-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all 0.2s ease;
  min-width: 180px;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
  }
  
  &:disabled {
    background: var(--surface-disabled);
    color: var(--text-disabled);
    cursor: not-allowed;
  }
  
  @media (max-width: 768px) {
    width: 100%;
    min-width: auto;
  }
}

.setting-button {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background: var(--surface-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  
  &:hover {
    background: var(--surface-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
  }
  
  &.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    
    &:hover {
      background: var(--primary-color-hover);
      border-color: var(--primary-color-hover);
    }
  }
  
  &.secondary {
    background: var(--surface-secondary);
    color: var(--text-primary);
  }
  
  &.danger {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
    
    &:hover {
      background: var(--error-color-hover);
      border-color: var(--error-color-hover);
    }
  }
  
  .arrow {
    font-size: var(--font-size-sm);
    transition: transform 0.2s ease;
  }
  
  &:hover .arrow {
    transform: translateX(4px);
  }
}

/* 切换开关样式 */
.setting-toggle {
  position: relative;
  width: 52px;
  height: 28px;
  cursor: pointer;
  
  input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--border-color);
    transition: all 0.3s ease;
    border-radius: 24px;
    
    &::before {
      position: absolute;
      content: '';
      height: 20px;
      width: 20px;
      left: 4px;
      bottom: 4px;
      background: white;
      transition: all 0.3s ease;
      border-radius: 50%;
      box-shadow: var(--shadow-sm);
    }
  }
  
  input:checked + .toggle-slider {
    background: var(--primary-color);
    
    &::before {
      transform: translateX(24px);
    }
  }
  
  input:disabled + .toggle-slider {
    background: var(--surface-disabled);
    cursor: not-allowed;
    
    &::before {
      background: var(--text-disabled);
    }
  }
}

/* 主题选择器 */
.theme-selector {
  display: flex;
  gap: var(--space-2);
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background: var(--surface-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  
  &:hover {
    background: var(--surface-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
  }
  
  &.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
  
  .theme-icon {
    font-size: var(--font-size-xl);
  }
  
  .theme-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
  }
}

/* 应用信息 */
.app-info {
  display: flex;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
  padding: var(--space-6);
  background: var(--surface-secondary);
  border-radius: var(--border-radius-md);
  
  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
  }
}

.app-logo {
  .logo-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    box-shadow: var(--shadow-md);
  }
}

.app-details {
  flex: 1;
  
  h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
  }
  
  .version {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: 0 0 var(--space-3) 0;
    font-weight: 500;
  }
  
  .description {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
  }
}

/* 存储信息 */
.storage-info {
  background: var(--surface-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--space-4);
}

.storage-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--border-light);
  
  &:last-child {
    border-bottom: none;
  }
  
  &.total {
    font-weight: 600;
    color: var(--text-primary);
    border-top: 2px solid var(--border-color);
    padding-top: var(--space-3);
    margin-top: var(--space-2);
  }
}

.storage-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.storage-value {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: 500;
}

/* 团队信息 */
.team-info {
  text-align: center;
  padding: var(--space-4);
  background: var(--surface-secondary);
  border-radius: var(--border-radius-md);
  
  p {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin: var(--space-1) 0;
  }
}

/* 操作栏 */
.settings-actions {
  position: sticky;
  bottom: 0;
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--space-4);
  box-shadow: var(--shadow-lg);
  animation: slideUp 0.3s ease;
  
  @keyframes slideUp {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

.actions-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--space-3);
  }
}

.unsaved-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--font-size-sm);
  color: var(--warning-color);
  
  .indicator-dot {
    width: 8px;
    height: 8px;
    background: var(--warning-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
}

.action-buttons {
  display: flex;
  gap: var(--space-3);
}

.action-btn {
  padding: var(--space-3) var(--space-6);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    
    &:hover:not(:disabled) {
      background: var(--primary-color-hover);
      border-color: var(--primary-color-hover);
      transform: translateY(-1px);
      box-shadow: var(--shadow-md);
    }
  }
  
  &.secondary {
    background: var(--surface-secondary);
    color: var(--text-primary);
    
    &:hover:not(:disabled) {
      background: var(--surface-color);
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }
  }
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease;
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
}

.confirm-modal {
  background: var(--surface-color);
  border-radius: var(--border-radius-lg);
  padding: var(--space-6);
  max-width: 440px;
  width: 90%;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-xl);
  animation: slideIn 0.3s ease;
  
  @keyframes slideIn {
    from {
      transform: translateY(-20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

.modal-header {
  margin-bottom: var(--space-4);
  
  h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
  }
}

.modal-content {
  margin-bottom: var(--space-6);
  
  p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0 0 var(--space-3) 0;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.modal-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

.modal-button {
  padding: var(--space-3) var(--space-6);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    
    &:hover {
      background: var(--primary-color-hover);
      border-color: var(--primary-color-hover);
    }
  }
  
  &.secondary {
    background: var(--surface-secondary);
    color: var(--text-primary);
    
    &:hover {
      background: var(--surface-color);
    }
  }
  
  &.danger {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
    
    &:hover {
      background: var(--error-color-hover);
      border-color: var(--error-color-hover);
    }
  }
}

/* 响应式设计 */
@media (max-width: 568px) {
  .settings-page {
    padding: var(--space-2);
  }
  
  .settings-container {
    gap: var(--space-3);
  }
  
  .section {
    padding: var(--space-4);
  }
  
  .section-header {
    margin-bottom: var(--space-6);
    
    h2 {
      font-size: var(--font-size-xl);
    }
  }
  
  .setting-group {
    margin-bottom: var(--space-6);
  }
  
  .setting-item {
    padding: var(--space-3) 0;
  }
  
  .app-info {
    padding: var(--space-4);
  }
  
  .app-logo .logo-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}

/* 深色主题优化 */
[data-theme="dark"] {
  .theme-option {
    &.active {
      box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
    }
  }
  
  .setting-toggle {
    .toggle-slider {
      &::before {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
    }
  }
  
  .confirm-modal {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  }
}
