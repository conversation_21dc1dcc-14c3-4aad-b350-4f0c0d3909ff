import React, { useEffect, useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import Icon from '../../components/common/Icon';
import './UiTestPage.scss';

/**
 * UI库测试页面
 * 用于测试和展示集成的各种UI库组件
 */
const UiTestPage: React.FC = () => {
  const { theme } = useTheme();
  const [loadTime, setLoadTime] = useState<number>(0);
  
  // 测量页面加载时间
  useEffect(() => {
    const startTime = performance.now();
    
    // 页面加载完成时计算时间
    const calculateLoadTime = () => {
      const endTime = performance.now();
      setLoadTime(Math.round(endTime - startTime));
    };
    
    // 延迟执行以确保组件完全渲染
    const timer = setTimeout(calculateLoadTime, 100);
    return () => clearTimeout(timer);
  }, []);

  // 健身相关图标
  const fitnessIcons = [
    { name: 'dumbbell', label: '哑铃' },
    { name: 'human', label: '人体' },
    { name: 'human-run', label: '跑步' },
    { name: 'heart', label: '心率' },
    { name: 'trophy', label: '奖杯' },
    { name: 'chart', label: '图表' },
    { name: 'chart-bar', label: '柱状图' },
    { name: 'analytics', label: '分析' },
    { name: 'calendar', label: '日历' },
    { name: 'calendar-check', label: '完成' },
    { name: 'calendar-plus', label: '添加' },
    { name: 'water', label: '水分' },
  ];

  // 导航图标
  const navigationIcons = [
    { name: 'home', label: '首页' },
    { name: 'settings', label: '设置' },
    { name: 'user', label: '用户' },
    { name: 'users', label: '用户组' },
    { name: 'search', label: '搜索' },
    { name: 'plus', label: '添加' },
    { name: 'minus', label: '减少' },
    { name: 'check', label: '完成' },
    { name: 'close', label: '关闭' },
    { name: 'arrow-left', label: '左箭头' },
    { name: 'arrow-right', label: '右箭头' },
    { name: 'menu', label: '菜单' },
  ];

  // 应用自定义图标组件示例
  const appIcons = [
    { name: 'dashboard', label: '仪表盘', size: 'medium' },
    { name: 'workout', label: '训练', size: 'medium' },
    { name: 'profile', label: '档案', size: 'medium' },
    { name: 'feed', label: '动态', size: 'medium' },
    { name: 'routines', label: '计划', size: 'medium' },
    { name: 'exercises', label: '运动', size: 'medium' },
    { name: 'settings', label: '设置', size: 'medium' },
  ];

  return (
          <div className={`ui-test-page theme-${theme}`}>
      <header className="page-header">
        <h1>UI库集成测试页面</h1>
        <p className="description">
          此页面用于测试和展示集成到FitMaster应用中的各种UI库组件。
        </p>
      </header>

      <div className="test-sections">
        {/* HeroUI 测试区域 */}
        <section className="test-section">
          <h2>HeroUI 组件测试</h2>
          <div className="component-grid">
            <div className="component-card">
              <h3>按钮组件</h3>
              <div className="component-preview">
                {/* HeroUI组件将在此处添加 */}
                <p className="placeholder">HeroUI按钮将在此处显示</p>
              </div>
            </div>

            <div className="component-card">
              <h3>卡片组件</h3>
              <div className="component-preview">
                {/* HeroUI组件将在此处添加 */}
                <p className="placeholder">HeroUI卡片将在此处显示</p>
              </div>
            </div>

            <div className="component-card">
              <h3>表单组件</h3>
              <div className="component-preview">
                {/* HeroUI组件将在此处添加 */}
                <p className="placeholder">HeroUI表单组件将在此处显示</p>
              </div>
            </div>
          </div>
        </section>

        {/* Pixel Icon Library 测试区域 */}
        <section className="test-section">
          <h2>Pixel Icon Library 测试</h2>
          <div className="component-grid">
            <div className="component-card">
              <h3>健身相关图标</h3>
              <div className="component-preview icons-preview">
                {fitnessIcons.map((icon) => (
                  <div key={icon.name} className="icon-item">
                    <i className={`hn hn-${icon.name}`}></i>
                    <span className="icon-label">{icon.label}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="component-card">
              <h3>导航图标</h3>
              <div className="component-preview icons-preview">
                {navigationIcons.map((icon) => (
                  <div key={icon.name} className="icon-item">
                    <i className={`hn hn-${icon.name}`}></i>
                    <span className="icon-label">{icon.label}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="component-card">
              <h3>应用自定义图标组件</h3>
              <div className="component-preview app-icons-preview">
                {appIcons.map((icon: any) => (
                  <div key={icon.name} className="app-icon-item">
                    <Icon name={icon.name} size={icon.size} />
                    <span className="icon-label">{icon.label}</span>
                  </div>
                ))}
                
                <div className="icon-sizes">
                  <h4>图标尺寸示例</h4>
                  <div className="size-examples">
                    <div className="size-example">
                      <Icon name="workout" size="small" />
                      <span>Small</span>
                    </div>
                    <div className="size-example">
                      <Icon name="workout" size="medium" />
                      <span>Medium</span>
                    </div>
                    <div className="size-example">
                      <Icon name="workout" size="large" />
                      <span>Large</span>
                    </div>
                    <div className="size-example">
                      <Icon name="workout" size="xlarge" />
                      <span>XLarge</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* RetroUI 测试区域 */}
        <section className="test-section">
          <h2>RetroUI 测试</h2>
          <div className="component-grid">
            <div className="component-card">
              <h3>像素风格按钮</h3>
              <div className="component-preview">
                {/* RetroUI组件将在此处添加 */}
                <p className="placeholder">RetroUI按钮将在此处显示</p>
              </div>
            </div>

            <div className="component-card">
              <h3>像素风格卡片</h3>
              <div className="component-preview">
                {/* RetroUI组件将在此处添加 */}
                <p className="placeholder">RetroUI卡片将在此处显示</p>
              </div>
            </div>
          </div>
        </section>

        {/* 性能测试区域 */}
        <section className="test-section">
          <h2>性能测试</h2>
          <div className="performance-tests">
            <div className="test-card">
              <h3>加载时间</h3>
              <div className="test-content">
                <p>页面加载时间: <strong>{loadTime}ms</strong></p>
              </div>
            </div>
            <div className="test-card">
              <h3>渲染性能</h3>
              <div className="test-content">
                <p>图标渲染: <strong>{fitnessIcons.length + navigationIcons.length + appIcons.length}</strong> 个图标</p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default UiTestPage; 