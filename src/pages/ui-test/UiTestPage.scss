.ui-test-page {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  // 页面标题
  .page-header {
    margin-bottom: 32px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 16px;

    h1 {
      font-size: 28px;
      margin-bottom: 8px;
    }

    .description {
      color: #666;
      font-size: 16px;
    }
  }

  // 测试区块
  .test-section {
    margin-bottom: 48px;
    
    h2 {
      font-size: 24px;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #eaeaea;
    }
  }

  // 组件网格布局
  .component-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
  }

  // 组件卡片
  .component-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    h3 {
      font-size: 18px;
      padding: 12px 16px;
      margin: 0;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e0e0e0;
    }

    .component-preview {
      padding: 20px;
      min-height: 120px;
      display: flex;
      flex-direction: column;
      gap: 12px;

      &.icons-preview {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 16px;
        
        .icon-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          
          i {
            font-size: 24px;
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 8px;
            background: #f8f8f8;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            transition: all 0.2s ease;
            
            &:hover {
              transform: scale(1.1);
              background: #eaeaea;
            }
          }
          
          .icon-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
          }
        }
      }
      
      // 应用自定义图标样式
      &.app-icons-preview {
        .app-icon-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 16px;
          
          .icon {
            background-color: #f5f5f5;
            padding: 8px;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.2s ease;
            
            &:hover {
              transform: scale(1.1);
              background-color: #eaeaea;
            }
          }
          
          .icon-label {
            font-size: 12px;
            color: #666;
          }
        }
        
        // 图标尺寸示例
        .icon-sizes {
          margin-top: 24px;
          border-top: 1px solid #eaeaea;
          padding-top: 16px;
          
          h4 {
            margin-bottom: 16px;
            font-size: 16px;
            color: #333;
          }
          
          .size-examples {
            display: flex;
            justify-content: space-around;
            align-items: flex-end;
            flex-wrap: wrap;
            gap: 16px;
            
            .size-example {
              display: flex;
              flex-direction: column;
              align-items: center;
              
              .icon {
                margin-bottom: 8px;
                background-color: #f5f5f5;
                border-radius: 8px;
                padding: 8px;
              }
              
              span {
                font-size: 12px;
                color: #666;
              }
            }
          }
        }
      }

      .placeholder {
        color: #999;
        font-style: italic;
        text-align: center;
        border: 1px dashed #ccc;
        padding: 20px;
        border-radius: 4px;
      }
    }
  }

  // 性能测试区域
  .performance-tests {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;

    .test-card {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;

      h3 {
        font-size: 18px;
        padding: 12px 16px;
        margin: 0;
        background-color: #f5f5f5;
        border-bottom: 1px solid #e0e0e0;
      }

      .test-content {
        padding: 16px;
        min-height: 100px;
        
        p {
          margin: 0;
          line-height: 1.6;
          
          strong {
            color: var(--primary-color);
          }
        }
      }
    }
  }

  // 暗色主题支持
  &.theme-dark {
    background-color: #1a1a1a;
    color: #e0e0e0;

    .page-header {
      border-bottom-color: #333;

      .description {
        color: #aaa;
      }
    }

    .test-section h2 {
      border-bottom-color: #333;
    }

    .component-card {
      border-color: #333;
      background-color: #252525;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

      h3 {
        background-color: #333;
        border-bottom-color: #444;
      }
      
      .component-preview {
        &.icons-preview {
          .icon-item {
            i {
              background: #333;
              color: #e0e0e0;
              
              &:hover {
                background: #444;
              }
            }
            
            .icon-label {
              color: #aaa;
            }
          }
        }
        
        &.app-icons-preview {
          .app-icon-item {
            .icon {
              background-color: #333;
              color: #e0e0e0;
              
              &:hover {
                background-color: #444;
              }
            }
            
            .icon-label {
              color: #aaa;
            }
          }
          
          .icon-sizes {
            border-top-color: #444;
            
            h4 {
              color: #e0e0e0;
            }
            
            .size-example {
              .icon {
                background-color: #333;
                color: #e0e0e0;
              }
              
              span {
                color: #aaa;
              }
            }
          }
        }
      
        .placeholder {
          color: #777;
          border-color: #444;
        }
      }
    }

    .performance-tests .test-card {
      border-color: #333;

      h3 {
        background-color: #333;
        border-bottom-color: #444;
      }
    }
  }
} 