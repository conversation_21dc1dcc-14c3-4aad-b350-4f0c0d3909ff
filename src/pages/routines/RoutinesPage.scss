// Routines Page Styles
.routines-page {
  padding: 0;
}

// Header Section
.routines-header {
  margin-bottom: var(--space-8);
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-6);
    
    .title-section {
      h1 {
        font-size: var(--text-3xl);
        font-weight: var(--font-bold);
        color: var(--text-primary);
        margin: 0 0 var(--space-2) 0;
        background: var(--gradient-brand);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      p {
        color: var(--text-secondary);
        font-size: var(--text-base);
        margin: 0;
      }
    }
    
    .create-routine-btn {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-3) var(--space-6);
      background: var(--gradient-primary);
      border: none;
      border-radius: var(--radius-lg);
      color: var(--text-on-accent);
      font-size: var(--text-base);
      font-weight: var(--font-semibold);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      box-shadow: var(--shadow-md);
      
      svg {
        width: 1.25rem;
        height: 1.25rem;
        stroke-width: 2;
      }
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
  }
  
  .controls-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--space-4);
    
    .filter-tabs {
      display: flex;
      gap: var(--space-2);
      padding: var(--space-1);
      background: var(--bg-surface);
      border: 1px solid var(--primary-500);
      border-radius: var(--radius-lg);
      
      .filter-btn {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-4);
        border: none;
        border-radius: var(--radius-md);
        background: transparent;
        color: var(--text-secondary);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        cursor: pointer;
        transition: all var(--transition-normal) var(--ease-in-out);
        white-space: nowrap;
        
        svg {
          width: 1rem;
          height: 1rem;
          stroke-width: 2;
        }
        
        &:hover {
          background: var(--bg-hover);  /* 使用浅色悬停背景，保持文字可读性 */
          color: var(--text-primary);
          border-color: var(--accent-500);  /* 添加蓝色边框表示悬停 */
        }
        
        &.active {
          background: var(--accent-500);
          color: var(--text-on-accent);  /* 现在已正确定义为白色 */
        }
      }
    }
    
    .sort-dropdown {
      .sort-select {
        padding: var(--space-2) var(--space-4);
        background: var(--bg-surface);
        border: 1px solid var(--primary-500);
        border-radius: var(--radius-md);
        color: var(--text-secondary);
        font-size: var(--text-sm);
        cursor: pointer;
        transition: all var(--transition-normal) var(--ease-in-out);
        
        &:hover {
          border-color: var(--accent-500);
        }
        
        &:focus {
          outline: 2px solid var(--accent-500);
          outline-offset: 2px;
        }
      }
    }
  }
}

// Routines Grid - 统一响应式布局
.routines-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
  
  @media (max-width: 1024px) {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-5);
  }
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  @media (max-width: 480px) {
    gap: var(--space-3);
  }
}

// Routine Card
.routine-card {
  background: var(--bg-surface);
  border: 1px solid var(--primary-500);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  transition: all var(--transition-normal) var(--ease-in-out);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-brand);
    opacity: 0;
    transition: opacity var(--transition-normal) var(--ease-in-out);
  }
  
  &:hover {
    border-color: var(--accent-500);
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    
    &::before {
      opacity: 1;
    }
  }
  
  .card-header {
    margin-bottom: var(--space-4);
    
    .routine-title {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--space-2);
      
      h3 {
        font-size: var(--text-lg);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin: 0;
        line-height: var(--leading-tight);
      }
      
      .favorite-btn {
        padding: var(--space-1);
        background: transparent;
        border: none;
        border-radius: var(--radius-md);
        color: var(--text-tertiary);
        cursor: pointer;
        transition: all var(--transition-normal) var(--ease-in-out);
        
        svg {
          width: 1.25rem;
          height: 1.25rem;
          stroke-width: 2;
        }
        
        &:hover {
          color: var(--error-500);
          background: var(--primary-600);
        }
        
        &.favorited {
          color: var(--error-500);
        }
      }
    }
    
    .routine-description {
      font-size: var(--text-sm);
      line-height: var(--leading-relaxed);
      color: var(--text-secondary);
      margin: 0;
    }
  }
  
  .card-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-4);
    margin-bottom: var(--space-4);
    padding: var(--space-4);
    background: var(--primary-600);
    border-radius: var(--radius-lg);
    
    .stat-item {
      text-align: center;
      
      .stat-value {
        display: block;
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--text-primary);
        font-family: var(--font-mono);
        margin-bottom: var(--space-1);
      }
      
      .stat-label {
        font-size: var(--text-xs);
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: var(--font-medium);
      }
    }
  }
  
  .muscle-groups {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
    
    .muscle-tag {
      background: var(--accent-500);
      color: var(--text-on-accent);
      font-size: var(--text-xs);
      font-weight: var(--font-medium);
      padding: 0.25rem 0.5rem;
      border-radius: var(--radius-full);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }
  
  .card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
    font-size: var(--text-sm);
    
    .difficulty-badge {
      font-weight: var(--font-semibold);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
    
    .last-used {
      color: var(--text-tertiary);
    }
  }
  
  .card-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-3);
    
    @media (max-width: 480px) {
      grid-template-columns: 1fr;
      gap: var(--space-2);
    }
    
    .action-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--space-2);
      padding: var(--space-3);
      border: 1px solid var(--primary-500);
      border-radius: var(--radius-md);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      
      svg {
        width: 1rem;
        height: 1rem;
        stroke-width: 2;
      }
      
      &.secondary {
        background: transparent;
        color: var(--text-secondary);
        
        &:hover {
          background: var(--primary-600);
          color: var(--text-primary);
          border-color: var(--accent-500);
        }
      }
      
      &.primary {
        background: var(--accent-500);
        color: var(--text-on-accent);
        border-color: var(--accent-500);
        
        &:hover {
          background: var(--accent-400);
          transform: translateY(-1px);
        }
      }
    }
  }
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--space-16) var(--space-8);
  
  .empty-icon {
    width: 4rem;
    height: 4rem;
    margin-bottom: var(--space-4);
    color: var(--text-tertiary);
    
    svg {
      width: 100%;
      height: 100%;
      stroke-width: 1.5;
    }
  }
  
  h3 {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0 0 var(--space-2) 0;
  }
  
  p {
    color: var(--text-secondary);
    margin: 0 0 var(--space-6) 0;
    max-width: 24rem;
  }
  
  .create-first-routine-btn {
    padding: var(--space-3) var(--space-6);
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--radius-lg);
    color: var(--text-on-accent);
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
  }
}

// Modal Styles
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--space-4);
  backdrop-filter: blur(4px);
}

.routine-detail-modal {
  background: var(--bg-surface);
  border: 1px solid var(--primary-500);
  border-radius: var(--radius-xl);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-2xl);
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6);
    border-bottom: 1px solid var(--primary-500);
    
    h2 {
      font-size: var(--text-xl);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin: 0;
    }
    
    .close-btn {
      padding: var(--space-2);
      background: transparent;
      border: none;
      border-radius: var(--radius-md);
      color: var(--text-tertiary);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      
      svg {
        width: 1.25rem;
        height: 1.25rem;
        stroke-width: 2;
      }
      
      &:hover {
        background: var(--primary-600);
        color: var(--text-primary);
      }
    }
  }
  
  .modal-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-6);
    
    .routine-description {
      font-size: var(--text-base);
      line-height: var(--leading-relaxed);
      color: var(--text-secondary);
      margin: 0 0 var(--space-6) 0;
    }
    
    .routine-info {
      display: flex;
      flex-direction: column;
      gap: var(--space-3);
      margin-bottom: var(--space-6);
      padding: var(--space-4);
      background: var(--primary-600);
      border-radius: var(--radius-lg);
      
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .label {
          font-weight: var(--font-medium);
          color: var(--text-secondary);
        }
        
        .value {
          color: var(--text-primary);
          
          &.difficulty {
            font-weight: var(--font-semibold);
            text-transform: uppercase;
            letter-spacing: 0.05em;
          }
        }
      }
    }
    
    .exercises-list {
      h3 {
        font-size: var(--text-lg);
        font-weight: var(--font-semibold);
        color: var(--text-primary);
        margin: 0 0 var(--space-4) 0;
      }
      
      .exercise-item {
        display: flex;
        gap: var(--space-4);
        padding: var(--space-4);
        background: var(--primary-600);
        border-radius: var(--radius-lg);
        margin-bottom: var(--space-3);
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .exercise-number {
          width: 2rem;
          height: 2rem;
          background: var(--accent-500);
          color: var(--text-on-accent);
          border-radius: var(--radius-full);
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: var(--font-bold);
          font-size: var(--text-sm);
          flex-shrink: 0;
        }
        
        .exercise-info {
          flex: 1;
          
          h4 {
            font-size: var(--text-base);
            font-weight: var(--font-semibold);
            color: var(--text-primary);
            margin: 0 0 var(--space-2) 0;
          }
          
          .exercise-details {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-3);
            font-size: var(--text-sm);
            color: var(--text-secondary);
            
            span {
              background: var(--bg-surface);
              padding: 0.25rem 0.5rem;
              border-radius: var(--radius-md);
              border: 1px solid var(--primary-500);
            }
          }
        }
      }
    }
  }
  
  .modal-actions {
    display: flex;
    gap: var(--space-3);
    padding: var(--space-6);
    border-top: 1px solid var(--primary-500);
    
    .action-btn {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--space-2);
      padding: var(--space-3);
      border: 1px solid var(--primary-500);
      border-radius: var(--radius-md);
      font-size: var(--text-base);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: all var(--transition-normal) var(--ease-in-out);
      
      &.secondary {
        background: transparent;
        color: var(--text-secondary);
        
        &:hover {
          background: var(--primary-600);
          color: var(--text-primary);
        }
      }
      
      &.primary {
        background: var(--accent-500);
        color: var(--text-on-accent);
        border-color: var(--accent-500);
        
        &:hover {
          background: var(--accent-400);
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .routines-page {
    padding: 0 var(--space-4);
  }
  
  .routines-header {
    .header-content {
      flex-direction: column;
      gap: var(--space-4);
      align-items: stretch;
      
      .create-routine-btn {
        justify-content: center;
      }
    }
    
    .controls-section {
      flex-direction: column;
      gap: var(--space-4);
      
      .filter-tabs {
        .filter-btn {
          flex: 1;
          justify-content: center;
          padding: var(--space-2);
          font-size: var(--text-xs);
          
          svg {
            width: 0.875rem;
            height: 0.875rem;
          }
        }
      }
      
      .sort-dropdown {
        align-self: stretch;
        
        .sort-select {
          width: 100%;
        }
      }
    }
  }
  
  .routines-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .routine-card {
    padding: var(--space-4);
    
    .card-stats {
      gap: var(--space-2);
      padding: var(--space-3);
    }
    
    .muscle-groups {
      .muscle-tag {
        font-size: 0.6875rem;
      }
    }
    
    .card-actions {
      grid-template-columns: 1fr;
      gap: var(--space-2);
    }
  }
  
  .routine-detail-modal {
    margin: var(--space-4);
    max-height: calc(100vh - 2rem);
    
    .modal-content {
      padding: var(--space-4);
      
      .exercises-list {
        .exercise-item {
          flex-direction: column;
          gap: var(--space-3);
          
          .exercise-number {
            align-self: flex-start;
          }
          
          .exercise-details {
            gap: var(--space-2);
          }
        }
      }
    }
    
    .modal-actions {
      padding: var(--space-4);
      flex-direction: column;
    }
  }
}

// Animation
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.routine-card {
  animation: slideUp 0.3s ease-out;
}

.routine-detail-modal {
  animation: slideUp 0.2s ease-out;
}

// Focus states
.filter-btn:focus,
.sort-select:focus,
.action-btn:focus,
.favorite-btn:focus,
.close-btn:focus {
  outline: 2px solid var(--accent-500);
  outline-offset: 2px;
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .routine-card,
  .routine-detail-modal,
  .action-btn,
  .create-routine-btn {
    animation: none;
    transition: none;
  }
  
  .routine-card:hover,
  .action-btn:hover,
  .create-routine-btn:hover {
    transform: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .routine-card,
  .routine-detail-modal {
    border-width: 2px;
  }
  
  .card-stats {
    border: 1px solid var(--primary-400);
  }
} 