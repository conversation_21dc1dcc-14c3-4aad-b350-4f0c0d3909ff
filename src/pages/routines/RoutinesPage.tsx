import React, { useState } from 'react';
import './RoutinesPage.scss';
import { Button } from '../../components/common';

// 数据接口定义
interface Exercise {
  id: string;
  name: string;
  sets: number;
  reps: string;
  weight?: number;
  notes?: string;
  restTime?: number; // seconds
}

interface Routine {
  id: string;
  name: string;
  description?: string;
  exercises: Exercise[];
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // minutes
  muscle_groups: string[];
  isPublic: boolean;
  created_at: Date;
  last_used?: Date;
  times_completed: number;
  created_by: string;
  is_favorite: boolean;
}

// Mock数据
const mockRoutines: Routine[] = [
  {
    id: 'routine_1',
    name: 'Push Day - 胸肩三头',
    description: '专注于推举动作的上肢训练计划，适合中级训练者',
    exercises: [
      {
        id: 'ex_1',
        name: '杠铃卧推',
        sets: 4,
        reps: '8-10',
        weight: 80,
        restTime: 180
      },
      {
        id: 'ex_2',
        name: '哑铃肩上推举',
        sets: 3,
        reps: '10-12',
        weight: 25,
        restTime: 120
      },
      {
        id: 'ex_3',
        name: '双杠臂屈伸',
        sets: 3,
        reps: '12-15',
        restTime: 90
      },
      {
        id: 'ex_4',
        name: '哑铃侧平举',
        sets: 4,
        reps: '12-15',
        weight: 12,
        restTime: 60
      }
    ],
    tags: ['推举', '胸部', '肩部', '三头肌'],
    difficulty: 'intermediate',
    duration: 75,
    muscle_groups: ['胸部', '肩部', '三头肌'],
    isPublic: true,
    created_at: new Date('2025-01-15'),
    last_used: new Date('2025-01-17'),
    times_completed: 12,
    created_by: 'user_1',
    is_favorite: true
  },
  {
    id: 'routine_2',
    name: 'Pull Day - 背二头',
    description: '全面的拉伸动作训练，强化背部和二头肌',
    exercises: [
      {
        id: 'ex_5',
        name: '引体向上',
        sets: 4,
        reps: '6-8',
        restTime: 180
      },
      {
        id: 'ex_6',
        name: '杠铃划船',
        sets: 4,
        reps: '8-10',
        weight: 70,
        restTime: 150
      },
      {
        id: 'ex_7',
        name: '哑铃弯举',
        sets: 3,
        reps: '10-12',
        weight: 20,
        restTime: 90
      },
      {
        id: 'ex_8',
        name: '高位下拉',
        sets: 3,
        reps: '12-15',
        weight: 60,
        restTime: 90
      }
    ],
    tags: ['拉伸', '背部', '二头肌'],
    difficulty: 'intermediate',
    duration: 80,
    muscle_groups: ['背部', '二头肌'],
    isPublic: false,
    created_at: new Date('2025-01-12'),
    last_used: new Date('2025-01-16'),
    times_completed: 8,
    created_by: 'user_1',
    is_favorite: false
  },
  {
    id: 'routine_3',
    name: '全身入门训练',
    description: '适合初学者的全身基础训练计划',
    exercises: [
      {
        id: 'ex_9',
        name: '深蹲',
        sets: 3,
        reps: '12-15',
        weight: 40,
        restTime: 120
      },
      {
        id: 'ex_10',
        name: '俯卧撑',
        sets: 3,
        reps: '8-12',
        restTime: 90
      },
      {
        id: 'ex_11',
        name: '哑铃划船',
        sets: 3,
        reps: '10-12',
        weight: 15,
        restTime: 90
      },
      {
        id: 'ex_12',
        name: '平板支撑',
        sets: 3,
        reps: '30-60秒',
        restTime: 60
      }
    ],
    tags: ['全身', '初学者', '基础'],
    difficulty: 'beginner',
    duration: 45,
    muscle_groups: ['全身'],
    isPublic: true,
    created_at: new Date('2025-01-10'),
    last_used: new Date('2025-01-14'),
    times_completed: 5,
    created_by: 'user_2',
    is_favorite: true
  }
];

const RoutinesPage: React.FC = () => {
  const [routines, setRoutines] = useState<Routine[]>(mockRoutines);
  const [filter, setFilter] = useState<'all' | 'favorites' | 'created' | 'public'>('all');
  const [sortBy, setSortBy] = useState<'recent' | 'name' | 'difficulty' | 'duration'>('recent');
  const [selectedRoutine, setSelectedRoutine] = useState<Routine | null>(null);
  // const [showCreateModal, setShowCreateModal] = useState(false); // Removed as functionality not implemented

  // 过滤和排序逻辑
  const filteredAndSortedRoutines = routines
    .filter(routine => {
      switch (filter) {
        case 'favorites':
          return routine.is_favorite;
        case 'created':
          return routine.created_by === 'user_1'; // 当前用户
        case 'public':
          return routine.isPublic;
        default:
          return true;
      }
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'difficulty':
          const difficultyOrder = { beginner: 1, intermediate: 2, advanced: 3 };
          return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];
        case 'duration':
          return a.duration - b.duration;
        case 'recent':
        default:
          return (b.last_used?.getTime() || 0) - (a.last_used?.getTime() || 0);
      }
    });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'var(--success-500)';
      case 'intermediate': return 'var(--warning-500)';
      case 'advanced': return 'var(--error-500)';
      default: return 'var(--text-secondary)';
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return '初级';
      case 'intermediate': return '中级';
      case 'advanced': return '高级';
      default: return '未知';
    }
  };

  const handleToggleFavorite = (routineId: string) => {
    setRoutines(routines.map(routine => 
      routine.id === routineId 
        ? { ...routine, is_favorite: !routine.is_favorite }
        : routine
    ));
  };

  const handleStartWorkout = (routine: Routine) => {
    // 这里会跳转到训练页面
    console.log('开始训练:', routine.name);
  };

  const formatLastUsed = (date?: Date): string => {
    if (!date) return '从未使用';
    
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return '今天';
    if (diffInDays === 1) return '昨天';
    if (diffInDays < 7) return `${diffInDays}天前`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)}周前`;
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
  };

  return (
    <div className="routines-page">
      {/* Header */}
      <div className="routines-header">
        <div className="header-content">
          <Button 
            variant="primary"
            onClick={() => console.log('创建计划功能待实现')}
            leftIcon={
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="12" y1="5" x2="12" y2="19"/>
                <line x1="5" y1="12" x2="19" y2="12"/>
              </svg>
            }
          >
            创建计划
          </Button>
        </div>

        {/* Filters and Sort */}
        <div className="controls-section">
          <div className="filter-tabs">
            <button 
              className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
              onClick={() => setFilter('all')}
            >
              全部计划
            </button>
            <button 
              className={`filter-btn ${filter === 'favorites' ? 'active' : ''}`}
              onClick={() => setFilter('favorites')}
            >
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.57831 8.50903 2.99871 7.05 2.99871C5.59096 2.99871 4.19169 3.57831 3.16 4.61C2.1283 5.64169 1.54871 7.04096 1.54871 8.5C1.54871 9.95904 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39468C21.7563 5.72725 21.351 5.12087 20.84 4.61Z"/>
              </svg>
              收藏的
            </button>
            <button 
              className={`filter-btn ${filter === 'created' ? 'active' : ''}`}
              onClick={() => setFilter('created')}
            >
              我创建的
            </button>
            <button 
              className={`filter-btn ${filter === 'public' ? 'active' : ''}`}
              onClick={() => setFilter('public')}
            >
              公开的
            </button>
          </div>

          <div className="sort-dropdown">
            <select 
              value={sortBy} 
              onChange={(e) => setSortBy(e.target.value as any)}
              className="sort-select"
            >
              <option value="recent">最近使用</option>
              <option value="name">名称排序</option>
              <option value="difficulty">难度排序</option>
              <option value="duration">时长排序</option>
            </select>
          </div>
        </div>
      </div>

      {/* Routines Grid */}
      <div className="routines-grid">
        {filteredAndSortedRoutines.map(routine => (
          <div key={routine.id} className="routine-card">
            {/* Card Header */}
            <div className="card-header">
              <div className="routine-title">
                <h3>{routine.name}</h3>
                <button 
                  className={`favorite-btn ${routine.is_favorite ? 'favorited' : ''}`}
                  onClick={() => handleToggleFavorite(routine.id)}
                  aria-label={routine.is_favorite ? '取消收藏' : '添加收藏'}
                >
                  <svg viewBox="0 0 24 24" fill={routine.is_favorite ? "currentColor" : "none"} stroke="currentColor">
                    <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.57831 8.50903 2.99871 7.05 2.99871C5.59096 2.99871 4.19169 3.57831 3.16 4.61C2.1283 5.64169 1.54871 7.04096 1.54871 8.5C1.54871 9.95904 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39468C21.7563 5.72725 21.351 5.12087 20.84 4.61Z"/>
                  </svg>
                </button>
              </div>
              
              {routine.description && (
                <p className="routine-description">{routine.description}</p>
              )}
            </div>

            {/* Card Stats */}
            <div className="card-stats">
              <div className="stat-item">
                <span className="stat-value">{routine.exercises.length}</span>
                <span className="stat-label">动作</span>
              </div>
              <div className="stat-item">
                <span className="stat-value">{routine.duration}</span>
                <span className="stat-label">分钟</span>
              </div>
              <div className="stat-item">
                <span className="stat-value">{routine.times_completed}</span>
                <span className="stat-label">次完成</span>
              </div>
            </div>

            {/* Muscle Groups */}
            <div className="muscle-groups">
              {routine.muscle_groups.map(group => (
                <span key={group} className="muscle-tag">{group}</span>
              ))}
            </div>

            {/* Difficulty and Meta */}
            <div className="card-meta">
              <span 
                className="difficulty-badge" 
                style={{ color: getDifficultyColor(routine.difficulty) }}
              >
                {getDifficultyText(routine.difficulty)}
              </span>
              <span className="last-used">
                最后使用: {formatLastUsed(routine.last_used)}
              </span>
            </div>

            {/* Card Actions */}
            <div className="card-actions">
              <Button 
                variant="secondary"
                size="sm"
                onClick={() => setSelectedRoutine(routine)}
                leftIcon={
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z"/>
                    <circle cx="12" cy="12" r="3"/>
                  </svg>
                }
              >
                查看详情
              </Button>
              <Button 
                variant="primary"
                size="sm"
                onClick={() => handleStartWorkout(routine)}
                leftIcon={
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <polygon points="5,3 19,12 5,21"/>
                  </svg>
                }
              >
                开始训练
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredAndSortedRoutines.length === 0 && (
        <div className="empty-state">
          <div className="empty-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M15 10L11 14L17 20L21 4L3 11L9 13V19L12 16"/>
            </svg>
          </div>
          <h3>暂无训练计划</h3>
          <p>创建你的第一个训练计划开始健身之旅</p>
          <Button 
            variant="primary"
            onClick={() => console.log('创建计划功能待实现')}
          >
            创建训练计划
          </Button>
        </div>
      )}

      {/* Routine Detail Modal */}
      {selectedRoutine && (
        <div className="modal-overlay" onClick={() => setSelectedRoutine(null)}>
          <div className="routine-detail-modal" onClick={e => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{selectedRoutine.name}</h2>
              <Button 
                variant="ghost"
                size="sm"
                onClick={() => setSelectedRoutine(null)}
                leftIcon={
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <line x1="18" y1="6" x2="6" y2="18"/>
                    <line x1="6" y1="6" x2="18" y2="18"/>
                  </svg>
                }
              />
            </div>
            
            <div className="modal-content">
              {selectedRoutine.description && (
                <p className="routine-description">{selectedRoutine.description}</p>
              )}
              
              <div className="routine-info">
                <div className="info-item">
                  <span className="label">难度:</span>
                  <span 
                    className="value difficulty" 
                    style={{ color: getDifficultyColor(selectedRoutine.difficulty) }}
                  >
                    {getDifficultyText(selectedRoutine.difficulty)}
                  </span>
                </div>
                <div className="info-item">
                  <span className="label">预计时长:</span>
                  <span className="value">{selectedRoutine.duration} 分钟</span>
                </div>
                <div className="info-item">
                  <span className="label">目标肌群:</span>
                  <span className="value">{selectedRoutine.muscle_groups.join(', ')}</span>
                </div>
              </div>

              <div className="exercises-list">
                <h3>训练动作</h3>
                {selectedRoutine.exercises.map((exercise, index) => (
                  <div key={exercise.id} className="exercise-item">
                    <div className="exercise-number">{index + 1}</div>
                    <div className="exercise-info">
                      <h4>{exercise.name}</h4>
                      <div className="exercise-details">
                        <span>{exercise.sets} 组</span>
                        <span>{exercise.reps} 次</span>
                        {exercise.weight && <span>{exercise.weight} kg</span>}
                        {exercise.restTime && <span>休息 {exercise.restTime}s</span>}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="modal-actions">
              <button 
                className="action-btn secondary"
                onClick={() => setSelectedRoutine(null)}
              >
                关闭
              </button>
              <button 
                className="action-btn primary"
                onClick={() => {
                  handleStartWorkout(selectedRoutine);
                  setSelectedRoutine(null);
                }}
              >
                开始训练
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoutinesPage; 