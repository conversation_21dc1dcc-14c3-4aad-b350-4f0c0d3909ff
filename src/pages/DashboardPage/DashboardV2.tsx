import React from 'react'
import { useTheme } from '../../contexts/ThemeContext'
import { Link } from 'react-router-dom'
import WeeklyDatePicker from './components/WeeklyDatePicker/WeeklyDatePicker'
import FitnessProgressCard from './components/FitnessProgressCard/FitnessProgressCard'
import CharacterTaskCard from './components/CharacterTaskCard/CharacterTaskCard'
import UserRecommendationCard from './components/UserRecommendationCard/UserRecommendationCard'
import Icon from '../../components/common/Icon'
import './DashboardV2.scss'

interface DashboardV2Props {
  selectedDate: Date;
  onDateChange: (date: Date) => void;
}

const DashboardV2: React.FC<DashboardV2Props> = ({ selectedDate, onDateChange }) => {
  const { theme } = useTheme()

  // 格式化日期为键值
  const formatDateKey = (date: Date): string => {
    return date.toISOString().split('T')[0]
  }

  // 模拟周日期的健身数据
  const generateWeeklyFitnessData = () => {
    const today = new Date()
    const weeklyData: Record<string, any> = {}
    
    // 生成过去几天的模拟数据
    for (let i = -6; i <= 0; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() + i)
      const dateKey = formatDateKey(date)
      
      // 为每天生成不同的健身数据
      weeklyData[dateKey] = {
        moveProgress: Math.floor(Math.random() * 600) + 200,
        moveGoal: 500,
        exerciseProgress: Math.floor(Math.random() * 40) + 10,
        exerciseGoal: 30,
        standProgress: Math.floor(Math.random() * 12) + 6,
        standGoal: 12
      }
    }
    
    return weeklyData
  }

  const weeklyFitnessData = generateWeeklyFitnessData()

  // 获取选中日期的健身数据
  const getSelectedDateData = () => {
    const dateKey = formatDateKey(selectedDate)
    return weeklyFitnessData[dateKey] || {
      moveProgress: 0,
      moveGoal: 500,
      exerciseProgress: 0,
      exerciseGoal: 30,
      standProgress: 0,
      standGoal: 12
    }
  }

  const selectedDateData = getSelectedDateData()

  // 根据选中日期生成任务数据
  const generateTasksForDate = (date: Date) => {
    const dateKey = formatDateKey(date)
    const fitnessData = weeklyFitnessData[dateKey]
    
    return [
      {
        id: 'workout',
        icon: '🏋️‍♂️',
        title: '开始一次训练',
        description: '完成今天的健身计划',
        completed: fitnessData && fitnessData.exerciseProgress > 0,
        energyReward: 20
      },
      {
        id: 'nutrition',
        icon: '🍎',
        title: '记录一餐',
        description: '记录你的营养摄入',
        completed: fitnessData && fitnessData.moveProgress > 200,
        energyReward: 15
      },
      {
        id: 'hydration',
        icon: '💧',
        title: '喝水打卡',
        description: '保持充足的水分摄入',
        completed: fitnessData && fitnessData.standProgress > 6,
        energyReward: 10
      },
      {
        id: 'social',
        icon: '📱',
        title: '发布动态',
        description: '分享你的健身成果',
        completed: Math.random() > 0.6, // 随机完成状态
        energyReward: 12
      },
      {
        id: 'weight',
        icon: '⚖️',
        title: '记录一次体重',
        description: '跟踪体重变化',
        completed: Math.random() > 0.7, // 随机完成状态
        energyReward: 8
      },
      {
        id: 'mood',
        icon: '😊',
        title: '记录心情',
        description: '记录今日心情状态',
        completed: Math.random() > 0.5, // 随机完成状态
        energyReward: 5
      }
    ]
  }

  const handleDateSelect = (date: Date) => {
    onDateChange(date)
    console.log('选择的日期:', date)
  }

  const tasksForSelectedDate = generateTasksForDate(selectedDate)

  return (
    <div className={`dashboard-v2 theme-${theme}`}>
      {/* 开发工具链接区 */}
      <div className="development-tools">
        <h3>开发者工具</h3>
        <div className="dev-tools-container">
          <Link to="/ui-test" className="dev-tool-link">
            <Icon name="dashboard" size="small" />
            <span>UI测试页面</span>
          </Link>
          <Link to="/icons" className="dev-tool-link">
            <Icon name="search" size="small" />
            <span>图标展示</span>
          </Link>
          <Link to="/icons/guide" className="dev-tool-link">
            <Icon name="book" size="small" />
            <span>图标使用指南</span>
          </Link>
          <Link to="/muscle-demo" className="dev-tool-link">
            <Icon name="heart" size="small" />
            <span>肌肉可视化演示</span>
          </Link>
        </div>
      </div>

      {/* 周日期选择器 */}
      <WeeklyDatePicker
        selectedDate={selectedDate}
        onDateSelect={handleDateSelect}
        fitnessData={weeklyFitnessData}
      />

      {/* 人物和任务卡片 */}
      <CharacterTaskCard
        tasks={tasksForSelectedDate}
        energyValue={120}
        achievementCount={8}
      />

      {/* 数据概览标题 */}
      <div className="dashboard-v2__section-title">
        <h3>📊 数据概览</h3>
      </div>

      {/* 健身进度卡片 - 基于选中日期的数据 */}
      <FitnessProgressCard
        moveProgress={selectedDateData.moveProgress}
        moveGoal={selectedDateData.moveGoal}
        exerciseProgress={selectedDateData.exerciseProgress}
        exerciseGoal={selectedDateData.exerciseGoal}
        standProgress={selectedDateData.standProgress}
        standGoal={selectedDateData.standGoal}
      />

      {/* 用户推荐部分 */}
      <UserRecommendationCard
        onFollow={(userId) => console.log('关注用户:', userId)}
        onViewAll={() => console.log('查看全部用户')}
      />

      {/* 浮动聊天按钮 */}
      <button className="dashboard-v2__chat-button" onClick={() => console.log('打开AI聊天')}>
        <span className="chat-icon">💬</span>
        <span className="chat-text">与我聊天</span>
      </button>
    </div>
  )
}

export default DashboardV2 