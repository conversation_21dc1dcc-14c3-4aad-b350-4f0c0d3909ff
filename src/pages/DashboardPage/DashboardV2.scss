.dashboard-v2 {
  min-height: 100vh;
  background: var(--bg-primary, #ffffff); // 改为纯白背景
  position: relative;
  padding-bottom: 80px; // 为浮动按钮留出空间
  
  // 主题支持 - 暗色模式
  &.theme-dark {
    background: var(--bg-primary, #1f2937); // 暗色模式使用主色
    
    // 开发工具链接区域暗色主题
    .development-tools {
      background: rgba(31, 41, 55, 0.8);
      border-color: #374151;
      
      h3 {
        color: #e5e7eb;
        border-bottom-color: #374151;
      }
      
      .dev-tools-container {
        gap: 12px;
      }
      
      .dev-tool-link {
        background: #374151;
        color: #e5e7eb;
        border-color: #4b5563;
        
        &:hover {
          background: #4b5563;
          border-color: #6366f1;
        }
        
        .icon {
          color: #a5b4fc;
        }
      }
    }
    
    // 健身进度区域暗色主题已移动到 FitnessProgressCard 组件中
    
    // 任务区域暗色主题
    .dashboard-v2__tasks-section {
      background: #1f2937;
      border: 1px solid #374151;
      
      .section-title {
        color: #f9fafb;
      }
      
      .task-item {
        background: #374151;
        border-color: #4b5563;
        
        &:hover {
          background: #4b5563;
          border-color: #6366f1;
        }
        
        &.completed {
          background: linear-gradient(135deg, #064e3b 0%, #065f46 100%);
          border-color: #10b981;
        }
        
        .task-icon {
          background: #111827;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
        
        .task-title {
          color: #f9fafb;
        }
        
        .task-description {
          color: #d1d5db;
        }
        
        .task-status {
          background: #fbbf24;
          color: #92400e;
        }
      }
    }
    
    // 数据面板区域暗色主题
    .dashboard-v2__data-section {
      background: #1f2937;
      border: 1px solid #374151;
      
      .section-title {
        color: #f9fafb;
      }
      
      .data-card {
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
        border: 1px solid #4b5563;
        
        &:hover {
          background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
          border-color: #6366f1;
        }
        
        .data-icon {
          background: #111827;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
        
        .data-title {
          color: #d1d5db;
        }
        
        .data-value {
          color: #f9fafb;
        }
      }
    }
  }
  
  // 开发者工具区域
  .development-tools {
    position: relative;
    margin: 24px auto;
    padding: 16px;
    max-width: 800px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    
    h3 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #64748b;
      border-bottom: 1px solid #e2e8f0;
      padding-bottom: 8px;
    }
    
    .dev-tools-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    
    .dev-tool-link {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      background: #f1f5f9;
      border-radius: 8px;
      border: 1px solid #e2e8f0;
      color: #334155;
      text-decoration: none;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      
      &:hover {
        background: #e2e8f0;
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        border-color: #94a3b8;
      }
      
      .icon {
        color: #64748b;
      }
    }
  }
  
  // 健身进度区域样式已移动到 FitnessProgressCard 组件中
  
  // 区域标题样式
  &__section-title {
    margin: 0 auto 16px auto;
    max-width: min(92vw, 450px);
    width: 100%;
    padding: 0 20px;
    
    h3 {
      font-size: 18px;
      font-weight: bold;
      color: var(--text-primary, #1f2937);
      margin: 0;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  // 任务区域
  &__tasks-section {
    margin: 0 4vw 20px 4vw; // 使用viewport宽度
    max-width: calc(100vw - 8vw); // 确保不超出屏幕
    background: #ffffff;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    
    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: #1f2937;
      margin: 0 0 16px 0;
      display: flex;
      align-items: center;
      gap: 8px;
      
      &::before {
        content: '📋';
        font-size: 20px;
      }
    }
    
    .tasks-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
    
    .task-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: #f9fafb;
      border-radius: 12px;
      border: 2px solid transparent;
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        background: #eef2ff;
        border-color: #c7d2fe;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
      }
      
      &.completed {
        background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
        border-color: #22c55e;
        
        .task-title {
          text-decoration: line-through;
          color: #6b7280;
        }
        
        .task-status {
          background: #22c55e;
          color: white;
        }
      }
      
      .task-icon {
        font-size: 24px;
        width: 40px;
        height: 40px;
        background: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        flex-shrink: 0;
      }
      
      .task-content {
        flex: 1;
        min-width: 0;
        
        .task-title {
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 4px;
        }
        
        .task-description {
          font-size: 14px;
          color: #6b7280;
          line-height: 1.4;
        }
      }
      
      .task-status {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        background: #fbbf24;
        color: #92400e;
        flex-shrink: 0;
      }
    }
  }
  
  // 数据面板区域
  &__data-section {
    margin: 0 4vw 20px 4vw; // 使用viewport宽度
    max-width: calc(100vw - 8vw); // 确保不超出屏幕
    background: #ffffff;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    
    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: #1f2937;
      margin: 0 0 16px 0;
      display: flex;
      align-items: center;
      gap: 8px;
      
      &::before {
        content: '📊';
        font-size: 20px;
      }
    }
    
    .data-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }
    
    .data-card {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-radius: 12px;
      padding: 16px;
      display: flex;
      align-items: center;
      gap: 12px;
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        background: linear-gradient(135deg, #eef2ff 0%, #ddd6fe 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
      }
      
      .data-icon {
        font-size: 24px;
        width: 48px;
        height: 48px;
        background: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        flex-shrink: 0;
      }
      
      .data-content {
        flex: 1;
        min-width: 0;
        
        .data-title {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 4px;
          font-weight: 500;
        }
        
        .data-value {
          font-size: 18px;
          font-weight: bold;
          color: #1f2937;
        }
      }
    }
  }
  
  // 浮动聊天按钮
  &__chat-button {
    position: fixed;
    bottom: 100px; // 在底部导航上方
    right: 20px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);
    transition: all 0.3s ease;
    z-index: 1000;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 32px rgba(79, 70, 229, 0.4);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    .chat-icon {
      font-size: 16px;
    }
    
    .chat-text {
      white-space: nowrap;
    }
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-v2 {
    // 健身进度区域响应式样式已移动到 FitnessProgressCard 组件中
    
    &__section-title {
      max-width: min(95vw, 400px);
      padding: 0 16px;
      
      h3 {
        font-size: 16px;
      }
    }
    
    &__tasks-section,
    &__data-section {
      margin: 0 3vw 16px 3vw; // 使用viewport宽度
      max-width: calc(100vw - 6vw);
      padding: 16px;
    }
    
    .data-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }
    
    .data-card {
      padding: 12px;
      
      .data-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
      }
      
      .data-value {
        font-size: 16px;
      }
    }
    
    &__chat-button {
      bottom: 80px;
      right: 16px;
      padding: 10px 16px;
      font-size: 13px;
      
      .chat-icon {
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 480px) {
  .dashboard-v2 {
    &__section-title {
      max-width: min(96vw, 350px);
      padding: 0 12px;
      
      h3 {
        font-size: 15px;
      }
    }
    
    .task-item {
      padding: 12px;
      
      .task-icon {
        width: 36px;
        height: 36px;
        font-size: 20px;
      }
      
      .task-title {
        font-size: 15px;
      }
      
      .task-description {
        font-size: 13px;
      }
    }
  }
} 