import React from 'react'
import { useTheme } from '../../../../contexts/ThemeContext'
import ThemeToggle from '../../../../components/common/ThemeToggle'
import './DateHeader.scss'

interface DateHeaderProps {
  selectedDate: Date
  onNotificationPress: () => void
  onSettingsPress: () => void
  className?: string
}

const DateHeader: React.FC<DateHeaderProps> = ({
  selectedDate,
  onNotificationPress,
  onSettingsPress,
  className = ''
}) => {
  const { theme } = useTheme()
  const formatDate = (date: Date): string => {
    const today = new Date()
    const isToday = date.toDateString() === today.toDateString()
    
    if (isToday) {
      return '今天'
    }
    
    const month = date.getMonth() + 1
    const day = date.getDate()
    const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    const weekDay = weekDays[date.getDay()]
    
    return `${month}月${day}日 ${weekDay}`
  }

  const getDetailedDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    return `${year}年${month}月${day}日`
  }

  return (
          <div className={`date-header theme-${theme} ${className}`}>
      <div className="date-header__main">
        <div className="date-header__date-info">
          <h2 className="date-header__title">
            📅 {formatDate(selectedDate)}
          </h2>
          <p className="date-header__subtitle">
            {getDetailedDate(selectedDate)}
          </p>
        </div>
        
        <div className="date-header__actions">
          <ThemeToggle />
          
          <button 
            className="date-header__action-btn notification-btn"
            onClick={onNotificationPress}
            type="button"
            aria-label="查看通知"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
              <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
            </svg>
          </button>
          
          <button 
            className="date-header__action-btn settings-btn"
            onClick={onSettingsPress}
            type="button"
            aria-label="打开设置"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="3"/>
              <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
  )
}

export default DateHeader 