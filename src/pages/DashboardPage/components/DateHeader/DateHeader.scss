.date-header {
  background: var(--bg-primary, #ffffff);
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  padding: 16px 20px;
  position: sticky;
  top: 0;
  z-index: 100;
  
  &__main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 100%;
  }
  
  &__date-info {
    flex: 1;
    min-width: 0;
  }
  
  &__title {
    font-size: 18px;
    color: var(--text-primary, #1f2937);
    margin: 0 0 4px 0;
    font-weight: bold;
    line-height: 1.2;
  }
  
  &__subtitle {
    font-size: 14px;
    color: var(--text-secondary, #6b7280);
    margin: 0;
    line-height: 1.2;
  }
  
  &__actions {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
    
    // ThemeToggle 样式调整
    .theme-toggle {
      .theme-toggle-btn {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
        background: #f9fafb;
        
        .theme-label {
          display: none; // 在DateHeader中隐藏标签
        }
      }
    }
  }
  
  &__action-btn {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    background: var(--bg-surface, #f9fafb);
    border: 1px solid var(--border-color, #e5e7eb);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--primary-500, #4F46E5);
    
    &:hover {
      background: #eef2ff;
      border-color: #4F46E5;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
    }
    
    &:active {
      transform: translateY(0);
      box-shadow: none;
    }
    
    svg {
      stroke-width: 2;
    }
  }
  
  .notification-btn {
    &:hover {
      background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
      border-color: #f59e0b;
      color: #f59e0b;
    }
  }
  
  .settings-btn {
    &:hover {
      background: linear-gradient(135deg, #e0e7ff 0%, #8b5cf6 100%);
      border-color: #8b5cf6;
      color: #8b5cf6;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .date-header {
    padding: 12px 16px;
    
    &__title {
      font-size: 16px;
    }
    
    &__subtitle {
      font-size: 12px;
    }
    
    &__actions {
      gap: 8px;
    }
    
    &__action-btn {
      width: 40px;
      height: 40px;
    }
  }
}

// 暗黑主题适配
.date-header.theme-dark {
  background: var(--bg-primary);
  border-bottom-color: var(--border-color);
  
  .date-header__title {
    color: var(--text-primary);
  }
  
  .date-header__subtitle {
    color: var(--text-secondary);
  }
  
  .date-header__action-btn {
    background: var(--bg-surface);
    border-color: var(--border-color);
    color: var(--primary-400);
    
    &:hover {
      background: var(--bg-hover);
      border-color: var(--primary-500);
    }
  }
} 