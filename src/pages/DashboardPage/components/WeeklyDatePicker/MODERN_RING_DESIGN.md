# Mini Apple Watch Rings - 简化设计说明

## 概述
这是一个用于显示每日健身数据的迷你苹果手表环组件，采用简化的圆环设计，去除了网格和阴影效果，专注于清晰的数据展示。

## 设计理念
- **简洁性**: 去除了视觉干扰元素，专注于数据本身
- **清晰性**: 使用清晰的圆环显示进度信息
- **主题适配**: 根据深色/浅色主题自动调整颜色
- **响应式**: 适配不同设备尺寸

## 技术实现

### 核心特性
1. **主题适配颜色系统**
   - 深色主题: 红色 #ef4444, 绿色 #22c55e, 橙色 #f59e0b
   - 浅色主题: 深红色 #dc2626, 深绿色 #16a34a, 深橙色 #d97706

2. **进度动画**
   - 流畅的进度显示动画
   - 完成状态的脉冲效果
   - 圆角线端 (stroke-linecap="round")

3. **交互效果**
   - 悬停时的缩放效果
   - 亮度和饱和度增强
   - 完成状态的特殊动画

## 组件结构
```
MiniAppleWatchRings
├── Move Ring (外圈) - 活动数据
├── Exercise Ring (中圈) - 锻炼数据
└── Stand Ring (内圈) - 站立数据
```

## 环形参数
- **Move Ring**: 半径 18px, 线宽 3px
- **Exercise Ring**: 半径 14px, 线宽 3px  
- **Stand Ring**: 半径 10px, 线宽 3px

## 动画效果
- **进度动画**: 1.5秒 ease-out 动画
- **完成脉冲**: 2秒无限循环脉冲效果
- **悬停缩放**: 1.05倍缩放效果

## 无障碍性
- 高对比度模式支持
- 减少动画偏好支持
- 键盘导航支持
- 焦点状态指示

## 响应式设计
- 移动设备优化的缩放比例
- 不同屏幕尺寸的适配
- 打印样式优化

## 使用方法
```tsx
<MiniAppleWatchRings
  moveProgress={850}
  moveGoal={1000}
  exerciseProgress={25}
  exerciseGoal={30}
  standProgress={10}
  standGoal={12}
/>
```

## 性能优化
- CSS动画硬件加速
- 减少不必要的重绘
- 优化的SVG渲染
- 响应式媒体查询

## 维护说明
- 组件采用模块化设计，易于维护
- 主题颜色集中管理
- 动画效果可通过CSS变量调整
- 支持用户偏好设置（减少动画等） 