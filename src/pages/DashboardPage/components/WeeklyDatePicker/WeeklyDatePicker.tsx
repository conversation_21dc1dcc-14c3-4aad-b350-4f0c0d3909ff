import React from 'react'
import { useTheme } from '../../../../contexts/ThemeContext'
import MiniAppleWatchRings from './MiniAppleWatchRings'
import Icon from '../../../../components/common/Icon'
import './WeeklyDatePicker.scss'

interface FitnessData {
  moveProgress: number
  moveGoal: number
  exerciseProgress: number
  exerciseGoal: number
  standProgress: number
  standGoal: number
}

interface WeeklyDatePickerProps {
  selectedDate: Date
  onDateSelect: (date: Date) => void
  className?: string
  fitnessData?: Record<string, FitnessData>
}

interface DateItem {
  date: Date
  day: number
  weekDay: string
  isToday: boolean
  isPast: boolean
  isFuture: boolean
  isSelected: boolean
  fitnessData?: FitnessData
}

const WeeklyDatePicker: React.FC<WeeklyDatePickerProps> = ({
  selectedDate,
  onDateSelect,
  className = '',
  fitnessData = {}
}) => {
  const { theme } = useTheme()
  
  const formatDateKey = (date: Date): string => {
    return date.toISOString().split('T')[0]
  }
  
  const generateWeekDates = (): DateItem[] => {
    const today = new Date()
    const dates: DateItem[] = []
    
    // 以今天为中心，显示过去4天，今天，未来2天
    const pastDays = 4 // 过去4天
    const futureDays = 2 // 未来2天
    
    for (let i = -pastDays; i <= futureDays; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() + i)
      
      const isToday = i === 0
      const isPast = i < 0
      const isFuture = i > 0
      const isSelected = date.toDateString() === selectedDate.toDateString()
      
      const weekDays = ['日', '一', '二', '三', '四', '五', '六']
      const weekDay = weekDays[date.getDay()]
      const dateKey = formatDateKey(date)
      
      dates.push({
        date,
        day: date.getDate(),
        weekDay,
        isToday,
        isPast,
        isFuture,
        isSelected,
        fitnessData: fitnessData[dateKey]
      })
    }
    
    return dates
  }

  const handleDateClick = (dateItem: DateItem) => {
    if (!dateItem.isFuture) {
      onDateSelect(dateItem.date)
    }
  }

  const calculateWeeklyStats = () => {
    const dates = generateWeekDates()
    let workoutDays = 0
    let totalMinutes = 0
    
    dates.forEach(dateItem => {
      if (dateItem.fitnessData && !dateItem.isFuture) {
        if (dateItem.fitnessData.exerciseProgress > 0) {
          workoutDays++
          totalMinutes += dateItem.fitnessData.exerciseProgress
        }
      }
    })
    
    return { workoutDays, totalMinutes }
  }

  const dates = generateWeekDates()
  const { workoutDays, totalMinutes } = calculateWeeklyStats()

  return (
    <div className={`weekly-date-picker-card theme-${theme} ${className}`}>
      {/* 标题栏 */}
      <div className="weekly-date-picker-card__header">
        <h2 className="weekly-date-picker-card__title">本周</h2>
        <button className="weekly-date-picker-card__nav-btn">
          <Icon name="arrow-right" size="small" />
        </button>
      </div>

      {/* 日期选择器 */}
      <div className="weekly-date-picker-card__calendar">
        {dates.map((dateItem, index) => (
          <div
            key={index}
            className={`weekly-date-picker-card__date-item ${
              dateItem.isSelected ? 'selected' : ''
            } ${
              dateItem.isToday && !dateItem.isSelected ? 'today' : ''
            } ${
              dateItem.isFuture ? 'future disabled' : ''
            } ${
              dateItem.isPast ? 'past' : ''
            } ${
              dateItem.fitnessData ? 'has-data' : ''
            }`}
            onClick={() => handleDateClick(dateItem)}
          >
            {/* 周信息 - 移动到上方 */}
            <div className="weekly-date-picker-card__weekday">
              周{dateItem.weekDay}
            </div>
            
            {/* 日期圆圈 */}
            <div className="weekly-date-picker-card__date-circle">
              {/* 三环显示 - 只在未选中状态显示 */}
              {dateItem.fitnessData && !dateItem.isFuture && !dateItem.isSelected && (
                <div className="weekly-date-picker-card__rings">
                  <MiniAppleWatchRings
                    moveProgress={dateItem.fitnessData.moveProgress}
                    moveGoal={dateItem.fitnessData.moveGoal}
                    exerciseProgress={dateItem.fitnessData.exerciseProgress}
                    exerciseGoal={dateItem.fitnessData.exerciseGoal}
                    standProgress={dateItem.fitnessData.standProgress}
                    standGoal={dateItem.fitnessData.standGoal}
                  />
                </div>
              )}
              
              {/* 数字显示 */}
              <div className="weekly-date-picker-card__day-number">
                {dateItem.isToday && !dateItem.isSelected ? '今' : dateItem.day}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 统计信息 */}
      <div className="weekly-date-picker-card__stats">
        <div className="weekly-date-picker-card__stat-item">
          <div className="weekly-date-picker-card__stat-icon">⚡</div>
          <div className="weekly-date-picker-card__stat-content">
            <div className="weekly-date-picker-card__stat-number">{workoutDays}</div>
            <div className="weekly-date-picker-card__stat-label">周</div>
            <div className="weekly-date-picker-card__stat-description">目前连续记录</div>
          </div>
        </div>
        
        <div className="weekly-date-picker-card__stat-divider"></div>
        
        <div className="weekly-date-picker-card__stat-item">
          <div className="weekly-date-picker-card__stat-icon">⏱</div>
          <div className="weekly-date-picker-card__stat-content">
            <div className="weekly-date-picker-card__stat-number">{totalMinutes}</div>
            <div className="weekly-date-picker-card__stat-unit">/100</div>
            <div className="weekly-date-picker-card__stat-label">分钟</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default WeeklyDatePicker 