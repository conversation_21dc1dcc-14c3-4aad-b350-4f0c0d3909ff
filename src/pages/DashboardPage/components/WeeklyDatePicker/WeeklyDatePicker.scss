.weekly-date-picker-card {
  background: var(--bg-primary);
  border-radius: 24px;
  padding: 24px;
  margin: 0 auto 20px auto; // 使用 auto 左右边距实现居中
  max-width: min(92vw, 450px); // 限制最大宽度，并使用更小的 viewport 百分比
  width: 100%; // 确保宽度适应容器
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
  }
  
  &__title {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.5px;
  }
  
  &__nav-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    background: var(--bg-surface);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
    
    &:hover {
      background: var(--bg-hover);
      color: var(--text-primary);
      transform: translateX(2px);
    }
  }
  
  &__calendar {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    margin-bottom: 32px;
  }
  
  &__date-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover:not(.disabled) {
      transform: translateY(-2px);
    }
    
    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
  }
  
  &__weekday {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-tertiary);
    text-align: center;
    letter-spacing: 0.2px;
    margin-bottom: 8px;
    padding: 4px 8px;
    border-radius: 12px;
    background: transparent;
    transition: all 0.2s ease;
    min-width: 52px;
    
    // 默认历史日期的周信息
    .weekly-date-picker-card__date-item.past:not(.today):not(.selected) & {
      color: var(--text-secondary);
      font-weight: 500;
    }
    
    // 当天的周信息加粗并使用强调色 - 去除边框
    .weekly-date-picker-card__date-item.today:not(.selected) & {
      font-weight: 700;
      color: var(--accent-500);
      background: rgba(59, 130, 246, 0.1);
    }
    
    // 选中日期的周信息高亮 - 确保高对比度
    .weekly-date-picker-card__date-item.selected & {
      font-weight: 700;
      color: #ffffff !important; // 强制白色文本确保高对比度
      background: var(--accent-600); // 深蓝色背景适合浅色主题
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
    }
    
    // 未来日期的周信息 - 提升可读性
    .weekly-date-picker-card__date-item.future.disabled & {
      color: var(--text-disabled);
      opacity: 0.8;  /* 从0.6提升到0.8，增强可读性 */
    }
  }
  
  &__date-circle {
    position: relative;
    width: 64px;
    height: 64px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-surface);
    border: 2px solid transparent;
    transition: all 0.2s ease;
    
    // 历史日期默认状态
    .weekly-date-picker-card__date-item.past:not(.today):not(.selected) & {
      background: var(--bg-surface);
      border-color: var(--border-color);
      opacity: 0.8;
    }
    
    // 当天状态 - 去除边框，保持干净外观
    .weekly-date-picker-card__date-item.today:not(.selected) & {
      background: var(--bg-surface);
      border-color: transparent;
    }
    
    // 选中状态 - 纯色背景确保一致性
    .weekly-date-picker-card__date-item.selected & {
      background: var(--accent-500);  /* 纯蓝色背景 */
      border-color: var(--accent-500);
      border-width: 2px;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      transform: scale(1.15);
    }
    
    // 未来日期 - 提升可读性
    .weekly-date-picker-card__date-item.future.disabled & {
      background: var(--bg-surface);
      border-color: var(--border-color);
      opacity: 0.7;  /* 从0.5提升到0.7，增强可读性 */
    }
  }
  
  &__day-number {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-secondary);
    z-index: 3;
    position: relative;
    transition: all 0.2s ease;
    
    // 历史日期数字颜色
    .weekly-date-picker-card__date-item.past:not(.today):not(.selected) & {
      color: var(--text-secondary);
      font-weight: 600;
    }
    
    // 当天状态 - 蓝色强调
    .weekly-date-picker-card__date-item.today:not(.selected) & {
      color: var(--accent-500);
      font-weight: 700;
      font-size: 18px;
    }
    
    // 选中状态 - 确保最高对比度白色文本
    .weekly-date-picker-card__date-item.selected & {
      color: #000000 !important; // 选中状态数字改为黑色
      font-size: 22px;
      font-weight: 700;
      text-shadow: none; // 移除阴影
    }
    
    // 未来日期 - 提升可读性
    .weekly-date-picker-card__date-item.future.disabled & {
      color: var(--text-disabled);
      opacity: 0.8; // 从0.7提升到0.8，进一步增强可读性
    }
    
    // 有数据的日期 - 适当缩小以配合三环
    .weekly-date-picker-card__date-item.has-data:not(.selected) & {
      font-size: 13px;
      font-weight: 600;
    }
  }
  
  &__rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    opacity: 0.9;
    
    .mini-apple-watch-rings {
      transform: scale(1.4);
    }
    
    // 当天三环显示增强 - 增大尺寸
    .weekly-date-picker-card__date-item.today:not(.selected) & {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.5);
    }
    
    // 历史日期三环显示 - 增大尺寸
    .weekly-date-picker-card__date-item.past:not(.today):not(.selected) & {
      opacity: 0.7;
      transform: translate(-50%, -50%) scale(1.3);
    }
    
    // 悬停时增强显示 - 增大尺寸
    .weekly-date-picker-card__date-item:hover:not(.disabled) & {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.6);
    }
  }
  
  &__stats {
    display: flex;
    align-items: center;
    padding-top: 24px;
    border-top: 1px solid var(--border-color);
  }
  
  &__stat-item {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
  }
  
  &__stat-icon {
    font-size: 20px;
    width: 24px;
    text-align: center;
  }
  
  &__stat-content {
    display: flex;
    align-items: baseline;
    gap: 4px;
    flex-wrap: wrap;
  }
  
  &__stat-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1;
  }
  
  &__stat-unit {
    font-size: 18px;
    color: var(--text-tertiary);
    font-weight: 500;
  }
  
  &__stat-label {
    font-size: 18px;
    color: var(--text-primary);
    font-weight: 600;
  }
  
  &__stat-description {
    font-size: 14px;
    color: var(--text-secondary);
    width: 100%;
    margin-top: 2px;
  }
  
  &__stat-divider {
    width: 1px;
    height: 48px;
    background: var(--border-color);
    margin: 0 24px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .weekly-date-picker-card {
    padding: 20px;
    border-radius: 20px;
    margin: 0 auto 16px auto; // 居中
    max-width: min(95vw, 400px); // 稍微增大移动端宽度
    
    &__header {
      margin-bottom: 24px;
    }
    
    &__title {
      font-size: 24px;
    }
    
    &__nav-btn {
      width: 32px;
      height: 32px;
      border-radius: 10px;
    }
    
    &__calendar {
      gap: 4px;
      margin-bottom: 24px;
    }
    
    &__weekday {
      font-size: 11px;
      margin-bottom: 6px;
      padding: 3px 6px;
      min-width: 44px;
    }
    
    &__date-circle {
      width: 52px;
      height: 52px;
      
      .weekly-date-picker-card__date-item.selected & {
        transform: scale(1.1);
      }
    }
    
    &__day-number {
      font-size: 14px;
      
      .weekly-date-picker-card__date-item.today:not(.selected) & {
        font-size: 16px;
      }
      
      .weekly-date-picker-card__date-item.selected & {
        font-size: 18px;
      }
      
      .weekly-date-picker-card__date-item.has-data:not(.selected) & {
        font-size: 12px;
      }
    }
    
          &__rings {
        .mini-apple-watch-rings {
          transform: scale(1.2);
        }
        
        .weekly-date-picker-card__date-item.today:not(.selected) & {
          transform: translate(-50%, -50%) scale(1.3);
        }
        
        .weekly-date-picker-card__date-item:hover:not(.disabled) & {
          transform: translate(-50%, -50%) scale(1.4);
        }
      }
    
    &__stats {
      padding-top: 20px;
    }
    
    &__stat-item {
      gap: 12px;
    }
    
    &__stat-icon {
      font-size: 18px;
    }
    
    &__stat-number {
      font-size: 28px;
    }
    
    &__stat-unit {
      font-size: 16px;
    }
    
    &__stat-label {
      font-size: 16px;
    }
    
    &__stat-description {
      font-size: 13px;
    }
    
    &__stat-divider {
      height: 40px;
      margin: 0 16px;
    }
  }
}

@media (max-width: 480px) {
  .weekly-date-picker-card {
    padding: 16px;
    border-radius: 16px;
    margin: 0 auto 16px auto; // 居中
    max-width: min(96vw, 350px); // 小屏幕适配
    
    &__header {
      margin-bottom: 20px;
    }
    
    &__title {
      font-size: 22px;
    }
    
    &__nav-btn {
      width: 28px;
      height: 28px;
      border-radius: 8px;
    }
    
    &__calendar {
      gap: 2px;
      margin-bottom: 20px;
    }
    
    &__weekday {
      font-size: 10px;
      margin-bottom: 4px;
      padding: 2px 4px;
      min-width: 36px;
    }
    
    &__date-circle {
      width: 44px;
      height: 44px;
      
      .weekly-date-picker-card__date-item.selected & {
        transform: scale(1.05);
      }
    }
    
    &__day-number {
      font-size: 12px;
      
      .weekly-date-picker-card__date-item.today:not(.selected) & {
        font-size: 14px;
      }
      
      .weekly-date-picker-card__date-item.selected & {
        font-size: 16px;
      }
      
      .weekly-date-picker-card__date-item.has-data:not(.selected) & {
        font-size: 9px;
      }
    }
    
          &__rings {
        .mini-apple-watch-rings {
          transform: scale(1.0);
        }
        
        .weekly-date-picker-card__date-item.today:not(.selected) & {
          transform: translate(-50%, -50%) scale(1.15);
        }
        
        .weekly-date-picker-card__date-item:hover:not(.disabled) & {
          transform: translate(-50%, -50%) scale(1.25);
        }
      }
    
    &__stats {
      padding-top: 16px;
      display: flex; // 保持flex布局一行两列
      flex-direction: row; // 强制横向布局
      gap: 16px;
    }
    
    &__stat-item {
      gap: 12px;
      flex: 1; // 每个项目占据相等空间
    }
    
    &__stat-number {
      font-size: 24px;
    }
    
    &__stat-unit {
      font-size: 14px;
    }
    
    &__stat-label {
      font-size: 14px;
    }
    
    &__stat-description {
      font-size: 12px;
    }
    
    &__stat-divider {
      display: block; // 保持分割线显示
      height: 40px;
      margin: 0 8px; // 缩小间距以适应小屏
    }
  }
}

// 暗黑主题适配
.weekly-date-picker-card.theme-dark {
  background: var(--bg-primary);
  border-color: var(--border-color);
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.3);
  
  .weekly-date-picker-card__title {
    color: var(--text-primary);
  }
  
  .weekly-date-picker-card__nav-btn {
    background: var(--bg-surface);
    color: var(--text-secondary);
    
    &:hover {
      background: var(--bg-hover);
      color: var(--text-primary);
    }
  }
  
  .weekly-date-picker-card__weekday {
    color: var(--text-tertiary);
    
    .weekly-date-picker-card__date-item.past:not(.today):not(.selected) & {
      color: var(--text-secondary);
    }
    
         .weekly-date-picker-card__date-item.today:not(.selected) & {
       color: var(--accent-400);
       background: rgba(96, 165, 250, 0.15);
     }
    
    .weekly-date-picker-card__date-item.selected & {
      color: #ffffff;  /* 纯白文本确保对比度 */
      background: var(--accent-500);  /* 纯蓝色背景 */
    }
    
    .weekly-date-picker-card__date-item.future.disabled & {
      color: var(--text-disabled);
    }
  }
  
  .weekly-date-picker-card__date-circle {
    background: var(--bg-surface);
    
    .weekly-date-picker-card__date-item.past:not(.today):not(.selected) & {
      background: var(--bg-surface);
      border-color: var(--border-color);
    }
    
         .weekly-date-picker-card__date-item.today:not(.selected) & {
       background: var(--bg-surface);
       border-color: transparent;
     }
    
    .weekly-date-picker-card__date-item.selected & {
      background: linear-gradient(135deg, var(--accent-500), var(--accent-400));
      border-color: var(--accent-500);
    }
    
    .weekly-date-picker-card__date-item.future.disabled & {
      background: var(--bg-surface);
      border-color: var(--border-color);
    }
  }
  
  .weekly-date-picker-card__day-number {
    color: var(--text-secondary);
    
    .weekly-date-picker-card__date-item.past:not(.today):not(.selected) & {
      color: var(--text-secondary);
    }
    
    .weekly-date-picker-card__date-item.today:not(.selected) & {
      color: var(--accent-400);
    }
    
    .weekly-date-picker-card__date-item.selected & {
      color: white;
    }
    
    .weekly-date-picker-card__date-item.future.disabled & {
      color: var(--text-disabled);
    }
  }
  
  .weekly-date-picker-card__stats {
    border-top-color: var(--border-color);
  }
  
  .weekly-date-picker-card__stat-number {
    color: var(--text-primary);
  }
  
  .weekly-date-picker-card__stat-unit {
    color: var(--text-tertiary);
  }
  
  .weekly-date-picker-card__stat-label {
    color: var(--text-primary);
  }
  
  .weekly-date-picker-card__stat-description {
    color: var(--text-secondary);
  }
  
  .weekly-date-picker-card__stat-divider {
    background: var(--border-color);
  }
}

// 浅色主题适配
.weekly-date-picker-card.theme-light {
  background: var(--bg-primary);
  border-color: var(--border-color);
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.05);
  
  .weekly-date-picker-card__title {
    color: var(--text-primary);
  }
  
  .weekly-date-picker-card__nav-btn {
    background: var(--bg-surface);
    color: var(--text-secondary);
    
    &:hover {
      background: var(--bg-hover);
      color: var(--text-primary);
    }
  }
  
  .weekly-date-picker-card__weekday {
    color: var(--text-tertiary);
    
    .weekly-date-picker-card__date-item.past:not(.today):not(.selected) & {
      color: var(--text-secondary);
    }
    
         .weekly-date-picker-card__date-item.today:not(.selected) & {
       color: var(--accent-600);
       background: rgba(59, 130, 246, 0.08);
     }
    
    .weekly-date-picker-card__date-item.selected & {
      color: #ffffff;  /* 纯白文本确保对比度 */
      background: var(--accent-600);  /* 深蓝色背景适合浅色主题 */
    }
    
    .weekly-date-picker-card__date-item.future.disabled & {
      color: var(--text-disabled);
    }
  }
  
  .weekly-date-picker-card__date-circle {
    background: var(--bg-surface);
    
    .weekly-date-picker-card__date-item.past:not(.today):not(.selected) & {
      background: var(--bg-surface);
      border-color: var(--border-color);
    }
    
         .weekly-date-picker-card__date-item.today:not(.selected) & {
       background: var(--bg-surface);
       border-color: transparent;
     }
    
         .weekly-date-picker-card__date-item.selected & {
       background: var(--accent-600);  /* 纯深蓝色背景 */
       border-color: var(--accent-600);
       box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
     }
    
    .weekly-date-picker-card__date-item.future.disabled & {
      background: var(--bg-surface);
      border-color: var(--border-color);
    }
  }
  
  .weekly-date-picker-card__day-number {
    color: var(--text-secondary);
    
    .weekly-date-picker-card__date-item.past:not(.today):not(.selected) & {
      color: var(--text-secondary);
    }
    
    .weekly-date-picker-card__date-item.today:not(.selected) & {
      color: var(--accent-600);
    }
    
    .weekly-date-picker-card__date-item.selected & {
      color: white;
    }
    
    .weekly-date-picker-card__date-item.future.disabled & {
      color: var(--text-disabled);
    }
  }
  
  .weekly-date-picker-card__stats {
    border-top-color: var(--border-color);
  }
  
  .weekly-date-picker-card__stat-number {
    color: var(--text-primary);
  }
  
  .weekly-date-picker-card__stat-unit {
    color: var(--text-tertiary);
  }
  
  .weekly-date-picker-card__stat-label {
    color: var(--text-primary);
  }
  
  .weekly-date-picker-card__stat-description {
    color: var(--text-secondary);
  }
  
  .weekly-date-picker-card__stat-divider {
    background: var(--border-color);
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .weekly-date-picker-card {
    border-width: 2px;
    
    &__date-circle {
      border-width: 3px;
      
             .weekly-date-picker-card__date-item.selected & {
         border-width: 3px;
         box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
       }
       
       .weekly-date-picker-card__date-item.today:not(.selected) & {
         border-width: 2px;
         border-color: var(--accent-600);
         box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.4);
       }
    }
    
    &__weekday {
      .weekly-date-picker-card__date-item.selected & {
        background: #000;
        color: #fff;
        border: 2px solid #000;
      }
      
             .weekly-date-picker-card__date-item.today:not(.selected) & {
         background: rgba(59, 130, 246, 0.2);
       }
    }
    
    &__day-number {
      .weekly-date-picker-card__date-item.selected & {
        color: #fff;
        font-weight: 800;
      }
      
      .weekly-date-picker-card__date-item.today:not(.selected) & {
        font-weight: 800;
      }
    }
    
    &__stats {
      border-top-width: 2px;
    }
    
    &__stat-divider {
      width: 2px;
    }
  }
}

// 减少动画
@media (prefers-reduced-motion: reduce) {
  .weekly-date-picker-card {
    &__date-item,
    &__date-circle,
    &__day-number,
    &__weekday,
    &__rings,
    &__nav-btn {
      transition: none;
    }
    
    &__date-item:hover:not(.disabled) {
      transform: none;
    }
    
    &__nav-btn:hover {
      transform: none;
    }
    
    &__date-circle {
      .weekly-date-picker-card__date-item.selected & {
        transform: none;
      }
    }
    
         &__rings {
       .weekly-date-picker-card__date-item.today:not(.selected) & {
         transform: translate(-50%, -50%) scale(1.5);
       }
       
       .weekly-date-picker-card__date-item:hover:not(.disabled) & {
         transform: translate(-50%, -50%) scale(1.6);
       }
     }
  }
} 