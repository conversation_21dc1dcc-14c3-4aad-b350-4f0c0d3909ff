.mini-apple-watch-rings {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  
  &__svg {
    display: block;
    transform: rotate(0deg);
    transition: transform 0.3s ease;
  }
  
  &__progress-ring {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: progressAnimation 1.5s ease-out forwards;
    
    &.completed {
      // 去除复杂的脉冲动画，只保留简单的完成状态
      opacity: 1;
    }
  }
  
  // 简化的悬停效果
  &:hover {
    .mini-apple-watch-rings__svg {
      transform: scale(1.05);
    }
    
    .mini-apple-watch-rings__progress-ring {
      // 去除光晕效果，只保留轻微的透明度变化
      opacity: 0.8;
    }
  }
}

// 进度动画 - 保持原有的平滑进度显示
@keyframes progressAnimation {
  0% {
    stroke-dashoffset: var(--circumference, 565.48);
  }
  100% {
    stroke-dashoffset: var(--progress-offset, 0);
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .mini-apple-watch-rings {
    &__progress-ring {
      stroke-width: 4;
    }
  }
}

// 减少动画偏好
@media (prefers-reduced-motion: reduce) {
  .mini-apple-watch-rings {
    &__svg {
      transition: none;
    }
    
    &__progress-ring {
      animation: none;
      transition: none;
    }
    
    &:hover {
      .mini-apple-watch-rings__svg {
        transform: none;
      }
    }
  }
}

// 响应式设计 - 移动设备优化
@media (max-width: 768px) {
  .mini-apple-watch-rings {
    &:hover {
      .mini-apple-watch-rings__svg {
        transform: scale(1.02);
      }
    }
  }
}

@media (max-width: 480px) {
  .mini-apple-watch-rings {
    &:hover {
      .mini-apple-watch-rings__svg {
        transform: scale(1.01);
      }
    }
  }
} 