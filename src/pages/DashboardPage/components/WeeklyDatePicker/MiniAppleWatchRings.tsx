import React from 'react'
import { useTheme } from '../../../../contexts/ThemeContext'
import './MiniAppleWatchRings.scss'

interface MiniAppleWatchRingsProps {
  moveProgress: number
  moveGoal: number
  exerciseProgress: number
  exerciseGoal: number
  standProgress: number
  standGoal: number
  className?: string
}

const MiniAppleWatchRings: React.FC<MiniAppleWatchRingsProps> = ({
  moveProgress,
  moveGoal,
  exerciseProgress,
  exerciseGoal,
  standProgress,
  standGoal,
  className = ''
}) => {
  const { theme } = useTheme()

  const calculatePercentage = (progress: number, goal: number): number => {
    return Math.min((progress / goal) * 100, 100)
  }

  const calculateStrokeDashoffset = (percentage: number, circumference: number): number => {
    return circumference - (percentage / 100) * circumference
  }

  // 统一的配色方案 - 与主要的 AppleWatchRings 保持一致
  const getThemeColors = () => {
    return {
      move: '#FF6B35',     // 统一的橙红色
      exercise: '#4CAF50', // 统一的绿色
      stand: '#2196F3'     // 统一的蓝色
    }
  }

  const colors = getThemeColors()

  // 迷你环形参数 - 调整半径和线条宽度避免重叠
  const ringData = [
    {
      id: 'move',
      progress: moveProgress,
      goal: moveGoal,
      color: colors.move,
      radius: 16, // 从18减少到16
      strokeWidth: 2.5 // 从3减少到2.5
    },
    {
      id: 'exercise',
      progress: exerciseProgress,
      goal: exerciseGoal,
      color: colors.exercise,
      radius: 12, // 从14减少到12
      strokeWidth: 2.5 // 从3减少到2.5
    },
    {
      id: 'stand',
      progress: standProgress,
      goal: standGoal,
      color: colors.stand,
      radius: 8, // 从10减少到8
      strokeWidth: 2.5 // 从3减少到2.5
    }
  ]

  const svgSize = 40 // 从44减少到40，给圆环更多空间
  const center = svgSize / 2

  return (
    <div className={`mini-apple-watch-rings theme-${theme} ${className}`}>
      <svg
        width={svgSize}
        height={svgSize}
        viewBox={`0 0 ${svgSize} ${svgSize}`}
        className="mini-apple-watch-rings__svg"
      >
        {/* 进度环 */}
        {ringData.map((ring) => {
          const circumference = 2 * Math.PI * ring.radius
          const percentage = calculatePercentage(ring.progress, ring.goal)
          const strokeDashoffset = calculateStrokeDashoffset(percentage, circumference)
          const isCompleted = ring.progress >= ring.goal

          return (
            <g key={ring.id}>
              {/* 背景环 */}
              <circle
                cx={center}
                cy={center}
                r={ring.radius}
                fill="none"
                stroke={ring.color}
                strokeWidth={ring.strokeWidth}
                opacity={0.15}
                strokeLinecap="round"
              />
              
              {/* 进度环 - 去除光晕和呼吸效果 */}
              <circle
                cx={center}
                cy={center}
                r={ring.radius}
                fill="none"
                stroke={ring.color}
                strokeWidth={ring.strokeWidth}
                strokeDasharray={circumference}
                strokeDashoffset={strokeDashoffset}
                strokeLinecap="round"
                transform={`rotate(-90 ${center} ${center})`}
                className={`mini-apple-watch-rings__progress-ring ${isCompleted ? 'completed' : ''}`}
                style={{
                  '--circumference': circumference,
                  '--progress-offset': strokeDashoffset
                } as React.CSSProperties}
              />
            </g>
          )
        })}
      </svg>
    </div>
  )
}

export default MiniAppleWatchRings 