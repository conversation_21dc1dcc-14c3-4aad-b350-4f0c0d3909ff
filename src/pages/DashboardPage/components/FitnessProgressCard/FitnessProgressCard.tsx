import React from 'react'
import { useTheme } from '../../../../contexts/ThemeContext'
import './FitnessProgressCard.scss'

interface FitnessProgressCardProps {
  moveProgress: number
  moveGoal: number
  exerciseProgress: number
  exerciseGoal: number
  standProgress: number
  standGoal: number
}

const FitnessProgressCard: React.FC<FitnessProgressCardProps> = ({
  moveProgress,
  moveGoal,
  exerciseProgress,
  exerciseGoal,
  standProgress,
  standGoal
}) => {
  const { theme } = useTheme()

  const calculatePercentage = (progress: number, goal: number): number => {
    return Math.min((progress / goal) * 100, 100)
  }

  const calculateStrokeDasharray = (percentage: number, circumference: number): string => {
    const progressLength = (percentage / 100) * circumference
    return `${progressLength} ${circumference}`
  }

  // 环形参数
  const ringData = [
    {
      id: 'move',
      progress: moveProgress,
      goal: moveGoal,
      color: '#FF6B35',
      radius: 85,
      strokeWidth: 12,
      label: '活动',
      unit: '卡路里'
    },
    {
      id: 'exercise',
      progress: exerciseProgress,
      goal: exerciseGoal,
      color: '#4CAF50',
      radius: 65,
      strokeWidth: 12,
      label: '锻炼',
      unit: '分钟'
    },
    {
      id: 'stand',
      progress: standProgress,
      goal: standGoal,
      color: '#2196F3',
      radius: 45,
      strokeWidth: 12,
      label: '站立',
      unit: '小时'
    }
  ]

  const svgSize = 200
  const center = svgSize / 2

  return (
    <div className={`fitness-progress-card theme-${theme}`}>
      {/* 圆环区域 - 占50% */}
      <div className="fitness-progress-card__rings">
        <div className="fitness-progress-card__rings-container">
          <svg
            width={svgSize}
            height={svgSize}
            viewBox={`0 0 ${svgSize} ${svgSize}`}
            className="fitness-progress-card__svg"
          >
            {ringData.map((ring) => {
              const circumference = 2 * Math.PI * ring.radius
              const percentage = calculatePercentage(ring.progress, ring.goal)
              const strokeDasharray = calculateStrokeDasharray(percentage, circumference)
              const isCompleted = ring.progress >= ring.goal

              return (
                <g key={ring.id}>
                  {/* 背景环 */}
                  <circle
                    cx={center}
                    cy={center}
                    r={ring.radius}
                    fill="none"
                    stroke="var(--ring-background, #E5E5E5)"
                    strokeWidth={ring.strokeWidth}
                    opacity={0.2}
                  />
                  
                  {/* 进度环 */}
                  <circle
                    cx={center}
                    cy={center}
                    r={ring.radius}
                    fill="none"
                    stroke={ring.color}
                    strokeWidth={ring.strokeWidth}
                    strokeDasharray={strokeDasharray}
                    strokeDashoffset={0}
                    strokeLinecap="round"
                    transform={`rotate(-90 ${center} ${center})`}
                    className={`fitness-progress-card__progress-ring ${isCompleted ? 'completed' : ''}`}
                  />
                </g>
              )
            })}
          </svg>
          
          {/* 中心数据显示 */}
          <div className="fitness-progress-card__center-data">
            <div className="fitness-progress-card__main-stat">
              <span className="value">{moveProgress}</span>
              <span className="unit">卡路里</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* 统计信息区域 - 占50% */}
      <div className="fitness-progress-card__stats">
        <div className="fitness-progress-card__stats-list">
          {ringData.map((ring) => {
            const percentage = calculatePercentage(ring.progress, ring.goal)
            const isCompleted = ring.progress >= ring.goal
            
            return (
              <div key={ring.id} className="fitness-progress-card__stat-item">
                <div 
                  className="fitness-progress-card__stat-color"
                  style={{ backgroundColor: ring.color }}
                />
                <div className="fitness-progress-card__stat-info">
                  <div className="fitness-progress-card__stat-label">
                    {ring.label}
                    {isCompleted && <span className="completed-icon">✓</span>}
                  </div>
                  <div className="fitness-progress-card__stat-progress">
                    <span className="current">{ring.progress}</span>
                    <span className="separator">/</span>
                    <span className="goal">{ring.goal}</span>
                    <span className="unit">{ring.unit}</span>
                    <span className="percentage">({percentage.toFixed(0)}%)</span>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default FitnessProgressCard 