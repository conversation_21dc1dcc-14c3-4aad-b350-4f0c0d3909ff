.fitness-progress-card {
  background: var(--bg-surface, #ffffff);
  border-radius: 16px;
  padding: 20px;
  margin: 0 auto 20px auto;
  max-width: min(92vw, 450px);
  width: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  // 主要布局：左右结构
  display: flex;
  flex-direction: row;
  align-items: stretch;
  gap: 20px;
  min-height: 280px;

  // 暗色主题
  &.theme-dark {
    background: var(--bg-surface);
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

    --ring-background: rgba(255, 255, 255, 0.1);
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --border-light: rgba(255, 255, 255, 0.1);
  }

  // 浅色主题变量
  &.theme-light {
    --ring-background: #E5E5E5;
    --text-primary: #1a1a1a;
    --text-secondary: #6b7280;
    --border-light: #e5e7eb;
  }

  // 圆环区域 - 占50%
  &__rings {
    flex: 0 0 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    
    &-container {
      position: relative;
      width: 200px;
      height: 200px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .fitness-progress-card__svg {
      width: 100%;
      height: 100%;
      max-width: 200px;
      max-height: 200px;
    }

    .fitness-progress-card__progress-ring {
      transition: stroke-dasharray 1s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  // 中心数据显示
  &__center-data {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: none;
  }

  &__main-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .value {
      font-size: 26px; // 缩小字体，更适合iOS显示
      font-weight: 700;
      color: var(--text-primary);
      line-height: 1;
      margin-bottom: 2px; // 轻微调整与单位的间距
    }
    
    .unit {
      font-size: 11px;
      color: var(--text-secondary);
      font-weight: 500;
      line-height: 1;
    }
  }

  // 统计信息区域 - 占50%
  &__stats {
    flex: 0 0 50%;
    display: flex;
    flex-direction: column;
    padding-left: 20px;
    padding-right: 16px; // 增加右侧内边距，避免与卡片边缘紧贴
    border-left: 1px solid var(--border-light);
    
    &-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      gap: 16px;
    }
  }

  &__stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 12px;
    background: var(--bg-hover, rgba(248, 250, 252, 0.5));
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  &__stat-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  &__stat-info {
    flex: 1;
    min-width: 0;
  }

  &__stat-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    gap: 6px;
    
    .completed-icon {
      color: #22c55e;
      font-size: 12px;
    }
  }

  &__stat-progress {
    font-size: 13px;
    color: var(--text-secondary);
    
    .current {
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .separator {
      margin: 0 2px;
    }
    
    .goal {
      font-weight: 500;
    }
    
    .unit {
      margin-left: 2px;
    }
    
    .percentage {
      margin-left: 6px;
      font-weight: 500;
      color: var(--text-secondary);
    }
  }
}

// 响应式设计：iPad/平板端
@media (max-width: 768px) {
  .fitness-progress-card {
    max-width: min(95vw, 400px);
    min-height: 320px;
    gap: 16px;
    padding: 16px;
    
    &__rings {
      &-container {
        width: 180px;
        height: 180px;
      }
    }

    &__main-stat {
      .value {
        font-size: 24px; // 缩小平板端字体，更好的居中效果
        margin-bottom: 2px;
      }
      
      .unit {
        font-size: 10px;
        line-height: 1;
      }
    }

    &__stats {
      padding-left: 16px;
      padding-right: 12px;
      
      &-list {
        gap: 12px;
      }
    }

    &__stat-item {
      padding: 10px;
      gap: 10px;
    }

    &__stat-color {
      width: 10px;
      height: 10px;
    }

    &__stat-label {
      font-size: 13px;
    }

    &__stat-progress {
      font-size: 12px;
    }
  }
}

// 响应式设计：手机端 - 保持左右布局，调整尺寸适配小屏
@media (max-width: 480px) {
  .fitness-progress-card {
    // 保持左右布局，不改为 column
    max-width: min(96vw, 350px);
    padding: 12px;
    gap: 12px;
    min-height: 200px;
    
    // 圆环区域 - 左侧50%
    &__rings {
      flex: 0 0 50%;
      
      &-container {
        width: 120px; // 适配小屏的圆环尺寸
        height: 120px;
      }
    }

    &__main-stat {
      .value {
        font-size: 18px; // 进一步缩小，确保在小屏幕完美居中
        margin-bottom: 1px; // 调整与单位的间距
      }
      
      .unit {
        font-size: 9px;
        line-height: 1;
      }
    }

    // 统计信息区域 - 右侧50%
    &__stats {
      flex: 0 0 50%;
      padding-left: 12px;
      padding-right: 8px;
      border-left: 1px solid var(--border-light); // 保持左边框分隔
      
      &-list {
        gap: 8px; // 紧凑间距
      }
    }

    &__stat-item {
      padding: 8px; // 紧凑内边距
    }

    &__stat-color {
      width: 8px;
      height: 8px;
    }

    &__stat-label {
      font-size: 12px;
    }

    &__stat-progress {
      font-size: 10px;
    }
  }
} 