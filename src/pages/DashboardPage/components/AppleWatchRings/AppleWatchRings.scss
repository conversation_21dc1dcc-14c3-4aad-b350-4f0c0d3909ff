.apple-watch-rings {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  // 移除独立背景，融入父容器
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  
  &__container {
    position: relative;
    flex-shrink: 0;
  }
  
  &__svg {
    display: block;
    transform: rotate(0deg);
  }
  
  &__progress-ring {
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    // 去除复杂的动画，只保留简单的进度显示
    
    &.completed {
      // 去除闪烁和光晕效果，只保留简单的完成状态
      opacity: 1;
    }
  }
  
  &__center-data {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: none;
  }
  
  &__main-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .value {
      font-size: 24px;
      font-weight: bold;
      color: var(--text-primary);
      line-height: 1;
    }
    
    .unit {
      font-size: 12px;
      color: var(--text-secondary);
      margin-top: 2px;
    }
  }
  
  &__details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  &__detail-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateX(4px);
      opacity: 0.8;
    }
  }
  
  &__detail-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
  }
  
  &__detail-info {
    flex: 1;
    min-width: 0;
  }
  
  &__detail-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    gap: 6px;
    
    .completed-icon {
      font-size: 12px;
      color: var(--color-primary, #4f46e5);
      // 去除弹跳动画
    }
  }
  
  &__detail-progress {
    font-size: 12px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 2px;
    
    .current {
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .separator {
      margin: 0 2px;
    }
    
    .goal {
      color: var(--text-secondary);
    }
    
    .unit {
      margin-left: 4px;
    }
    
    .percentage {
      margin-left: 8px;
      font-weight: 500;
      color: var(--color-primary, #4f46e5);
    }
  }
}

// 简化的动画效果 - 去除复杂的光晕和呼吸效果
@keyframes ringProgress {
  from {
    stroke-dasharray: 0 1000;
  }
  to {
    stroke-dasharray: var(--progress-length) var(--circumference);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .apple-watch-rings {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    
    &__container {
      align-self: center;
    }
    
    &__details {
      width: 100%;
    }
    
    &__detail-item {
      padding: 6px 0;
    }
  }
}

@media (max-width: 480px) {
  .apple-watch-rings {
    &__container svg {
      width: 160px;
      height: 160px;
    }
    
    &__main-stat .value {
      font-size: 20px;
    }
    
    &__detail-label {
      font-size: 13px;
    }
    
    &__detail-progress {
      font-size: 11px;
    }
  }
} 