import React from 'react'
import { useTheme } from '../../../../contexts/ThemeContext'
import './UserRecommendationCard.scss'

interface User {
  id: string
  name: string
  avatar: string
  dynamicCount: number  // 替换为动态数量
  isFollowing: boolean
}

interface UserRecommendationCardProps {
  users?: User[]
  onFollow?: (userId: string) => void
  onViewAll?: () => void
}

const UserRecommendationCard: React.FC<UserRecommendationCardProps> = ({
  users = [],
  onFollow = () => {},
  onViewAll = () => {}
}) => {
  const { theme } = useTheme()

  // 默认用户数据
  const defaultUsers: User[] = [
    {
      id: '1',
      name: '<PERSON>',
      avatar: 'J<PERSON>',
      dynamicCount: 288,
      isFollowing: false
    },
    {
      id: '2',
      name: 'Hammo23',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      dynamicCount: 156,
      isFollowing: false
    },
    {
      id: '3',
      name: '<PERSON>',
      avatar: 'AC',
      dynamicCount: 92,
      isFollowing: false
    },
    {
      id: '4',
      name: '<PERSON> Kim',
      avatar: 'SK',
      dynamicCount: 74,
      isFollowing: false
    },
    {
      id: '5',
      name: 'Mike Johnson',
      avatar: 'MJ',
      dynamicCount: 123,
      isFollowing: false
    }
  ]

  const displayUsers = users.length > 0 ? users : defaultUsers

  const handleFollow = (userId: string) => {
    onFollow(userId)
  }

  const renderAvatar = (user: User) => {
    if (user.avatar.startsWith('http')) {
      return (
        <img 
          src={user.avatar} 
          alt={user.name}
          className="user-avatar__image"
        />
      )
    } else {
      return (
        <div className="user-avatar__initials">
          {user.avatar}
        </div>
      )
    }
  }

  return (
    <div className={`user-recommendation-card theme-${theme}`}>
      <div className="user-recommendation-card__header">
        <h3 className="section-title">用户推荐</h3>
        <button className="view-all-btn" onClick={onViewAll}>
          查看全部
        </button>
      </div>

      <div className="user-recommendation-card__content">
        <div className="users-scroll-container">
          <div className="users-list">
            {displayUsers.map((user) => (
              <div key={user.id} className="user-item">
                <button className="close-btn" aria-label="关闭">
                  ×
                </button>
                
                <div className="user-avatar">
                  {renderAvatar(user)}
                </div>
                
                <div className="user-info">
                  <h4 className="user-name">{user.name}</h4>
                  <div className="user-stats">
                    <span className="dynamic-count">
                      {user.dynamicCount} 动态
                    </span>
                  </div>
                </div>
                
                <button 
                  className="follow-btn"
                  onClick={() => handleFollow(user.id)}
                >
                  关注
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserRecommendationCard 