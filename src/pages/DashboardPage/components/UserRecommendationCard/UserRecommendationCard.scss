.user-recommendation-card {
  background: var(--bg-surface, #ffffff);
  border-radius: 16px;
  padding: 20px;
  margin: 0 auto 20px auto;
  max-width: min(92vw, 450px);
  width: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: visible; // 确保悬浮效果不被裁剪

  // 暗色主题
  &.theme-dark {
    background: var(--bg-surface);
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: var(--text-primary, #1f2937);
      margin: 0;
    }

    .view-all-btn {
      background: none;
      border: none;
      color: var(--text-secondary, #6b7280);
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 6px;
      transition: all 0.2s ease;

      &:hover {
        background: var(--bg-hover, #f3f4f6);
        color: var(--text-primary);
      }
    }
  }

  &__content {
    .users-scroll-container {
      overflow-x: auto;
      overflow-y: visible; // 允许垂直方向的溢出，避免裁剪悬浮卡片
      -webkit-overflow-scrolling: touch;
      scroll-behavior: smooth;
      
      // 隐藏滚动条但保持功能
      scrollbar-width: none;
      -ms-overflow-style: none;
      
      &::-webkit-scrollbar {
        display: none;
      }
    }
    
    .users-list {
      display: flex;
      gap: 16px;
      padding: 8px 0 16px 0; // 增加上下内边距，给悬浮效果留出空间

      .user-item {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px 16px;
        background: var(--bg-hover, #f8fafc);
        border-radius: 16px;
        border: 1px solid var(--border-color, #e5e7eb);
        transition: all 0.3s ease;
        
        // 大卡片设计：宽屏显示2个，窄屏自适应
        min-width: 180px; // 显著增大最小宽度
        flex: 0 0 calc(50% - 8px); // 每行2个卡片，减去gap的一半
        max-width: 200px; // 限制最大宽度
        min-height: 220px; // 增加高度容纳更多内容

        &:hover {
          background: var(--bg-surface, #ffffff);
          border-color: var(--accent-300, #c7d2fe);
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .close-btn {
          position: absolute;
          top: 8px;
          right: 8px;
          background: none;
          border: none;
          color: var(--text-tertiary, #9ca3af);
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          width: 22px;
          height: 22px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: all 0.2s ease;

          &:hover {
            background: var(--bg-surface, #f3f4f6);
            color: var(--text-secondary);
          }
        }

        .user-avatar {
          width: 80px; // 显著增大头像尺寸
          height: 80px;
          border-radius: 50%;
          overflow: hidden;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          margin-bottom: 12px;
          border: 3px solid var(--border-color, #e5e7eb);

          &__image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          &__initials {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px; // 增大字体
            font-weight: bold;
            color: #ffffff;
            background: linear-gradient(135deg, #e879f9 0%, #a855f7 100%);
          }
        }

        // 为不同用户生成不同颜色
        &:nth-child(1) .user-avatar__initials {
          background: linear-gradient(135deg, #f472b6 0%, #ec4899 100%);
        }
        
        &:nth-child(2) .user-avatar__initials {
          background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
        }
        
        &:nth-child(3) .user-avatar__initials {
          background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
        }
        
        &:nth-child(4) .user-avatar__initials {
          background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
        }
        
        &:nth-child(5) .user-avatar__initials {
          background: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 100%);
        }

        .user-info {
          text-align: center;
          margin-bottom: 16px;
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .user-name {
            font-size: 16px; // 增大姓名字体
            font-weight: 600;
            color: var(--text-primary, #1f2937);
            margin: 0 0 8px 0;
            line-height: 1.2;
          }

          .user-stats {
            display: flex;
            justify-content: center;
            margin-top: 4px;

            .dynamic-count {
              font-size: 13px;
              color: rgba(var(--text-primary-rgb, 31, 41, 55), 0.6); // 比用户名颜色浅一些
              font-weight: 400;
              // 去除所有背景、边框和padding
            }
          }
        }

        .follow-btn {
          background: var(--accent-600, #2563eb);
          color: #ffffff;
          border: none;
          padding: 10px 20px; // 增大按钮
          border-radius: 20px;
          font-size: 14px; // 增大按钮字体
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
          flex-shrink: 0;
          width: calc(100% - 32px);
          max-width: 140px;

          &:hover {
            background: var(--accent-700, #1d4ed8);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(37, 99, 235, 0.4);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }
}

// 响应式设计：平板端 (768px - 1024px)
@media (max-width: 1024px) {
  .user-recommendation-card {
    &__content {
      .users-list {
        .user-item {
          // 在平板上仍保持2个卡片布局，但稍微调小
          min-width: 160px;
          max-width: 180px;
          min-height: 200px;
          padding: 18px 14px;

          .user-avatar {
            width: 70px;
            height: 70px;

            &__initials {
              font-size: 24px;
            }
          }

          .user-info {
            .user-name {
              font-size: 15px;
            }

            .user-stats {
              .dynamic-count {
                font-size: 12px;
                color: rgba(var(--text-primary-rgb, 31, 41, 55), 0.6);
              }
            }
          }

          .follow-btn {
            padding: 8px 16px;
            font-size: 13px;
          }
        }
      }
    }
  }
}

// 响应式设计：手机端 (≤768px)
@media (max-width: 768px) {
  .user-recommendation-card {
    padding: 16px;
    max-width: min(95vw, 400px);

    &__header {
      margin-bottom: 16px;

      .section-title {
        font-size: 16px;
      }

      .view-all-btn {
        font-size: 13px;
      }
    }

    &__content {
      .users-list {
        gap: 12px;

        .user-item {
          // 手机端改为每行显示1个大卡片，或并排显示2个小卡片
          flex: 0 0 calc(50% - 6px); // 手机上依然2个卡片
          min-width: 140px;
          max-width: 160px;
          min-height: 180px;
          padding: 16px 12px;

          .user-avatar {
            width: 60px;
            height: 60px;

            &__initials {
              font-size: 20px;
            }
          }

          .user-info {
            margin-bottom: 12px;

            .user-name {
              font-size: 14px;
            }

            .user-stats {
              .dynamic-count {
                font-size: 11px;
                color: rgba(var(--text-primary-rgb, 31, 41, 55), 0.6);
              }
            }
          }

          .follow-btn {
            padding: 6px 14px;
            font-size: 12px;
          }
        }
      }
    }
  }
}

// 小手机端 (≤480px)
@media (max-width: 480px) {
  .user-recommendation-card {
    padding: 12px;
    max-width: min(96vw, 350px);

    &__content {
      .users-list {
        gap: 10px;

        .user-item {
          // 小手机上显示2个卡片，垂直布局但更紧凑
          flex: 0 0 calc(50% - 5px);
          min-width: 120px;
          max-width: 140px;
          min-height: 140px;
          padding: 12px 8px;

          // 保持垂直布局，但更紧凑
          flex-direction: column;
          text-align: center;
          align-items: center;

          .close-btn {
            top: 6px;
            right: 6px;
          }

          .user-avatar {
            width: 50px;
            height: 50px;
            margin-bottom: 8px;

            &__initials {
              font-size: 16px;
            }
          }

          .user-info {
            flex: 1;
            margin-bottom: 8px;
            text-align: center;

            .user-name {
              font-size: 13px;
              margin-bottom: 4px;
            }

            .user-stats {
              justify-content: center;

              .dynamic-count {
                font-size: 11px;
              }
            }
          }

          .follow-btn {
            width: calc(100% - 16px);
            padding: 6px 10px;
            font-size: 11px;
            max-width: 100px;
          }
        }
      }
    }
  }
}

// 暗色主题特定样式
.user-recommendation-card.theme-dark {
  .user-recommendation-card__header {
    .section-title {
      color: var(--text-primary);
    }

    .view-all-btn {
      color: var(--text-secondary);

      &:hover {
        background: var(--bg-hover);
        color: var(--text-primary);
      }
    }
  }

  .users-list {
    .user-item {
      background: var(--bg-hover);
      border-color: var(--border-color);

      &:hover {
        background: var(--bg-surface);
        border-color: var(--accent-500);
      }

      .close-btn {
        color: var(--text-tertiary);

        &:hover {
          background: var(--bg-surface);
          color: var(--text-secondary);
        }
      }

      .user-avatar {
        border-color: var(--border-color);
      }

      .user-info {
        .user-name {
          color: var(--text-primary);
        }

        .user-stats {
          .dynamic-count {
            color: rgba(248, 250, 252, 0.6); // 暗色主题下的浅色文字
          }
        }
      }

      .follow-btn {
        background: var(--accent-600);

        &:hover {
          background: var(--accent-500);
        }
      }
    }
  }
} 