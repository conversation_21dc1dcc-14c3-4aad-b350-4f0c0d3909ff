import React, { useState, useEffect } from 'react'
import { useTheme } from '../../../../contexts/ThemeContext'
import type { DashboardData, DashboardLoadingState, DashboardError } from '../../../../types/dashboard'
import mockDataGenerator from '../../../../utils/mockDataGenerator'
import ExerciseRecordCard from '../ExerciseRecordCard'
import NutritionRecordCard from '../NutritionRecordCard'
import './DataDashboardSection.scss'

interface DataDashboardSectionProps {
  className?: string
  refreshInterval?: number // 自动刷新间隔(毫秒)
  onDataChange?: (data: DashboardData) => void
}

const DataDashboardSection: React.FC<DataDashboardSectionProps> = ({
  className = '',
  refreshInterval = 300000, // 默认5分钟刷新
  onDataChange
}) => {
  const { theme } = useTheme()
  
  // 状态管理
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState<DashboardLoadingState>({
    exercise: true,
    nutrition: true,
    weight: true,
    water: true,
    steps: true,
    overall: true
  })
  const [error, setError] = useState<DashboardError | null>(null)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  // 数据加载函数
  const loadData = async (showLoading = true) => {
    try {
      if (showLoading) {
        setLoading({
          exercise: true,
          nutrition: true,
          weight: true,
          water: true,
          steps: true,
          overall: true
        })
      }
      setError(null)

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 生成模拟数据
      const newData = mockDataGenerator.generateFullDashboard()
      setData(newData)
      setLastRefresh(new Date())
      
      // 通知父组件数据变化
      onDataChange?.(newData)
      
      // 逐步更新加载状态，模拟真实API调用
      setTimeout(() => setLoading(prev => ({ ...prev, exercise: false })), 100)
      setTimeout(() => setLoading(prev => ({ ...prev, nutrition: false })), 200)
      setTimeout(() => setLoading(prev => ({ ...prev, weight: false })), 300)
      setTimeout(() => setLoading(prev => ({ ...prev, water: false })), 400)
      setTimeout(() => setLoading(prev => ({ ...prev, steps: false, overall: false })), 500)
      
    } catch (err) {
      const error: DashboardError = {
        type: 'network',
        message: err instanceof Error ? err.message : '数据加载失败',
        timestamp: new Date()
      }
      setError(error)
      setLoading({
        exercise: false,
        nutrition: false,
        weight: false,
        water: false,
        steps: false,
        overall: false
      })
    }
  }

  // 刷新数据
  const handleRefresh = () => {
    loadData(true)
  }

  // 重试加载
  const handleRetry = () => {
    loadData(true)
  }

  // 初始化数据加载
  useEffect(() => {
    loadData()
  }, [])

  // 自动刷新
  useEffect(() => {
    if (refreshInterval > 0) {
      const interval = setInterval(() => {
        loadData(false) // 静默刷新
      }, refreshInterval)
      
      return () => clearInterval(interval)
    }
  }, [refreshInterval])

  // 格式化最后更新时间
  const formatLastUpdate = (date: Date) => {
    const now = new Date()
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffMinutes < 1) return '刚刚更新'
    if (diffMinutes < 60) return `${diffMinutes}分钟前`
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}小时前`
    return date.toLocaleDateString()
  }

  return (
          <div className={`data-dashboard-section theme-${theme} ${className}`}>
      {/* 标题和刷新控制 */}
      <div className="data-dashboard-section__header">
        <h3 className="section-title">
          📊 数据面板
          {loading.overall && <span className="loading-indicator">⏳</span>}
        </h3>
        <div className="section-controls">
          <span className="last-update">
            {data && formatLastUpdate(lastRefresh)}
          </span>
          <button 
            className="refresh-button"
            onClick={handleRefresh}
            disabled={loading.overall}
            title="刷新数据"
          >
            🔄
          </button>
        </div>
      </div>

      {/* 错误状态 */}
      {error && (
        <div className="data-dashboard-section__error">
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            <span className="error-text">{error.message}</span>
            <button className="retry-button" onClick={handleRetry}>
              重试
            </button>
          </div>
        </div>
      )}

      {/* 数据网格 - 双列布局 */}
      <div className="data-dashboard-section__grid">
        {/* 左列 */}
        <div className="data-dashboard-section__column data-dashboard-section__column--left">
          {/* 运动记录卡片 */}
          {data ? (
            <ExerciseRecordCard
              data={data.exerciseRecord}
              loading={loading.exercise}
              error={error?.component === 'exercise' ? error : null}
              onRetry={handleRetry}
              onRefresh={handleRefresh}
            />
          ) : (
            <ExerciseRecordCard
              data={{
                todayDuration: 0,
                goalDuration: 60,
                exerciseType: '加载中...',
                exerciseCount: 0
              }}
              loading={loading.exercise}
              error={error?.component === 'exercise' ? error : null}
              onRetry={handleRetry}
              onRefresh={handleRefresh}
            />
          )}

          {/* 体重趋势卡片占位 */}
          <div className="data-card-placeholder weight-card">
            <div className="placeholder-header">
              <span className="placeholder-icon">⚖️</span>
              <span className="placeholder-title">体重趋势</span>
              {loading.weight && <span className="loading-dot">⏳</span>}
            </div>
            <div className="placeholder-content">
              {data ? (
                <div className="preview-data">
                  <p>当前: {data.weightTrend.currentWeight}kg</p>
                  <p>趋势: {data.weightTrend.trend === 'up' ? '📈' : data.weightTrend.trend === 'down' ? '📉' : '➡️'}</p>
                  <p>BMI: {data.weightTrend.bmi}</p>
                </div>
              ) : (
                <div className="loading-skeleton">
                  <div className="skeleton-line"></div>
                  <div className="skeleton-line"></div>
                  <div className="skeleton-line"></div>
                </div>
              )}
            </div>
          </div>

          {/* 步数统计卡片占位 */}
          <div className="data-card-placeholder steps-card">
            <div className="placeholder-header">
              <span className="placeholder-icon">👣</span>
              <span className="placeholder-title">步数统计</span>
              {loading.steps && <span className="loading-dot">⏳</span>}
            </div>
            <div className="placeholder-content">
              {data ? (
                <div className="preview-data">
                  <p>今日步数: {data.stepsCount.steps.toLocaleString()}</p>
                  <p>目标: {data.stepsCount.goal.toLocaleString()}</p>
                  <p>距离: {data.stepsCount.distance}km</p>
                </div>
              ) : (
                <div className="loading-skeleton">
                  <div className="skeleton-line"></div>
                  <div className="skeleton-line"></div>
                  <div className="skeleton-line"></div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 右列 */}
        <div className="data-dashboard-section__column data-dashboard-section__column--right">
          {/* 营养记录卡片 */}
          {data ? (
            <NutritionRecordCard
              data={data.nutritionRecord}
              loading={loading.nutrition}
              error={error?.component === 'nutrition' ? error : null}
              onRetry={handleRetry}
              onRefresh={handleRefresh}
            />
          ) : (
            <NutritionRecordCard
              data={{
                calories: { consumed: 0, goal: 2000 },
                macros: {
                  protein: { consumed: 0, goal: 150, percentage: 20 },
                  carbs: { consumed: 0, goal: 250, percentage: 50 },
                  fat: { consumed: 0, goal: 67, percentage: 30 }
                },
                mealCount: 0
              }}
              loading={loading.nutrition}
              error={error?.component === 'nutrition' ? error : null}
              onRetry={handleRetry}
              onRefresh={handleRefresh}
            />
          )}

          {/* 水分摄入卡片占位 */}
          <div className="data-card-placeholder water-card">
            <div className="placeholder-header">
              <span className="placeholder-icon">💧</span>
              <span className="placeholder-title">水分摄入</span>
              {loading.water && <span className="loading-dot">⏳</span>}
            </div>
            <div className="placeholder-content">
              {data ? (
                <div className="preview-data">
                  <p>已摄入: {data.waterIntake.consumed}{data.waterIntake.unit}</p>
                  <p>目标: {data.waterIntake.goal}{data.waterIntake.unit}</p>
                  <p>次数: {data.waterIntake.drinkCount}次</p>
                </div>
              ) : (
                <div className="loading-skeleton">
                  <div className="skeleton-line"></div>
                  <div className="skeleton-line"></div>
                  <div className="skeleton-line"></div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DataDashboardSection 