// DataDashboardSection styling

.data-dashboard-section {
  margin-bottom: 30px;
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  &__error {
    background-color: rgba(255, 0, 0, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    
    .error-message {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .retry-button {
      margin-left: auto;
      padding: 4px 12px;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  
  &__grid {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
  }
  
  &__column {
    flex: 1;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .section-controls {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .refresh-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.1rem;
    padding: 4px;
    border-radius: 50%;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
  
  .last-update {
    font-size: 0.85rem;
    opacity: 0.7;
  }
  
  .data-card-placeholder {
    background-color: #f9f9f9;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    
    &.weight-card,
    &.steps-card {
      min-height: 180px;
    }
  }
  
  .placeholder-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
  }
  
  .placeholder-title {
    font-weight: 600;
  }
  
  .preview-data {
    p {
      margin: 8px 0;
    }
  }
  
  .loading-skeleton {
    .skeleton-line {
      height: 16px;
      background-color: #eee;
      border-radius: 4px;
      margin-bottom: 8px;
      
      &:nth-child(2) {
        width: 70%;
      }
      
      &:nth-child(3) {
        width: 50%;
      }
    }
  }
  
  // Dark theme support
  &.theme-dark {
    .data-card-placeholder {
      background-color: #2a2a2a;
    }
    
    .loading-skeleton .skeleton-line {
      background-color: #3a3a3a;
    }
  }
} 