.exercise-record-card {
  --ring-background: #e5e7eb;
  --color-primary: #3b82f6;
  --color-success: #10b981;
  --color-danger: #ef4444;
  --color-warning: #f59e0b;
  
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--color-primary);
  }

  // 主题适配
  &.theme-dark {
    --ring-background: #374151;
    background: var(--bg-card);
    border-color: var(--border-color);
    
    .exercise-record-card__icon {
      filter: brightness(1.2);
    }
  }

  // 加载状态
  &--loading {
    pointer-events: none;
    
    .loading-skeleton {
      background: linear-gradient(90deg, 
        var(--bg-surface) 25%, 
        var(--bg-hover) 50%, 
        var(--bg-surface) 75%
      );
      background-size: 200% 100%;
      animation: loading-shimmer 1.5s infinite;
      border-radius: 4px;
      
      &--circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
      }
      
      &--line {
        height: 16px;
        margin-bottom: 8px;
        
        &:last-child {
          width: 70%;
          margin-bottom: 0;
        }
      }
    }
  }

  // 错误状态
  &--error {
    border-color: var(--color-danger);
    
    .exercise-record-card__error {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
      text-align: center;
      
      .error-icon {
        font-size: 24px;
        margin-bottom: 8px;
      }
      
      .error-message {
        color: var(--text-secondary);
        font-size: 14px;
        margin-bottom: 12px;
      }
      
      .retry-button {
        background: var(--color-danger);
        color: white;
        border: none;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &:hover {
          background: color-mix(in srgb, var(--color-danger) 90%, black);
          transform: translateY(-1px);
        }
      }
    }
  }

  // 卡片头部
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    
    .exercise-record-card__icon {
      font-size: 20px;
      margin-right: 8px;
    }
    
    .exercise-record-card__title {
      flex: 1;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .exercise-record-card__refresh {
      background: none;
      border: none;
      font-size: 14px;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;
      
      &:hover {
        background: var(--bg-hover);
        transform: rotate(180deg);
      }
    }
  }

  // 卡片内容
  &__content {
    display: flex;
    gap: 16px;
    align-items: flex-start;
    margin-bottom: 16px;
  }

  // 进度环区域
  &__progress-ring {
    position: relative;
    flex-shrink: 0;
    
    .progress-ring {
      display: block;
      
      &__background {
        opacity: 0.3;
      }
      
      &__progress {
        filter: drop-shadow(0 0 4px currentColor);
      }
    }
    
    &__text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      
      .progress-ring__percentage {
        font-size: 16px;
        font-weight: 700;
        color: var(--text-primary);
        line-height: 1;
      }
      
      .progress-ring__label {
        font-size: 10px;
        color: var(--text-secondary);
        margin-top: 2px;
      }
    }
    
    &__celebration {
      position: absolute;
      top: -5px;
      right: -5px;
      font-size: 16px;
      animation: celebration-bounce 1s ease-in-out infinite alternate;
    }
  }

  // 运动信息区域
  &__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  &__stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .stat-label {
      font-size: 12px;
      color: var(--text-secondary);
      font-weight: 500;
    }
    
    .stat-value {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
      
      &--highlight {
        color: var(--color-primary);
        background: color-mix(in srgb, var(--color-primary) 10%, transparent);
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
      }
    }
  }

  // 最近运动信息
  &__last-exercise {
    border-top: 1px solid var(--border-light);
    padding-top: 12px;
    margin-top: 4px;
    
    .last-exercise__header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .last-exercise__icon {
        font-size: 14px;
        margin-right: 6px;
      }
      
      .last-exercise__title {
        font-size: 12px;
        font-weight: 600;
        color: var(--text-secondary);
      }
    }
    
    .last-exercise__details {
      padding-left: 20px;
    }
    
    .last-exercise__name {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 4px;
    }
    
    .last-exercise__stats {
      display: flex;
      gap: 12px;
      font-size: 12px;
      color: var(--text-secondary);
      
      span {
        position: relative;
        
        &:not(:last-child)::after {
          content: '•';
          position: absolute;
          right: -7px;
          color: var(--border-color);
        }
      }
    }
  }

  // 动画效果
  @keyframes loading-shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes celebration-bounce {
    0% {
      transform: translateY(0) scale(1);
    }
    100% {
      transform: translateY(-3px) scale(1.1);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    &__content {
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
    
    &__progress-ring {
      margin-bottom: 8px;
    }
    
    &__info {
      width: 100%;
    }
    
    &__stat {
      justify-content: center;
      gap: 8px;
      
      .stat-label::after {
        content: ':';
      }
    }
  }

  // 无障碍支持
  @media (prefers-reduced-motion: reduce) {
    &,
    .progress-ring__progress,
    .exercise-record-card__refresh,
    .loading-skeleton {
      animation: none;
      transition: none;
    }
    
    &:hover {
      transform: none;
    }
  }
} 