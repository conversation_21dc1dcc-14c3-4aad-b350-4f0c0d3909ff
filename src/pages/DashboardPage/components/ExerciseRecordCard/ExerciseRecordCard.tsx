import React from 'react'
import { useTheme } from '../../../../contexts/ThemeContext'
import type { ExerciseRecord, BaseCardProps } from '../../../../types/dashboard'
import './ExerciseRecordCard.scss'

interface ExerciseRecordCardProps extends BaseCardProps {
  data: ExerciseRecord
}

const ExerciseRecordCard: React.FC<ExerciseRecordCardProps> = ({
  data,
  className = '',
  loading = false,
  error = null,
  onRetry,
  onRefresh
}) => {
  const { theme } = useTheme()

  // 计算完成百分比
  const progressPercentage = Math.min((data.todayDuration / data.goalDuration) * 100, 100)
  const isGoalReached = data.todayDuration >= data.goalDuration
  
  // 计算环形进度的路径
  const radius = 35
  const circumference = 2 * Math.PI * radius
  const strokeDashoffset = circumference - (progressPercentage / 100) * circumference

  // 格式化时间显示
  const formatDuration = (minutes: number): string => {
    if (minutes < 60) return `${minutes}分钟`
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`
  }

  // 格式化时间戳
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  if (loading) {
    return (
              <div className={`exercise-record-card theme-${theme} ${className} exercise-record-card--loading`}>
        <div className="exercise-record-card__header">
          <div className="exercise-record-card__icon">🏃‍♂️</div>
          <div className="exercise-record-card__title">运动记录</div>
        </div>
        <div className="exercise-record-card__content">
          <div className="exercise-record-card__progress-ring">
            <div className="loading-skeleton loading-skeleton--circle"></div>
          </div>
          <div className="exercise-record-card__info">
            <div className="loading-skeleton loading-skeleton--line"></div>
            <div className="loading-skeleton loading-skeleton--line"></div>
            <div className="loading-skeleton loading-skeleton--line"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
              <div className={`exercise-record-card theme-${theme} ${className} exercise-record-card--error`}>
        <div className="exercise-record-card__header">
          <div className="exercise-record-card__icon">🏃‍♂️</div>
          <div className="exercise-record-card__title">运动记录</div>
        </div>
        <div className="exercise-record-card__error">
          <div className="error-icon">⚠️</div>
          <div className="error-message">{error.message}</div>
          {onRetry && (
            <button className="retry-button" onClick={onRetry}>
              重试
            </button>
          )}
        </div>
      </div>
    )
  }

  return (
          <div className={`exercise-record-card theme-${theme} ${className}`}>
      {/* 卡片头部 */}
      <div className="exercise-record-card__header">
        <div className="exercise-record-card__icon">🏃‍♂️</div>
        <div className="exercise-record-card__title">运动记录</div>
        {onRefresh && (
          <button 
            className="exercise-record-card__refresh"
            onClick={onRefresh}
            title="刷新数据"
          >
            🔄
          </button>
        )}
      </div>

      {/* 卡片内容 */}
      <div className="exercise-record-card__content">
        {/* 进度环 */}
        <div className="exercise-record-card__progress-ring">
          <svg width="80" height="80" className="progress-ring">
            {/* 背景环 */}
            <circle
              cx="40"
              cy="40"
              r={radius}
              stroke="var(--ring-background)"
              strokeWidth="6"
              fill="none"
              className="progress-ring__background"
            />
            {/* 进度环 */}
            <circle
              cx="40"
              cy="40"
              r={radius}
              stroke={isGoalReached ? "var(--color-success)" : "var(--color-primary)"}
              strokeWidth="6"
              fill="none"
              strokeLinecap="round"
              strokeDasharray={circumference}
              strokeDashoffset={strokeDashoffset}
              className="progress-ring__progress"
              style={{
                transition: 'stroke-dashoffset 0.8s ease-in-out',
                transform: 'rotate(-90deg)',
                transformOrigin: '40px 40px'
              }}
            />
          </svg>
          {/* 中心文本 */}
          <div className="progress-ring__text">
            <div className="progress-ring__percentage">
              {Math.round(progressPercentage)}%
            </div>
            <div className="progress-ring__label">完成</div>
          </div>
          {/* 目标达成庆祝效果 */}
          {isGoalReached && (
            <div className="progress-ring__celebration">🎉</div>
          )}
        </div>

        {/* 运动信息 */}
        <div className="exercise-record-card__info">
          <div className="exercise-record-card__stat">
            <div className="stat-label">今日运动</div>
            <div className="stat-value">
              {formatDuration(data.todayDuration)}
            </div>
          </div>
          
          <div className="exercise-record-card__stat">
            <div className="stat-label">目标时长</div>
            <div className="stat-value">
              {formatDuration(data.goalDuration)}
            </div>
          </div>

          <div className="exercise-record-card__stat">
            <div className="stat-label">运动类型</div>
            <div className="stat-value stat-value--highlight">
              {data.exerciseType}
            </div>
          </div>

          <div className="exercise-record-card__stat">
            <div className="stat-label">运动次数</div>
            <div className="stat-value">
              {data.exerciseCount}次
            </div>
          </div>
        </div>
      </div>

      {/* 最近运动信息 */}
      {data.lastExercise && (
        <div className="exercise-record-card__last-exercise">
          <div className="last-exercise__header">
            <span className="last-exercise__icon">⏱️</span>
            <span className="last-exercise__title">最近运动</span>
          </div>
          <div className="last-exercise__details">
            <div className="last-exercise__name">
              {data.lastExercise.name}
            </div>
            <div className="last-exercise__stats">
              <span className="last-exercise__duration">
                {formatDuration(data.lastExercise.duration)}
              </span>
              <span className="last-exercise__calories">
                {data.lastExercise.calories}卡
              </span>
              <span className="last-exercise__time">
                {formatTime(data.lastExercise.timestamp)}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ExerciseRecordCard 