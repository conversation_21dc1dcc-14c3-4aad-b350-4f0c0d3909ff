import React from 'react'
import { useTheme } from '../../../../contexts/ThemeContext'
import type { NutritionRecord, BaseCardProps } from '../../../../types/dashboard'
import './NutritionRecordCard.scss'

interface NutritionRecordCardProps extends BaseCardProps {
  data: NutritionRecord
}

const NutritionRecordCard: React.FC<NutritionRecordCardProps> = ({
  data,
  className = '',
  loading = false,
  error = null,
  onRetry,
  onRefresh
}) => {
  const { theme } = useTheme()

  // 计算卡路里完成百分比
  const calorieProgressPercentage = Math.min((data.calories.consumed / data.calories.goal) * 100, 100)
  const isCalorieGoalReached = data.calories.consumed >= data.calories.goal
  
  // 计算环形进度的路径
  const radius = 35
  const circumference = 2 * Math.PI * radius
  const strokeDashoffset = circumference - (calorieProgressPercentage / 100) * circumference

  // 营养素颜色配置
  const macroColors = {
    protein: '#ef4444', // 红色 - 蛋白质
    carbs: '#3b82f6',   // 蓝色 - 碳水化合物
    fat: '#f59e0b'      // 黄色 - 脂肪
  }

  // 格式化时间戳
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  // 计算营养素进度百分比
  const getMacroProgress = (macro: 'protein' | 'carbs' | 'fat') => {
    return Math.min((data.macros[macro].consumed / data.macros[macro].goal) * 100, 100)
  }

  if (loading) {
    return (
              <div className={`nutrition-record-card theme-${theme} ${className} nutrition-record-card--loading`}>
        <div className="nutrition-record-card__header">
          <div className="nutrition-record-card__icon">🥗</div>
          <div className="nutrition-record-card__title">营养记录</div>
        </div>
        <div className="nutrition-record-card__content">
          <div className="nutrition-record-card__calorie-ring">
            <div className="loading-skeleton loading-skeleton--circle"></div>
          </div>
          <div className="nutrition-record-card__info">
            <div className="loading-skeleton loading-skeleton--line"></div>
            <div className="loading-skeleton loading-skeleton--line"></div>
            <div className="loading-skeleton loading-skeleton--line"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
              <div className={`nutrition-record-card theme-${theme} ${className} nutrition-record-card--error`}>
        <div className="nutrition-record-card__header">
          <div className="nutrition-record-card__icon">🥗</div>
          <div className="nutrition-record-card__title">营养记录</div>
        </div>
        <div className="nutrition-record-card__error">
          <div className="error-icon">⚠️</div>
          <div className="error-message">{error.message}</div>
          {onRetry && (
            <button className="retry-button" onClick={onRetry}>
              重试
            </button>
          )}
        </div>
      </div>
    )
  }

  return (
          <div className={`nutrition-record-card theme-${theme} ${className}`}>
      {/* 卡片头部 */}
      <div className="nutrition-record-card__header">
        <div className="nutrition-record-card__icon">🥗</div>
        <div className="nutrition-record-card__title">营养记录</div>
        {onRefresh && (
          <button 
            className="nutrition-record-card__refresh"
            onClick={onRefresh}
            title="刷新数据"
          >
            🔄
          </button>
        )}
      </div>

      {/* 卡片内容 */}
      <div className="nutrition-record-card__content">
        {/* 卡路里进度环 */}
        <div className="nutrition-record-card__calorie-ring">
          <svg width="80" height="80" className="calorie-ring">
            {/* 背景环 */}
            <circle
              cx="40"
              cy="40"
              r={radius}
              stroke="var(--ring-background)"
              strokeWidth="6"
              fill="none"
              className="calorie-ring__background"
            />
            {/* 进度环 */}
            <circle
              cx="40"
              cy="40"
              r={radius}
              stroke={isCalorieGoalReached ? "var(--color-success)" : "var(--color-primary)"}
              strokeWidth="6"
              fill="none"
              strokeLinecap="round"
              strokeDasharray={circumference}
              strokeDashoffset={strokeDashoffset}
              className="calorie-ring__progress"
              style={{
                transition: 'stroke-dashoffset 0.8s ease-in-out',
                transform: 'rotate(-90deg)',
                transformOrigin: '40px 40px'
              }}
            />
          </svg>
          {/* 中心文本 */}
          <div className="calorie-ring__text">
            <div className="calorie-ring__percentage">
              {Math.round(calorieProgressPercentage)}%
            </div>
            <div className="calorie-ring__label">卡路里</div>
          </div>
          {/* 目标达成庆祝效果 */}
          {isCalorieGoalReached && (
            <div className="calorie-ring__celebration">🎉</div>
          )}
        </div>

        {/* 营养信息 */}
        <div className="nutrition-record-card__info">
          <div className="nutrition-record-card__calories">
            <div className="calories-stat">
              <span className="calories-consumed">{data.calories.consumed}</span>
              <span className="calories-separator">/</span>
              <span className="calories-goal">{data.calories.goal}</span>
              <span className="calories-unit">卡</span>
            </div>
            <div className="calories-remaining">
              剩余 {Math.max(0, data.calories.goal - data.calories.consumed)} 卡
            </div>
          </div>

          <div className="nutrition-record-card__meals">
            <div className="meals-count">
              <span className="meals-icon">🍽️</span>
              <span className="meals-text">今日 {data.mealCount} 餐</span>
            </div>
          </div>
        </div>
      </div>

      {/* 营养素分布 */}
      <div className="nutrition-record-card__macros">
        <div className="macros-title">营养素分布</div>
        <div className="macros-grid">
          {/* 蛋白质 */}
          <div className="macro-item">
            <div className="macro-header">
              <span className="macro-name">蛋白质</span>
              <span className="macro-value">
                {data.macros.protein.consumed}g / {data.macros.protein.goal}g
              </span>
            </div>
            <div className="macro-progress">
              <div 
                className="macro-progress-bar"
                style={{
                  width: `${getMacroProgress('protein')}%`,
                  backgroundColor: macroColors.protein
                }}
              />
            </div>
            <div className="macro-percentage">
              {Math.round(getMacroProgress('protein'))}%
            </div>
          </div>

          {/* 碳水化合物 */}
          <div className="macro-item">
            <div className="macro-header">
              <span className="macro-name">碳水</span>
              <span className="macro-value">
                {data.macros.carbs.consumed}g / {data.macros.carbs.goal}g
              </span>
            </div>
            <div className="macro-progress">
              <div 
                className="macro-progress-bar"
                style={{
                  width: `${getMacroProgress('carbs')}%`,
                  backgroundColor: macroColors.carbs
                }}
              />
            </div>
            <div className="macro-percentage">
              {Math.round(getMacroProgress('carbs'))}%
            </div>
          </div>

          {/* 脂肪 */}
          <div className="macro-item">
            <div className="macro-header">
              <span className="macro-name">脂肪</span>
              <span className="macro-value">
                {data.macros.fat.consumed}g / {data.macros.fat.goal}g
              </span>
            </div>
            <div className="macro-progress">
              <div 
                className="macro-progress-bar"
                style={{
                  width: `${getMacroProgress('fat')}%`,
                  backgroundColor: macroColors.fat
                }}
              />
            </div>
            <div className="macro-percentage">
              {Math.round(getMacroProgress('fat'))}%
            </div>
          </div>
        </div>
      </div>

      {/* 最近用餐信息 */}
      {data.lastMeal && (
        <div className="nutrition-record-card__last-meal">
          <div className="last-meal__header">
            <span className="last-meal__icon">🍴</span>
            <span className="last-meal__title">最近用餐</span>
          </div>
          <div className="last-meal__details">
            <div className="last-meal__name">
              {data.lastMeal.name}
            </div>
            <div className="last-meal__stats">
              <span className="last-meal__calories">
                {data.lastMeal.calories}卡
              </span>
              <span className="last-meal__time">
                {formatTime(data.lastMeal.timestamp)}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default NutritionRecordCard 