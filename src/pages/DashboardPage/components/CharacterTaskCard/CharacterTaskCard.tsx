import React from 'react';
import { useTheme } from '../../../../contexts/ThemeContext';
import { ProgressBar } from 'pixel-retroui';
import manImage from '../../../../assets/man.png'
import energyIcon from '../../../../assets/plates/plate25.png'
import achievementIcon from '../../../../assets/achieve.png'

// 导入所有奖励等级plate图片
import plate25 from '../../../../assets/plates/plate25.png';
import plate20 from '../../../../assets/plates/plate20.png';
import plate15 from '../../../../assets/plates/plate15.png';
import plate10 from '../../../../assets/plates/plate10.png';
import plate5 from '../../../../assets/plates/plate5.png';
import plate2_5 from '../../../../assets/plates/plate2-5.png';

import './CharacterTaskCard.scss'

interface Task {
  id: string
  icon: string
  title: string
  description: string
  completed: boolean
  energyReward: number
}

interface CharacterTaskCardProps {
  tasks: Task[]
  energyValue: number
  achievementCount: number
}

const CharacterTaskCard: React.FC<CharacterTaskCardProps> = ({
  tasks = [],
  energyValue = 120,
  achievementCount = 8
}) => {
  const { theme } = useTheme()

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return '早上好!'
    if (hour < 18) return '下午好!'
    return '晚上好!'
  }

  const completedTasksCount = tasks.filter(task => task.completed).length
  const taskProgressPercentage = tasks.length > 0 ? Math.round((completedTasksCount / tasks.length) * 100) : 0

  // 按能量值降序排列任务
  const sortedTasks = [...tasks].sort((a, b) => b.energyReward - a.energyReward)

  // 奖励等级映射：根据能量值确定对应的plate图片和名称
  const getRewardLevel = (energyReward: number) => {
    if (energyReward >= 25) return { image: plate25, name: '传奇奖励' };
    if (energyReward >= 20) return { image: plate20, name: '史诗奖励' };
    if (energyReward >= 15) return { image: plate15, name: '稀有奖励' };
    if (energyReward >= 10) return { image: plate10, name: '优秀奖励' };
    if (energyReward >= 5) return { image: plate5, name: '良好奖励' };
    return { image: plate2_5, name: '基础奖励' }; // 默认奖励等级
  };

  return (
    <div className={`character-task-card theme-${theme}`}>
      {/* 顶部状态栏：统计信息移到右侧 */}
      <div className="character-task-card__status-bar">
        <div className="status-left">
          <span className="status-title">{getGreeting()}</span>
        </div>
        <div className="status-right">
          <div className="stat-item">
            <img src={energyIcon} alt="能量" className="stat-icon" />
            <span className="stat-value">{energyValue}</span>
          </div>
          <div className="stat-item">
            <img src={achievementIcon} alt="成就" className="stat-icon" />
            <span className="stat-value">{achievementCount}</span>
          </div>
        </div>
      </div>

      {/* 中间区域：人物 + 进度条 */}
      <div className="character-task-card__header">
        {/* 左侧：人物信息（移除统计信息） */}
        <div className="left-section">
          <div className="character-info">
            <p className="motivation">今天也要加油哦~</p>
            <div className="character-avatar">
              <img 
                src={manImage} 
                alt="Character"
                className="character-image"
              />
            </div>
          </div>
        </div>

        {/* 右侧：进度条独立区域 */}
        <div className="right-section">
          {/* 任务完成度进度条 - 使用pixel-retroui */}
          <div className="progress-section">
            <div className="progress-header">
              <span className="progress-title">任务完成度</span>
              <span className="progress-value">{completedTasksCount}/{tasks.length}</span>
            </div>
            <ProgressBar
              progress={taskProgressPercentage || 0}
              size="md"
              color="green"
              borderColor="black"
              className="pixel-progress-bar w-full"
            />
          </div>

          {/* 能量进度条 - 使用pixel-retroui */}
          <div className="progress-section">
            <div className="progress-header">
              <span className="progress-title">能量值</span>
              <span className="progress-value">{energyValue}/200</span>
            </div>
            <ProgressBar
              progress={Math.round((energyValue / 200) * 100) || 0}
              size="md"
              color="orange" 
              borderColor="black"
              className="pixel-progress-bar w-full"
            />
          </div>
        </div>
      </div>

      {/* 下方区域：任务列表 */}
      <div className="character-task-card__tasks">
        <div className="tasks-container">
          {/* 添加今日任务标题 */}
          <h3 className="tasks-title">今日任务</h3>
          
          {/* 水平滚动任务列表 */}
          <div className="tasks-scroll">
            {sortedTasks.map((task) => {
              const rewardLevel = getRewardLevel(task.energyReward);
              
              return (
                <div key={task.id} className={`task-card ${task.completed ? 'completed' : ''}`}>
                  {/* 重新设计的任务标题区域 */}
                  <div className="task-header">
                    <span className="task-icon">{task.icon}</span>
                    <h4 className="task-title">{task.title}</h4>
                  </div>
                  
                  <p className="task-description">{task.description}</p>
                  
                  {/* 突出的奖励显示区域 - 使用plate图片 */}
                  <div className="reward-display">
                    <img 
                      src={rewardLevel.image} 
                      alt={rewardLevel.name}
                      className="reward-plate"
                      title={`${rewardLevel.name} (+${task.energyReward} 能量)`}
                    />
                    <span className="reward-text">+{task.energyReward} 能量</span>
                  </div>
                  
                  <div className="task-footer">
                    <button className={`task-button ${task.completed ? 'completed' : 'start-button'}`}>
                      {task.completed ? '已完成' : '开始'}
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CharacterTaskCard 