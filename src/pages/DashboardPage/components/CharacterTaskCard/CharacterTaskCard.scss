.character-task-card {
  background: var(--bg-surface, #ffffff);
  border-radius: 16px;
  padding: 16px !important;
  margin: 0 auto 20px auto;
  max-width: min(92vw, 450px) !important; // 与WeeklyDatePicker保持一致
  width: 100%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden !important;

  // 确保所有子元素使用正确的盒模型
  *,
  *::before,
  *::after {
    box-sizing: border-box !important;
  }

  // 暗色主题
  &.theme-dark {
    background: var(--bg-surface);
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

    // 暗色主题下的retro进度条适配
    .progress-bar {
      background-color: #1a1a1a; // 更深的背景
      border-color: #000000; // 纯黑边框

      .progress-fill {
        &.tasks {
          background: #00cc00; // 暗色下稍暗的绿色
          box-shadow: 
            inset 0 1px 0 #44ff44,
            inset 0 -1px 0 #006600;
        }

        &.energy {
          background: #cc5500; // 暗色下稍暗的橙色
          box-shadow: 
            inset 0 1px 0 #ff7722,
            inset 0 -1px 0 #993300;
        }
      }
    }
  }

  // 顶部状态栏：统计信息右对齐
  &__status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0 12px 0;
    border-bottom: 1px solid var(--border-color, rgba(0,0,0,0.1));
    margin-bottom: 12px; // 减少到状态栏的距离

    .status-left {
      .status-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
      }
    }

    .status-right {
      display: flex;
      align-items: center;
      gap: 20px;

      .stat-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;

        .stat-icon {
          width: 20px;
          height: 20px;
          object-fit: contain;
        }

        .stat-value {
          font-size: 16px;
          font-weight: 600;
          color: var(--text-primary);
        }
      }
    }
  }

  // 中间区域：人物信息 + 进度条
  &__header {
    display: flex !important;
    flex-direction: row !important;
    align-items: flex-start !important; // 改回flex-start，手动控制对齐
    gap: 16px !important;
    margin-bottom: 20px;
    width: 100% !important;
    box-sizing: border-box !important;

    // 左侧：人物信息区域（移除统计信息）
    .left-section {
      flex: 0 0 45% !important; // 略微增加比例
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      box-sizing: border-box !important;

      .character-info {
        text-align: center;
        padding: 8px !important;
        border-radius: 12px;
        width: 100%;

        .motivation {
          font-size: 14px;
          color: var(--text-secondary);
          margin: 0 0 12px 0;
        }

        .character-avatar {
          display: flex;
          justify-content: center;
          align-items: center;

          .character-image {
            width: 160px;
            height: 160px;
            object-fit: cover;
            border-radius: 50%;
          }
        }
      }
    }

    // 右侧：进度条独立区域 - 与人物图像高度一致并分散对齐
    .right-section {
      flex: 0 0 55% !important; // 相应调整比例
      display: flex;
      flex-direction: column;
      justify-content: space-around; // 分散对齐两个进度条
      padding: 0 8px 0 0 !important;
      box-sizing: border-box !important;
      max-width: 55% !important;
      
      // 由于移除了greeting，直接与motivation对齐：motivation(22px) = 22px
      margin-top: 22px;
      // 设置高度与人物图像一致：160px
      height: 160px;
      
      .progress-section {
        width: 100%;
        flex-shrink: 0;
        margin-bottom: 8px;

        .progress-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px !important;
          font-size: 14px;
          font-weight: 600;
          width: 100%;
          overflow: hidden;

          .progress-title {
            color: var(--text-secondary);
            flex: 1 1 auto;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .progress-value {
            color: var(--text-primary);
            flex: 0 0 auto;
            text-align: right;
            min-width: 70px;
            max-width: 80px;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        // 自定义pixel-retroui ProgressBar样式
        .pixel-progress-bar {
          // 确保像素风格渲染
          image-rendering: -moz-crisp-edges;
          image-rendering: -webkit-crisp-edges;
          image-rendering: pixelated;
          image-rendering: crisp-edges;
          
          // 高度由组件的size属性控制，无需CSS覆盖
          display: block;
        }

        // 保留旧的progress-bar类以防回退
        .progress-bar {
          width: 92% !important;
          max-width: 92% !important;
          height: 8px;
          border-radius: 0;
          background-color: #2a2a2a;
          border: 2px solid #1a1a1a;
          box-shadow: 
            inset 1px 1px 0 rgba(255,255,255,0.1),
            inset -1px -1px 0 rgba(0,0,0,0.3);
          overflow: hidden;
          box-sizing: border-box !important;
          position: relative;

          .progress-fill {
            height: 100%;
            border-radius: 0;
            transition: width 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;

            &.tasks {
              background: #00ff00;
              box-shadow: 
                inset 0 1px 0 #44ff44,
                inset 0 -1px 0 #00aa00;
              
              &::after {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-image: 
                  linear-gradient(90deg, 
                    transparent 0px, 
                    transparent 1px, 
                    rgba(255,255,255,0.1) 1px, 
                    rgba(255,255,255,0.1) 2px
                  );
                background-size: 2px 100%;
              }
            }

            &.energy {
              background: #ff6600;
              box-shadow: 
                inset 0 1px 0 #ff9944,
                inset 0 -1px 0 #cc4400;
              
              &::after {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-image: 
                  linear-gradient(90deg, 
                    transparent 0px, 
                    transparent 1px, 
                    rgba(255,255,255,0.1) 1px, 
                    rgba(255,255,255,0.1) 2px
                  );
                background-size: 2px 100%;
              }
            }
          }

          &::before {
            content: "";
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: linear-gradient(45deg, #444, #666, #444);
            z-index: -1;
            border-radius: 0;
          }
        }
      }

      // 最后一个进度条去除底部margin
      .progress-section:last-child {
        margin-bottom: 0 !important;
      }
    }
  }

  // 下方区域：任务列表（进一步缩小间距）
  &__tasks {
    margin-top: -12px; // 进一步缩小与人物图像的距离

    // 添加标题
    .tasks-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 6px 0; // 进一步缩小标题到列表的距离
      padding: 0 4px;
    }

    .tasks-container {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    // 改为水平滚动布局
    .tasks-scroll {
      display: flex;
      gap: 12px;
      padding: 8px 4px 12px 4px;
      overflow-x: auto;
      overflow-y: hidden;
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
      
      // 隐藏滚动条但保持功能
      &::-webkit-scrollbar {
        height: 4px;
      }

      &::-webkit-scrollbar-track {
        background: var(--bg-secondary);
        border-radius: 2px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--border-color);
        border-radius: 2px;
        
        &:hover {
          background: var(--text-secondary);
        }
      }
    }

    // 重新设计的任务卡片 - 突出标题和奖励
    .task-card {
      background: var(--bg-hover, #f8fafc);
      border: 1px solid var(--border-color, #e5e7eb);
      border-radius: 12px;
      padding: 14px; // 增加padding提供更多空间
      position: relative;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      
      // 增加卡片尺寸：3个卡片平分可用空间，确保内容不溢出
      width: calc((100% - 24px) / 3); // 减去2个gap的空间
      min-width: 130px; // 增加最小宽度
      max-width: 155px; // 增加最大宽度
      height: 220px; // 增加高度确保所有内容都能容纳
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: var(--accent-300, #c7d2fe);
      }

      &.completed {
        background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
        border-color: #22c55e;
      }

      // 重新设计的任务标题区域 - 更加突出
      .task-header {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 3px; // 减少间隙节省空间
        margin-bottom: 6px; // 减少底部间距
        text-align: center;

        .task-icon {
          font-size: 18px;
          width: 22px;
          height: 22px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }

        .task-title {
          font-size: 13px; // 增加字体大小突出显示
          font-weight: 700; // 更粗的字体
          color: var(--text-primary);
          margin: 0;
          line-height: 1.3;
          text-align: center;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2; // 允许最多2行
          -webkit-box-orient: vertical;
          width: 100%;
        }
      }

      .task-description {
        font-size: 9px;
        color: var(--text-secondary);
        margin: 0 0 6px 0; // 适中的底部间距
        line-height: 1.4; // 增加行高提升可读性
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3; // 允许3行描述，避免内容被截断
        -webkit-box-orient: vertical;
        height: 36px; // 增加高度容纳3行文本
        min-height: 36px; // 确保最小高度
      }

      // 重新设计的奖励显示区域 - 平衡空间分配
      .reward-display {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: 4px 0 6px 0; // 减少margin节省空间
        // 移除flex: 1，给予固定空间而不是占据剩余空间
        
        .reward-plate {
          width: 50px; // 突出显示奖励plate
          height: 50px;
          object-fit: contain;
          margin-bottom: 4px; // 减少底部间距节省空间
          image-rendering: pixelated; // 保持像素风格
          transition: transform 0.2s ease;
          
          &:hover {
            transform: scale(1.1);
          }
        }
        
        .reward-text {
          font-size: 10px;
          font-weight: 600;
          color: var(--text-secondary);
          text-align: center;
          opacity: 0.8;
          white-space: nowrap;
        }
      }

      .task-footer {
        display: flex;
        justify-content: center; // 改为居中对齐
        align-items: center; // 垂直居中对齐
        margin-top: auto; // 推到底部
        height: 30px; // 固定高度确保按钮有合适空间

        .task-button {
          border: none;
          border-radius: 6px;
          color: white;
          font-weight: 600;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
          flex-shrink: 0;

          &.completed {
            background: #22c55e;
            width: 50px; // 增加宽度容纳"已完成"文字
            height: 22px;
            font-size: 9px; // 调整字体大小
          }

          &.start-button {
            background: var(--accent-600, #2563eb);
            width: 34px;
            height: 22px;
            font-size: 9px;
            padding: 3px 5px;
            white-space: nowrap;

            &:hover {
              background: var(--accent-700, #1d4ed8);
              transform: scale(1.05);
            }
          }

          &:hover {
            transform: scale(1.05);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .character-task-card {
    padding: 16px;
    max-width: min(95vw, 400px);
    
    // 平板端状态栏调整
    &__status-bar {
      padding: 6px 0 10px 0;
      margin-bottom: 10px;
      
      .status-left .status-title {
        font-size: 16px; // 平板端稍小
      }
      
      .status-right {
        gap: 16px; // 减小间距
        
        .stat-item {
          gap: 5px;
          
          .stat-icon {
            width: 18px;
            height: 18px;
          }
          
          .stat-value {
            font-size: 14px;
          }
        }
      }
    }

    &__header {
      gap: 16px;
      align-items: flex-start !important; // 平板端也使用flex-start

      .left-section {
        flex: 0 0 45% !important; // 保持一致的比例

        .character-info {
          .greeting {
            font-size: 14px;
          }

          .motivation {
            font-size: 12px;
            margin-bottom: 10px;
          }

          .character-avatar {
            .character-image {
              width: 150px;
              height: 150px;
            }
          }
        }

        .stats-section {
          gap: 16px;
          margin-top: 6px;

          .stat-item {
            gap: 6px;

            .stat-icon {
              width: 19px;
              height: 19px;
            }

            .stat-value {
              font-size: 14px;
            }
          }
        }
      }

              .right-section {
          flex: 0 0 55% !important; // 保持一致比例
          justify-content: space-around; // 分散对齐
          // 由于移除了greeting，直接与motivation对齐：motivation(18px) = 18px
          margin-top: 18px;
          // 设置高度与人物图像一致：150px
          height: 150px;
        
        .progress-section {
          margin-bottom: 6px;

          .progress-header {
            margin-bottom: 3px;
            font-size: 12px;

            .progress-title,
            .progress-value {
              font-size: 12px;
            }

            .progress-value {
              min-width: 60px;
              max-width: 70px;
            }
          }

          // pixel-progress-bar 高度由size属性统一控制，无需响应式覆盖
          
          .progress-bar {
            width: 90% !important;
            max-width: 90% !important;
            height: 10px; // 平板端适中尺寸
            border-radius: 0; // 保持方角
            border-width: 2px; // 保持厚边框
          }
        }
      }
    }

    &__tasks {
      .tasks-title {
        font-size: 16px;
        margin-bottom: 10px;
      }

      .tasks-scroll {
        gap: 10px;
        padding: 6px 2px 10px 2px;
      }

      .task-card {
        // 平板端也使用3个卡片自适应布局（确保内容不溢出）
        width: calc((100% - 20px) / 3); // 减小gap适应平板
        min-width: 110px; // 增加最小宽度
        max-width: 135px; // 增加最大宽度
        height: 190px; // 增加高度确保内容不溢出
        padding: 12px; // 增加padding

        .task-header {
          margin-bottom: 5px;
          gap: 4px;

          .task-icon {
            font-size: 14px;
            width: 18px;
            height: 18px;
          }

          .task-title {
            font-size: 10px;
          }
        }

        .task-description {
          font-size: 8px;
          height: 30px; // 增加高度给描述更多空间
          min-height: 30px;
          margin-bottom: 5px; // 适中的底部间距
          line-height: 1.4;
          -webkit-line-clamp: 3; // 允许3行描述
        }

        // 平板端奖励显示调整 - 固定空间分配
        .reward-display {
          margin: 3px 0 5px 0; // 减少margin节省空间
          
          .reward-plate {
            width: 42px; // 平板端适中尺寸
            height: 42px;
            margin-bottom: 3px; // 减少间距节省空间
          }
          
          .reward-text {
            font-size: 9px;
          }
        }

        .task-footer {
          min-height: 18px;

          .task-button {
            &.completed {
              width: 42px; // 平板端增加宽度容纳"已完成"
              height: 18px;
              font-size: 8px;
            }

            &.start-button {
              width: 28px;
              height: 18px;
              font-size: 8px;
              padding: 2px 4px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .character-task-card {
    padding: 12px;
    max-width: min(96vw, 350px);

    // 手机端状态栏调整  
    &__status-bar {
      padding: 4px 0 8px 0;
      margin-bottom: 8px;
      
      .status-left .status-title {
        font-size: 14px; // 手机端更小
      }
      
      .status-right {
        gap: 12px; // 进一步减小间距
        
        .stat-item {
          gap: 4px;
          
          .stat-icon {
            width: 16px;
            height: 16px;
          }
          
          .stat-value {
            font-size: 12px;
          }
        }
      }
    }

    &__header {
      display: flex !important;
      flex-direction: row !important;
      align-items: flex-start !important; // 手机端也使用flex-start
      gap: 12px !important;
      width: 100% !important;
      box-sizing: border-box !important;

      .left-section {
        flex: 0 0 42% !important;
        
        .character-info {
          .greeting {
            font-size: 13px;
          }

          .motivation {
            font-size: 11px;
            margin-bottom: 8px;
          }

          .character-avatar {
            .character-image {
              width: 120px;
              height: 120px;
            }
          }
        }

        .stats-section {
          gap: 12px;
          margin-top: 4px;
          
          .stat-item {
            gap: 4px;
            
            .stat-icon {
              width: 17px;
              height: 17px;
            }
            
            .stat-value {
              font-size: 12px;
            }
          }
        }
      }

              .right-section {
          flex: 0 0 55% !important; // 保持一致比例
          justify-content: space-around; // 分散对齐
          // 由于移除了greeting，直接与motivation对齐：motivation(15px) = 15px
          margin-top: 15px;
          // 设置高度与人物图像一致：120px
          height: 120px;
        
        .progress-section {
          margin-bottom: 4px;

          .progress-header {
            margin-bottom: 2px;
            font-size: 10px;

            .progress-title,
            .progress-value {
              font-size: 10px;
            }

            .progress-value {
              min-width: 50px;
              max-width: 60px;
            }
          }

          // pixel-progress-bar 高度由size属性统一控制，无需响应式覆盖
          
          .progress-bar {
            width: 88% !important;
            max-width: 88% !important;
            height: 8px; // 手机端紧凑尺寸
            border-radius: 0; // 保持方角
            border-width: 1px; // 手机端稍细的边框
          }
        }
      }
    }

    &__tasks {
      .tasks-title {
        font-size: 14px;
        margin-bottom: 8px;
      }

      .tasks-scroll {
        gap: 8px;
        padding: 4px 2px 8px 2px;
      }

      .task-card {
        // 手机端也使用3个卡片自适应布局（确保内容不溢出）
        width: calc((100% - 16px) / 3); // 更小的gap适应手机
        min-width: 95px; // 增加手机端最小宽度
        max-width: 115px; // 增加最大宽度
        height: 160px; // 增加高度确保内容不溢出
        padding: 10px; // 增加padding

        .task-header {
          margin-bottom: 4px;

          .task-icon {
            font-size: 12px;
            width: 16px;
            height: 16px;
          }

          .task-title {
            font-size: 9px;
          }
        }

        .task-description {
          font-size: 7px;
          height: 24px; // 增加高度给描述更多空间
          min-height: 24px;
          -webkit-line-clamp: 3; // 允许3行描述
          margin-bottom: 3px; // 适中的底部间距
          line-height: 1.4;
        }

        // 手机端奖励显示调整 - 固定空间分配
        .reward-display {
          margin: 2px 0 4px 0; // 紧凑的间距节省空间
          
          .reward-plate {
            width: 36px; // 手机端紧凑尺寸
            height: 36px;
            margin-bottom: 2px; // 紧凑间距节省空间
          }
          
          .reward-text {
            font-size: 8px;
          }
        }

                  .task-footer {
            min-height: 16px;

            .task-button {
              &.completed {
                width: 36px; // 手机端增加宽度容纳"已完成"
                height: 16px;
                font-size: 7px;
              }

              &.start-button {
                width: 24px;
                height: 16px;
                font-size: 7px;
                padding: 2px 3px;
              }
            }
          }
      }
    }
  }
} 