// 肌肉可视化演示页面样式
.muscle-demo-page {
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary);
  padding: var(--space-4);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
  
  // iOS优化
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent;
  
  .demo-header {
    text-align: center;
    margin-bottom: var(--space-8);
    
    h1 {
      font-size: var(--text-3xl);
      font-weight: var(--font-bold);
      margin-bottom: var(--space-2);
      color: var(--text-primary);
    }
    
    p {
      font-size: var(--text-lg);
      color: var(--text-secondary);
      margin: 0;
    }
  }
  
  .demo-controls {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-6);
    justify-content: center;
    margin-bottom: var(--space-6);
    padding: var(--space-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    
    .control-group {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--space-2);
      
      label {
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        color: var(--text-secondary);
      }
      
      .toggle-buttons {
        display: flex;
        gap: var(--space-1);
        
        button {
          padding: var(--space-2) var(--space-3);
          border: 1px solid var(--border-color);
          background: var(--bg-primary);
          color: var(--text-primary);
          border-radius: var(--radius-md);
          font-size: var(--text-sm);
          font-weight: var(--font-medium);
          cursor: pointer;
          transition: all 0.2s ease;
          min-height: var(--ios-touch-target);
          
          &:hover {
            background: var(--bg-hover);
          }
          
          &.active {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
          }
          
          &:active {
            transform: scale(0.98);
          }
        }
      }
      
      .preset-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: var(--space-2);
        justify-content: center;
        
        button {
          padding: var(--space-2) var(--space-3);
          border: 1px solid var(--border-color);
          background: var(--bg-primary);
          color: var(--text-primary);
          border-radius: var(--radius-md);
          font-size: var(--text-sm);
          cursor: pointer;
          transition: all 0.2s ease;
          min-height: var(--ios-touch-target);
          
          &:hover {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
          }
          
          &:active {
            transform: scale(0.98);
          }
        }
      }
    }
  }
  
  .selected-muscles-info {
    text-align: center;
    margin-bottom: var(--space-6);
    
    h3 {
      font-size: var(--text-xl);
      font-weight: var(--font-semibold);
      margin-bottom: var(--space-3);
      color: var(--text-primary);
    }
    
    .muscle-tags {
      display: flex;
      flex-wrap: wrap;
      gap: var(--space-2);
      justify-content: center;
      
      .muscle-tag {
        padding: var(--space-1) var(--space-2);
        background: var(--color-primary);
        color: white;
        border-radius: var(--radius-sm);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        cursor: pointer;
        transition: all 0.2s ease;
        
        &:hover {
          background: var(--color-primary-dark);
          transform: scale(1.05);
        }
        
        &:active {
          transform: scale(0.95);
        }
      }
    }
  }
  
  .demo-visualization {
    display: flex;
    justify-content: center;
    margin-bottom: var(--space-8);
    
    // 确保组件居中且有最大宽度
    > * {
      max-width: 800px;
      width: 100%;
    }
  }
  
  .demo-features {
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--space-4);
      margin-top: var(--space-4);
      
      .feature-card {
        padding: var(--space-4);
        background: var(--bg-secondary);
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-color);
        text-align: center;
        transition: transform 0.2s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        h4 {
          font-size: var(--text-lg);
          font-weight: var(--font-semibold);
          margin-bottom: var(--space-2);
          color: var(--text-primary);
        }
        
        p {
          font-size: var(--text-sm);
          color: var(--text-secondary);
          line-height: 1.5;
          margin: 0;
        }
      }
    }
    
    h3 {
      font-size: var(--text-2xl);
      font-weight: var(--font-bold);
      text-align: center;
      margin-bottom: var(--space-4);
      color: var(--text-primary);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .muscle-demo-page {
    padding: var(--space-3);
    
    .demo-header {
      margin-bottom: var(--space-6);
      
      h1 {
        font-size: var(--text-2xl);
      }
      
      p {
        font-size: var(--text-base);
      }
    }
    
    .demo-controls {
      flex-direction: column;
      gap: var(--space-4);
      
      .control-group {
        .preset-buttons {
          button {
            font-size: var(--text-xs);
            padding: var(--space-1) var(--space-2);
          }
        }
      }
    }
    
    .selected-muscles-info {
      h3 {
        font-size: var(--text-lg);
      }
      
      .muscle-tags {
        .muscle-tag {
          font-size: var(--text-xs);
        }
      }
    }
    
    .demo-features {
      .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-3);
      }
      
      h3 {
        font-size: var(--text-xl);
      }
    }
  }
}

@media (max-width: 480px) {
  .muscle-demo-page {
    padding: var(--space-2);
    
    .demo-header {
      margin-bottom: var(--space-4);
      
      h1 {
        font-size: var(--text-xl);
      }
      
      p {
        font-size: var(--text-sm);
      }
    }
    
    .demo-controls {
      padding: var(--space-3);
      gap: var(--space-3);
    }
    
    .selected-muscles-info {
      margin-bottom: var(--space-4);
    }
    
    .demo-visualization {
      margin-bottom: var(--space-6);
    }
  }
}

// 暗色主题
.theme-dark {
  background: var(--bg-primary);
  
  .demo-controls {
    background: var(--bg-secondary);
    border-color: var(--border-color);
  }
  
  .demo-features {
    .feature-card {
      background: var(--bg-secondary);
      border-color: var(--border-color);
      
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
    }
  }
}

// iOS特定优化
@supports (-webkit-touch-callout: none) {
  .muscle-demo-page {
    // iOS Safari优化
    -webkit-overflow-scrolling: touch;
    
    .demo-controls {
      .control-group {
        .toggle-buttons,
        .preset-buttons {
          button {
            // iOS按钮触摸优化
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
          }
        }
      }
    }
    
    .selected-muscles-info {
      .muscle-tags {
        .muscle-tag {
          // iOS标签触摸优化
          -webkit-tap-highlight-color: transparent;
          -webkit-touch-callout: none;
        }
      }
    }
  }
}