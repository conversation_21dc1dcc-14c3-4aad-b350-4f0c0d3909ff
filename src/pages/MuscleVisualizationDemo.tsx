import React, { useState } from 'react';
import { MuscleGroupEnum } from '../types/muscle.types';
import { LabeledMuscleIllustration } from '../components/fitness/MuscleVisualization/LabeledMuscleIllustration';
import { MuscleIllustration } from '../components/fitness/MuscleVisualization/MuscleIllustration';
import './MuscleVisualizationDemo.scss';

export const MuscleVisualizationDemo: React.FC = () => {
  const [selectedMuscles, setSelectedMuscles] = useState<MuscleGroupEnum[]>([
    MuscleGroupEnum.CHEST,
    MuscleGroupEnum.TRICEPS,
    MuscleGroupEnum.SHOULDERS_FRONT
  ]);
  
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [showLabeled, setShowLabeled] = useState(true);

  // 肌肉颜色配置示例
  const muscleColorConfig = {
    [MuscleGroupEnum.CHEST]: 'primary' as const,
    [MuscleGroupEnum.TRICEPS]: 'secondary' as const,
    [MuscleGroupEnum.SHOULDERS_FRONT]: 'primary' as const,
    [MuscleGroupEnum.BICEPS]: 'secondary' as const,
    [MuscleGroupEnum.BACK]: 'primary' as const,
    [MuscleGroupEnum.QUADRICEPS]: 'secondary' as const
  };

  const handleToggleMuscle = (muscle: MuscleGroupEnum) => {
    setSelectedMuscles(prev => 
      prev.includes(muscle) 
        ? prev.filter(m => m !== muscle)
        : [...prev, muscle]
    );
  };

  const handlePresetSelection = (preset: string) => {
    switch (preset) {
      case 'push':
        setSelectedMuscles([
          MuscleGroupEnum.CHEST, 
          MuscleGroupEnum.TRICEPS, 
          MuscleGroupEnum.SHOULDERS_FRONT
        ]);
        break;
      case 'pull':
        setSelectedMuscles([
          MuscleGroupEnum.BACK, 
          MuscleGroupEnum.BICEPS, 
          MuscleGroupEnum.SHOULDERS_BACK
        ]);
        break;
      case 'legs':
        setSelectedMuscles([
          MuscleGroupEnum.QUADRICEPS, 
          MuscleGroupEnum.HAMSTRINGS, 
          MuscleGroupEnum.GLUTES,
          MuscleGroupEnum.CALVES
        ]);
        break;
      case 'upper':
        setSelectedMuscles([
          MuscleGroupEnum.CHEST,
          MuscleGroupEnum.BACK,
          MuscleGroupEnum.SHOULDERS,
          MuscleGroupEnum.BICEPS,
          MuscleGroupEnum.TRICEPS
        ]);
        break;
      case 'core':
        setSelectedMuscles([
          MuscleGroupEnum.ABDOMINALS,
          MuscleGroupEnum.OBLIQUES
        ]);
        break;
      default:
        setSelectedMuscles([]);
    }
  };

  return (
    <div className={`muscle-demo-page ${theme === 'dark' ? 'theme-dark' : ''}`}>
      <div className="demo-header">
        <h1>肌肉可视化组件演示</h1>
        <p>展示基于参考图像实现的中文标签肌肉可视化效果</p>
      </div>

      {/* 控制面板 */}
      <div className="demo-controls">
        <div className="control-group">
          <label>显示模式:</label>
          <div className="toggle-buttons">
            <button 
              className={showLabeled ? 'active' : ''}
              onClick={() => setShowLabeled(true)}
            >
              带标签版本
            </button>
            <button 
              className={!showLabeled ? 'active' : ''}
              onClick={() => setShowLabeled(false)}
            >
              基础版本
            </button>
          </div>
        </div>

        <div className="control-group">
          <label>主题:</label>
          <div className="toggle-buttons">
            <button 
              className={theme === 'light' ? 'active' : ''}
              onClick={() => setTheme('light')}
            >
              浅色
            </button>
            <button 
              className={theme === 'dark' ? 'active' : ''}
              onClick={() => setTheme('dark')}
            >
              深色
            </button>
          </div>
        </div>

        <div className="control-group">
          <label>预设选择:</label>
          <div className="preset-buttons">
            <button onClick={() => handlePresetSelection('push')}>推 (Push)</button>
            <button onClick={() => handlePresetSelection('pull')}>拉 (Pull)</button>
            <button onClick={() => handlePresetSelection('legs')}>腿部</button>
            <button onClick={() => handlePresetSelection('upper')}>上身</button>
            <button onClick={() => handlePresetSelection('core')}>核心</button>
            <button onClick={() => handlePresetSelection('clear')}>清空</button>
          </div>
        </div>
      </div>

      {/* 选中肌肉信息 */}
      <div className="selected-muscles-info">
        <h3>已选择肌肉群 ({selectedMuscles.length})</h3>
        <div className="muscle-tags">
          {selectedMuscles.map(muscle => (
            <span 
              key={muscle} 
              className="muscle-tag"
              onClick={() => handleToggleMuscle(muscle)}
            >
              {muscle} ×
            </span>
          ))}
        </div>
      </div>

      {/* 肌肉可视化组件 */}
      <div className="demo-visualization">
        {showLabeled ? (
          <LabeledMuscleIllustration
            selectedMuscles={selectedMuscles}
            onToggleMuscle={handleToggleMuscle}
            muscleColorConfig={muscleColorConfig}
            theme={theme}
            isLoading={false}
          />
        ) : (
          <MuscleIllustration
            selectedMuscles={selectedMuscles}
            onToggleMuscle={handleToggleMuscle}
            theme={theme}
            isLoading={false}
          />
        )}
      </div>

      {/* 功能说明 */}
      <div className="demo-features">
        <h3>功能特性</h3>
        <div className="features-grid">
          <div className="feature-card">
            <h4>🏷️ 中文标签</h4>
            <p>参考图像实现的中文肌肉群标签，精准定位各个肌肉部位</p>
          </div>
          <div className="feature-card">
            <h4>🍎 iOS优化</h4>
            <p>针对iOS设备优化的硬件加速和触摸交互体验</p>
          </div>
          <div className="feature-card">
            <h4>🎨 双主题</h4>
            <p>支持浅色和深色主题，适应不同使用环境</p>
          </div>
          <div className="feature-card">
            <h4>📱 响应式</h4>
            <p>完美适配各种屏幕尺寸，从iPhone到iPad</p>
          </div>
          <div className="feature-card">
            <h4>⚡ 高性能</h4>
            <p>SVG矢量图形配合硬件加速，流畅的交互体验</p>
          </div>
          <div className="feature-card">
            <h4>🎯 精准定位</h4>
            <p>基于实际SVG坐标系统的标签定位，准确对应肌肉群</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MuscleVisualizationDemo;