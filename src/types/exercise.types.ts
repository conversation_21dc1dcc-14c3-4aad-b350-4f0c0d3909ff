/**
 * 动作详情页面相关数据类型
 */

// 动作详情API响应类型
export interface ExerciseDetailResponse {
  exercise_id: number;
  target_muscles_id: number[];
  synergist_muscles_id: number[];
  ex_instructions: string[];
  exercise_tips: string[];
  video_file: string;
  is_public: boolean;
  id: number;
  created_at: string;
  updated_at: string;
  hit_time: number;
}

// 肌群信息类型
export interface MuscleGroupInfo {
  id: number;
  name: string;
  type: 'target' | 'synergist';
}

// 动作详情UI状态类型
export interface ExerciseDetailState {
  detail: ExerciseDetailResponse | null;
  loading: boolean;
  error: string | null;
  videoUrl: string | null;
  targetMuscles: MuscleGroupInfo[];
  synergistMuscles: MuscleGroupInfo[];
}

// 视频播放器配置类型
export interface VideoPlayerConfig {
  src: string;
  poster?: string;
  autoplay?: boolean;
  controls?: boolean;
  loop?: boolean;
  muted?: boolean;
}

// 动作指导步骤类型
export interface InstructionStep {
  id: number;
  content: string;
  isActive?: boolean;
}

// 动作提示类型
export interface ExerciseTip {
  id: number;
  content: string;
  type?: 'warning' | 'info' | 'success';
} 