// 运动记录数据类型
export interface ExerciseRecord {
  todayDuration: number // 今日运动时长(分钟)
  goalDuration: number // 目标时长(分钟)
  exerciseType: string // 运动类型
  exerciseCount: number // 今日运动次数
  lastExercise?: {
    name: string // 运动名称
    duration: number // 持续时间(分钟)
    calories: number // 消耗卡路里
    timestamp: Date // 运动时间
  }
}

// 营养记录数据类型
export interface NutritionRecord {
  calories: {
    consumed: number // 已摄入卡路里
    goal: number // 目标卡路里
  }
  macros: {
    protein: { consumed: number, goal: number, percentage: number } // 蛋白质(克)
    carbs: { consumed: number, goal: number, percentage: number } // 碳水化合物(克)
    fat: { consumed: number, goal: number, percentage: number } // 脂肪(克)
  }
  mealCount: number // 今日用餐次数
  lastMeal?: {
    name: string // 餐食名称
    calories: number // 卡路里
    timestamp: Date // 用餐时间
  }
}

// 体重趋势数据类型
export interface WeightTrend {
  currentWeight: number // 当前体重
  weightUnit: 'kg' | 'lb' // 体重单位
  weeklyData: Array<{
    date: string // 日期 (YYYY-MM-DD)
    weight: number // 体重
  }>
  trend: 'up' | 'down' | 'stable' // 趋势方向
  changeAmount: number // 变化量
  bmi?: number // BMI指数
  targetWeight?: number // 目标体重
}

// 水分摄入数据类型
export interface WaterIntake {
  consumed: number // 已摄入量
  goal: number // 目标摄入量
  unit: 'ml' | 'l' // 单位
  drinkCount: number // 喝水次数
  lastDrinkTime?: Date // 最后一次喝水时间
  reminders: {
    enabled: boolean // 是否启用提醒
    interval: number // 提醒间隔(分钟)
    nextReminder?: Date // 下次提醒时间
  }
}

// 步数统计数据类型
export interface StepsCount {
  steps: number // 当前步数
  goal: number // 目标步数
  distance: number // 行走距离(公里)
  calories: number // 消耗卡路里
  activeMinutes: number // 活跃分钟数
  hourlyData?: Array<{
    hour: number // 小时(0-23)
    steps: number // 该小时步数
  }>
}

// 数据面板整体状态类型
export interface DashboardData {
  exerciseRecord: ExerciseRecord
  nutritionRecord: NutritionRecord
  weightTrend: WeightTrend
  waterIntake: WaterIntake
  stepsCount: StepsCount
  lastUpdated: Date // 最后更新时间
}

// 加载状态类型
export interface DashboardLoadingState {
  exercise: boolean
  nutrition: boolean
  weight: boolean
  water: boolean
  steps: boolean
  overall: boolean
}

// 错误状态类型
export interface DashboardError {
  type: 'network' | 'data' | 'permission' | 'unknown'
  message: string
  component?: 'exercise' | 'nutrition' | 'weight' | 'water' | 'steps'
  timestamp: Date
}

// 卡片通用属性类型
export interface BaseCardProps {
  className?: string
  loading?: boolean
  error?: DashboardError | null
  onRetry?: () => void
  onRefresh?: () => void
}

// 数据面板配置类型
export interface DashboardConfig {
  refreshInterval: number // 自动刷新间隔(毫秒)
  enableAnimations: boolean // 是否启用动画
  compactMode: boolean // 紧凑模式
  showDetails: boolean // 显示详细信息
}

// 图表配置类型
export interface ChartConfig {
  colors: {
    primary: string
    secondary: string
    success: string
    warning: string
    danger: string
  }
  animations: {
    duration: number
    easing: string
  }
}

// 模拟数据生成器类型
export interface MockDataGenerator {
  generateExerciseRecord(): ExerciseRecord
  generateNutritionRecord(): NutritionRecord
  generateWeightTrend(): WeightTrend
  generateWaterIntake(): WaterIntake
  generateStepsCount(): StepsCount
  generateFullDashboard(): DashboardData
} 