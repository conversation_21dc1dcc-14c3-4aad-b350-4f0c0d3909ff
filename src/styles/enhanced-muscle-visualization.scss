// 🏋️‍♀️ 增强版肌肉可视化样式
// 集成muscle-selector-complete的样式系统

// 引入muscle-selector-complete的基础样式
@import '../components/muscle-selector-complete/src/styles.css';

// === 增强版肌肉图像样式 ===
.enhanced-muscle-illustration {
  max-width: 100%;
  height: auto;
  user-select: none;
  
  // iOS硬件加速优化
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  
  // 肌肉组件基础样式
  .muscle-group {
    cursor: pointer;
    transition: all 0.1s ease-out;
    
    // 悬停效果
    &:hover {
      .muscle-default {
        @apply fill-blue-400;
      }
      
      .fill-slate-400 {
        @apply fill-blue-400;
      }
    }
    
    // 激活状态
    &:active {
      transform: scale(0.98);
    }
  }
  
  // 肌肉路径基础样式
  .muscle-path {
    vector-effect: non-scaling-stroke;
    transition: fill 0.1s ease-out, stroke 0.1s ease-out;
  }
  
  // 透明填充区域
  .fill-transparent {
    fill: transparent;
  }
}

// === 主题支持 ===

// 浅色主题（默认）
.theme-light {
  #enhanced-muscle-illustration {
    .muscle-default {
      @apply fill-slate-400;
    }
    
    .muscle-selected {
      @apply fill-blue-500;
    }
    
    .muscle-hover:hover {
      @apply fill-blue-400;
    }
  }
}

// 深色主题
.theme-dark {
  #enhanced-muscle-illustration {
    .muscle-default {
      @apply fill-sky-300 fill-opacity-60;
    }
    
    .muscle-selected {
      @apply fill-sky-400;
    }
    
    .muscle-hover:hover {
      @apply fill-sky-400 fill-opacity-80;
    }
  }
}

// === 响应式设计 ===

// 平板设备
@media (max-width: 768px) {
  .enhanced-muscle-illustration {
    max-width: 95%;
    
    .muscle-group {
      // 触摸设备的交互区域优化
      cursor: pointer;
      -webkit-tap-highlight-color: transparent;
    }
  }
}

// 手机设备
@media (max-width: 480px) {
  .enhanced-muscle-illustration {
    max-width: 100%;
    
    .muscle-path {
      // 移动端笔画优化
      stroke-width: 1.2;
    }
  }
}

// === 加载状态样式 ===
.muscle-visualization-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  
  .loading-spinner {
    @apply animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500;
  }
}

// === 性能优化样式 ===
.muscle-visualization-optimized {
  // 硬件加速
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  
  // 3D渲染上下文
  transform-style: preserve-3d;
  
  .muscle-group {
    // 每个肌肉组都启用硬件加速
    will-change: fill, transform;
    transform: translateZ(0);
  }
}

// === 辅助功能支持 ===
.muscle-visualization-accessible {
  // 键盘导航支持
  .muscle-group:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
  
  // 高对比度支持
  @media (prefers-contrast: high) {
    .muscle-default {
      @apply fill-gray-700;
    }
    
    .muscle-selected {
      @apply fill-blue-700;
    }
  }
  
  // 减少动画支持
  @media (prefers-reduced-motion: reduce) {
    .muscle-group,
    .muscle-path {
      transition: none;
    }
  }
}

// === 集成样式覆盖 ===
// 确保与现有MuscleVisualization模块的样式兼容
.muscle-visualization-module {
  // 当使用增强版时，应用特定样式
  &.enhanced {
    .muscle-illustration {
      @extend .enhanced-muscle-illustration;
    }
  }
  
  // 平滑过渡效果
  &.transitioning {
    .muscle-illustration {
      opacity: 0.7;
      transition: opacity 0.3s ease-in-out;
    }
    
    &.transition-complete {
      .muscle-illustration {
        opacity: 1;
      }
    }
  }
}

// === 调试模式样式 ===
.muscle-visualization-debug {
  border: 2px dashed #ef4444;
  position: relative;
  
  &::before {
    content: '🔧 Enhanced Muscle Visualization (Debug Mode)';
    position: absolute;
    top: -30px;
    left: 0;
    background: #ef4444;
    color: white;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    z-index: 1000;
  }
  
  .muscle-group {
    outline: 1px solid rgba(239, 68, 68, 0.3);
    
    &:hover {
      outline-color: rgba(239, 68, 68, 0.8);
    }
  }
}

// === 尺寸变体 ===
.muscle-visualization-size-sm {
  .enhanced-muscle-illustration {
    max-width: 300px;
  }
  
  .muscle-path {
    stroke-width: 0.8;
  }
}

.muscle-visualization-size-md {
  .enhanced-muscle-illustration {
    max-width: 500px;
  }
  
  .muscle-path {
    stroke-width: 1;
  }
}

.muscle-visualization-size-lg {
  .enhanced-muscle-illustration {
    max-width: 700px;
  }
  
  .muscle-path {
    stroke-width: 1.2;
  }
} 