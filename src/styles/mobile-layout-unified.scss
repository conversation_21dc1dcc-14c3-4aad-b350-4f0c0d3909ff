/**
 * 🎯 iOS移动端统一布局方案 - 最终版
 * 解决页面滚动、空白区域和浮窗遮挡问题
 * 
 * 核心策略:
 * 1. 兼容性优先的100vh/100dvh fallback
 * 2. 基于现有设计系统的z-index管理
 * 3. Flex布局+固定定位的混合方案
 * 4. 完整的iOS Safe Area适配
 */

/* ===== iOS移动端布局优化 ===== */
@media (max-width: 768px) {
  
  /* 🔧 基础视口设置 - 兼容性fallback策略 */
  html {
    height: 100vh;                    /* iOS <15.4 fallback */
    height: 100dvh;                   /* 现代浏览器优先 */
    overflow: hidden;                 /* 防止整体页面滚动 */
    background: var(--bg-primary);    /* 确保背景一致 */
  }
  
  body {
    height: 100vh;                    /* iOS <15.4 fallback */
    height: 100dvh;                   /* 现代浏览器优先 */
    overflow: hidden;                 /* 防止body滚动 */
    margin: 0;
    padding: 0;
    background: var(--bg-primary);
    
    /* iOS Safari特殊处理 */
    @supports (-webkit-touch-callout: none) {
      height: -webkit-fill-available;
    }
  }
  
  #root {
    height: 100vh;                    /* iOS <15.4 fallback */
    height: 100dvh;                   /* 现代浏览器优先 */
    overflow: hidden;
  }
  
  /* 📱 主布局容器 - Flex布局核心 */
  .layout.mobile-layout {
    height: 100vh;                    /* iOS <15.4 fallback */
    height: 100dvh;                   /* 现代动态视口 */
    display: flex;
    flex-direction: column;
    overflow: hidden;                 /* 关键：防止容器滚动 */
    background: var(--bg-primary);
  }
  
  /* 🔝 固定Header - 使用现有设计系统z-index */
  .page-header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: var(--z-fixed) !important;  /* 1030 - 来自design-system.css */
    
    /* 📐 高度计算: Safe Area + Header内容 */
    height: calc(env(safe-area-inset-top, 44px) + 64px) !important;
    min-height: calc(env(safe-area-inset-top, 44px) + 64px) !important;
    
    /* 📱 Safe Area适配的内边距 */
    padding-top: calc(env(safe-area-inset-top, 44px) + 16px) !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
    padding-bottom: 16px !important;
    
    /* 🎨 主题响应背景 */
    background: var(--bg-primary) !important;
    border-bottom: 1px solid var(--border-color) !important;
    
    /* 🌟 iOS毛玻璃效果 */
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    
    /* ⚡ 硬件加速优化 */
    will-change: transform !important;
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    
    /* 📱 Header内容布局保持不变 */
    .dashboard-header-content {
      display: flex !important;
      align-items: center !important;
      justify-content: space-between !important;
      width: 100% !important;
      min-height: 48px !important;
      gap: 12px !important;
    }
    
    .dashboard-date-info {
      flex: 1 !important;
      min-width: 0 !important;
      
      .dashboard-title {
        font-size: 16px !important;
        margin: 0 0 2px 0 !important;
        color: var(--text-primary) !important;
        font-weight: bold !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
      }
      
      .dashboard-subtitle {
        font-size: 12px !important;
        margin: 0 !important;
        color: var(--text-secondary) !important;
      }
    }
    
    .header-actions {
      flex-shrink: 0 !important;
    }
  }
  
  /* 🎯 主内容区域 - Flex布局核心 */
  .main-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;      /* 防止main-content滚动 */
    
    /* 为固定Header预留空间 */
    padding-top: calc(env(safe-area-inset-top, 44px) + 64px) !important;
    
    /* 为底部导航预留空间 */
    &.with-bottom-nav {
      padding-bottom: calc(70px + env(safe-area-inset-bottom, 0px)) !important;
    }
  }
  
  /* 📄 页面内容区域 - 纯滚动区域 */
  .page-content {
    flex: 1 !important;              /* 占满剩余空间 */
    overflow-y: auto !important;     /* 只有这里可以滚动 */
    -webkit-overflow-scrolling: touch !important;  /* iOS平滑滚动 */
    
    /* 内容区域内边距 */
    padding: 16px !important;
    
    /* 确保内容可以完全滚动 */
    min-height: max-content !important;
    
    /* 重置之前可能的样式冲突 */
    height: auto !important;         /* 移除固定高度 */
    position: relative !important;   /* 重置定位 */
  }
  
  /* 🔻 固定底部导航 - 使用现有设计系统z-index */
  .bottom-navigation {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: var(--z-sticky) !important;  /* 1020 - 来自design-system.css */
    
    /* 📐 高度计算: 导航高度 + Safe Area */
    height: calc(70px + env(safe-area-inset-bottom, 0px)) !important;
    padding-bottom: env(safe-area-inset-bottom, 0px) !important;
    
    /* 🎨 背景和边框 */
    background: var(--bg-surface) !important;
    border-top: 1px solid var(--border-color) !important;
    
    /* 🌟 毛玻璃效果 */
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    
    /* 📱 阴影效果 */
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
    
    /* ⚡ 硬件加速 */
    will-change: transform !important;
    transform: translateZ(0) !important;
  }
  
  /* 💬 修复聊天按钮层级和位置 */
  .dashboard-v2__chat-button {
    position: fixed !important;
    right: 16px !important;
    z-index: var(--z-popover) !important;  /* 1060 - 高于导航栏 */
    
    /* 📐 位置计算: 导航栏高度 + Safe Area + 间距 */
    bottom: calc(70px + env(safe-area-inset-bottom, 0px) + 16px) !important;
    
    /* 🎨 按钮样式保持不变 */
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 50px !important;
    padding: 12px 20px !important;
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3) !important;
    transition: all 0.3s ease !important;
    
    /* 📱 确保按钮可见性 */
    min-height: 44px !important;     /* Apple HIG标准 */
    
    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 12px 32px rgba(79, 70, 229, 0.4) !important;
    }
    
    &:active {
      transform: translateY(0) !important;
    }
    
    .chat-icon {
      font-size: 16px !important;
    }
    
    .chat-text {
      white-space: nowrap !important;
    }
  }
  
  /* 🎨 iOS状态栏集成层 - 配合现有系统 */
  .ios-statusbar-integration {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: env(safe-area-inset-top, 44px) !important;
    z-index: calc(var(--z-fixed) + 10) !important;  /* 1040 - 高于Header */
    pointer-events: none !important;
    background: var(--bg-primary) !important;
    opacity: 1 !important;
  }
}

/* ===== 横屏模式优化 ===== */
@media (max-width: 768px) and (orientation: landscape) {
  .page-header {
    height: calc(env(safe-area-inset-top, 20px) + 48px) !important;
    padding-top: calc(env(safe-area-inset-top, 20px) + 12px) !important;
    padding-bottom: 12px !important;
  }
  
  .main-content {
    padding-top: calc(env(safe-area-inset-top, 20px) + 48px) !important;
  }
  
  .bottom-navigation {
    height: calc(60px + env(safe-area-inset-bottom, 0px)) !important;
  }
  
  .main-content.with-bottom-nav {
    padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px)) !important;
  }
  
  .dashboard-v2__chat-button {
    bottom: calc(60px + env(safe-area-inset-bottom, 0px) + 12px) !important;
    padding: 8px 16px !important;
  }
}

/* ===== 超小屏幕优化 ===== */
@media (max-width: 480px) {
  .page-header {
    padding-left: 12px !important;
    padding-right: 12px !important;
    
    .dashboard-header-content {
      gap: 8px !important;
    }
    
    .dashboard-date-info .dashboard-title {
      font-size: 14px !important;
    }
    
    .dashboard-date-info .dashboard-subtitle {
      font-size: 11px !important;
    }
  }
  
  .page-content {
    padding: 12px !important;
  }
  
  .dashboard-v2__chat-button {
    right: 12px !important;
    bottom: calc(70px + env(safe-area-inset-bottom, 0px) + 12px) !important;
    padding: 10px 16px !important;
    font-size: 13px !important;
    
    .chat-icon {
      font-size: 14px !important;
    }
  }
}

/* ===== 主题适配 ===== */
/* 浅色主题 */
@media (max-width: 768px) {
  .theme-light {
    .page-header {
      background: #ffffff !important;
      border-bottom-color: #e2e8f0 !important;
      
      .dashboard-title {
        color: #1e293b !important;
      }
      
      .dashboard-subtitle {
        color: #475569 !important;
      }
    }
    
    .ios-statusbar-integration {
      background: #ffffff !important;
    }
  }
}

/* 暗色主题 */
@media (max-width: 768px) {
  .theme-dark {
    .page-header {
      background: #0f172a !important;
      border-bottom-color: #374151 !important;
      
      .dashboard-title {
        color: #f8fafc !important;
      }
      
      .dashboard-subtitle {
        color: #cbd5e1 !important;
      }
    }
    
    .ios-statusbar-integration {
      background: #0f172a !important;
    }
    
    .bottom-navigation {
      background: #1e293b !important;
      border-top-color: #374151 !important;
    }
  }
}

/* ===== 调试模式 (开发环境) ===== */
.debug-mobile-layout {
  @media (max-width: 768px) {
    .page-header {
      background: rgba(255, 0, 0, 0.3) !important;
      border: 2px solid red !important;
    }
    
    .main-content {
      background: rgba(0, 255, 0, 0.1) !important;
      border: 2px solid green !important;
    }
    
    .page-content {
      background: rgba(0, 0, 255, 0.1) !important;
      border: 2px solid blue !important;
    }
    
    .bottom-navigation {
      background: rgba(255, 255, 0, 0.3) !important;
      border: 2px solid yellow !important;
    }
    
    .dashboard-v2__chat-button {
      background: rgba(255, 0, 255, 0.5) !important;
      border: 2px solid magenta !important;
    }
  }
}

/* ===== 辅助功能和性能优化 ===== */
@media (max-width: 768px) {
  /* 减少动画模式支持 */
  @media (prefers-reduced-motion: reduce) {
    .page-header,
    .bottom-navigation,
    .dashboard-v2__chat-button {
      transition: none !important;
      backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important;
    }
  }
  
  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    .page-header {
      border-bottom-width: 2px !important;
      background: var(--bg-primary) !important;
    }
    
    .bottom-navigation {
      border-top-width: 2px !important;
    }
    
    .dashboard-v2__chat-button {
      border: 2px solid var(--accent-500) !important;
    }
  }
  
  /* 触摸目标优化 */
  .page-header *,
  .bottom-navigation *,
  .dashboard-v2__chat-button {
    min-height: 44px !important;  /* Apple HIG标准 */
    min-width: 44px !important;
  }
}