/**
 * 🎯 iOS布局修复 - 简化版 (解决header间隙问题)
 * 使用简单媒体查询，确保在所有iOS设备上生效
 */

/* ===== iOS移动端布局修复 ===== */
@media (max-width: 768px) {
  
  /* 🔝 Header - 直接扩展到状态栏区域 */
  .page-header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: var(--z-fixed) !important; /* 1030 */
    
    /* ✅ 关键修复：去掉16px额外间距 */
    height: calc(env(safe-area-inset-top, 44px) + 56px) !important;
    min-height: calc(env(safe-area-inset-top, 44px) + 56px) !important;
    padding-top: env(safe-area-inset-top, 44px) !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
    padding-bottom: 8px !important;
    
    /* 主题响应背景 */
    background: var(--bg-primary) !important;
    border-bottom: 1px solid var(--border-color) !important;
    
    /* iOS毛玻璃效果 */
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    
    /* 硬件加速 */
    will-change: transform !important;
    transform: translateZ(0) !important;
    -webkit-transform: translateZ(0) !important;
    
    /* Header内容布局 */
    .dashboard-header-content {
      display: flex !important;
      align-items: center !important;
      justify-content: space-between !important;
      width: 100% !important;
      min-height: 48px !important;
      gap: 12px !important;
      position: relative !important;
      z-index: 1 !important;
      /* 内容区域从Safe Area边界开始 */
      padding-top: 8px !important;
    }
    
    .dashboard-date-info {
      flex: 1 !important;
      min-width: 0 !important;
      
      .dashboard-title {
        font-size: 16px !important;
        /* ✅ 修复标题间距：从2px减少到1px */
        margin: 0 0 1px 0 !important;  
        color: var(--text-primary) !important;
        font-weight: bold !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        line-height: 1.2 !important;
      }
      
      .dashboard-subtitle {
        font-size: 12px !important;
        margin: 0 !important;
        color: var(--text-secondary) !important;
        line-height: 1.2 !important;
      }
    }
    
    .header-actions {
      flex-shrink: 0 !important;
    }
  }
  
  /* 🎯 主内容区域适配 */
  .main-content {
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
    
    /* 为新的header高度预留空间 */
    padding-top: calc(env(safe-area-inset-top, 44px) + 56px) !important;
    
    /* 为底部导航预留空间 */
    &.with-bottom-nav {
      padding-bottom: calc(70px + env(safe-area-inset-bottom, 0px)) !important;
    }
  }
  
  /* 📄 页面内容区域 */
  .page-content {
    flex: 1 !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    padding: 16px !important;
    min-height: max-content !important;
    height: auto !important;
    position: relative !important;
  }
}

/* ===== 横屏模式适配 ===== */
@media (max-width: 768px) and (orientation: landscape) {
  .page-header {
    height: calc(env(safe-area-inset-top, 20px) + 44px) !important;
    padding-top: env(safe-area-inset-top, 20px) !important;
    padding-bottom: 6px !important;
    
    .dashboard-header-content {
      padding-top: 6px !important;
      min-height: 38px !important;
    }
  }
  
  .main-content {
    padding-top: calc(env(safe-area-inset-top, 20px) + 44px) !important;
  }
}

/* ===== 超小屏幕优化 ===== */
@media (max-width: 480px) {
  .page-header {
    padding-left: 12px !important;
    padding-right: 12px !important;
    
    .dashboard-header-content {
      gap: 8px !important;
    }
    
    .dashboard-date-info .dashboard-title {
      font-size: 14px !important;
    }
    
    .dashboard-date-info .dashboard-subtitle {
      font-size: 11px !important;
    }
  }
  
  .page-content {
    padding: 12px !important;
  }
}

/* ===== 主题适配 ===== */
@media (max-width: 768px) {
  /* 浅色主题 */
  .theme-light {
    .page-header {
      background: #ffffff !important;
      border-bottom-color: #e2e8f0 !important;
      
      .dashboard-title {
        color: #1e293b !important;
      }
      
      .dashboard-subtitle {
        color: #475569 !important;
      }
    }
  }
  
  /* 暗色主题 */
  .theme-dark {
    .page-header {
      background: #0f172a !important;
      border-bottom-color: #374151 !important;
      
      .dashboard-title {
        color: #f8fafc !important;
      }
      
      .dashboard-subtitle {
        color: #cbd5e1 !important;
      }
    }
  }
}

/* ===== 调试模式 ===== */
.debug-ios-layout-fix {
  @media (max-width: 768px) {
    .page-header {
      background: rgba(0, 255, 0, 0.3) !important;
      border: 2px solid lime !important;
      
      &::before {
        content: 'Header + Status Bar (Fixed)' !important;
        position: absolute !important;
        top: 2px !important;
        left: 2px !important;
        color: black !important;
        font-size: 10px !important;
        font-weight: bold !important;
        background: rgba(255, 255, 255, 0.8) !important;
        padding: 2px 4px !important;
        border-radius: 2px !important;
        z-index: 999 !important;
      }
    }
  }
}
