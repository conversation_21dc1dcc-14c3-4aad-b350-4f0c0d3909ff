// HeroUI 组件样式修复
// 确保HeroUI组件在我们的应用中正确显示

// Avatar 组件样式
[data-heroui-component="avatar"] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  
  &[data-size="md"] {
    width: 40px;
    height: 40px;
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// Button 组件样式修复
[data-heroui-component="button"] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  outline: none;
  border: none;
  background: transparent;
  
  // bordered 变体样式
  &[data-variant="bordered"] {
    background: transparent !important;
    
    // 未关注状态 - 蓝色边框
    &.not-following {
      border: 2px solid var(--accent-500, #3b82f6) !important;
      color: var(--accent-500, #3b82f6) !important;
      
      &:hover {
        background: rgba(59, 130, 246, 0.1) !important;
        border-color: var(--accent-600, #2563eb) !important;
      }
    }
    
    // 已关注状态 - 灰色边框
    &.following {
      border: 2px solid var(--primary-400, #94a3b8) !important;
      color: var(--primary-600, #475569) !important;
      
      &:hover {
        background: rgba(148, 163, 184, 0.1) !important;
        border-color: var(--primary-500, #64748b) !important;
      }
    }
    
    // 默认状态（向后兼容）
    &:not(.not-following):not(.following) {
      border: 2px solid var(--border-color, #d4d4d8) !important;
      color: var(--text-primary, #000000) !important;
      
      &:hover {
        border-color: var(--accent-500, #3b82f6) !important;
        background: var(--bg-hover, rgba(59, 130, 246, 0.1)) !important;
      }
    }
  }
  
  // 小尺寸样式
  &[data-size="sm"] {
    padding: 6px 12px;
    font-size: 14px;
    min-height: 32px;
    border-radius: 6px;
  }
}

// Divider 组件样式修复
[data-heroui-component="divider"] {
  &[data-orientation="vertical"] {
    width: 1px !important;
    min-height: 1em;
    background: var(--border-color, #e5e7eb) !important;
    border: none;
    margin: 0;
    display: inline-block;
    vertical-align: middle;
    
    // 确保在白色背景下可见
    &::before {
      content: '';
      display: block;
      width: 100%;
      height: 100%;
      background: var(--border-color, #d1d5db);
    }
  }
}

// 如果HeroUI组件没有data属性，使用类名选择器作为备选
.heroui-avatar,
.avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  width: 40px;
  height: 40px;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.heroui-button,
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  outline: none;
  border: 2px solid var(--border-color, #d4d4d8);
  background: transparent;
  color: var(--text-primary, #000000);
  padding: 6px 12px;
  font-size: 14px;
  min-height: 32px;
  border-radius: 6px;
  
  &:hover {
    border-color: var(--accent-500, #3b82f6);
    background: var(--bg-hover, rgba(59, 130, 246, 0.1));
  }
}

.heroui-divider,
.divider {
  &.vertical {
    width: 1px;
    min-height: 30px;
    background: var(--border-color, #d1d5db);
    border: none;
    margin: 0;
    display: inline-block;
    vertical-align: middle;
  }
} 