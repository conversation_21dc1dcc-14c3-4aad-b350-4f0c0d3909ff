// SCSS Variables for FitMaster
// These complement the CSS custom properties in design-system.css

// Color variable mappings for unified naming
// Maps the design-system.css variables to consistent naming
:root {
  // 统一颜色变量命名
  --color-primary: var(--accent-500);
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-background-primary: var(--bg-primary);
  --color-background-secondary: var(--bg-secondary);
  --color-background-tertiary: var(--bg-tertiary);
}

// Breakpoints
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// Z-index scale
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;
$z-toast: 1080;

// Mixins
@mixin respond-to($breakpoint) {
  @if $breakpoint == sm {
    @media (min-width: $breakpoint-sm) { @content; }
  }
  @if $breakpoint == md {
    @media (min-width: $breakpoint-md) { @content; }
  }
  @if $breakpoint == lg {
    @media (min-width: $breakpoint-lg) { @content; }
  }
  @if $breakpoint == xl {
    @media (min-width: $breakpoint-xl) { @content; }
  }
  @if $breakpoint == 2xl {
    @media (min-width: $breakpoint-2xl) { @content; }
  }
}

// 响应式断点 mixins
@mixin mobile {
  @media (max-width: #{$breakpoint-md - 1px}) { @content; }
}

@mixin tablet {
  @media (min-width: $breakpoint-md) and (max-width: #{$breakpoint-lg - 1px}) { @content; }
}

@mixin desktop {
  @media (min-width: $breakpoint-lg) { @content; }
}

@mixin card-hover {
  transition: all var(--transition-normal) var(--ease-in-out);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1);
  }
}

@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  transition: all var(--transition-normal) var(--ease-in-out);
  cursor: pointer;
  border: none;
  text-decoration: none;
  
  &:focus {
    outline: 2px solid var(--accent-500);
    outline-offset: 2px;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
} 