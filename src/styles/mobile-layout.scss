/**
 * 🎯 iOS移动端布局修复 - 环境自适应版本
 * 保持原有视觉设计，最小影响修复核心问题
 * 
 * 核心修复策略:
 * 1. 修复页面滚动异常问题
 * 2. 修复iOS端Header间隙问题（包含Safe Area优化）
 * 3. 修复聊天按钮层级遮挡问题
 * 4. 保持原有布局设计不变
 */

/* ===== iOS移动端布局修复 - 环境自适应版本 ===== */
@media (max-width: 768px) {
  
  /* 🔧 基础视口设置 - 解决页面滚动问题 */
  html {
    height: 100vh;
    height: 100dvh;
    overflow: hidden;
    background: var(--bg-primary);
  }
  
  body {
    height: 100vh;
    height: 100dvh;
    overflow: hidden;
    margin: 0;
    padding: 0;
    background: var(--bg-primary);
    
    /* iOS Safari特殊处理 */
    @supports (-webkit-touch-callout: none) {
      height: -webkit-fill-available;
    }
  }
  
  #root {
    height: 100vh;
    height: 100dvh;
    overflow: hidden;
  }
  
  /* 📱 主布局容器 - Flex布局核心 */
  .layout.mobile-layout {
    height: 100vh;
    height: 100dvh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--bg-primary);
  }

  /* 🌐 Web端布局 - 默认非iOS环境 */
  .page-header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: var(--z-fixed) !important; /* 1030 */
    
    /* ✅ Web端：固定高度，不依赖Safe Area */
    height: 50px !important;
    min-height: 50px !important;
    padding-top: 0 !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
    padding-bottom: 4px !important;
    
    /* 主题响应背景 */
    background: var(--bg-primary) !important;
    border-bottom: 1px solid var(--border-color) !important;
    
    /* Web端毛玻璃效果 */
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    
    /* 硬件加速 */
    will-change: transform !important;
    transform: translateZ(0) !important;
    
    /* Web端Header内容布局 */
    .dashboard-header-content {
      display: flex !important;
      align-items: center !important;
      justify-content: space-between !important;
      width: 100% !important;
      min-height: 44px !important;
      gap: 12px !important;
      position: relative !important;
      z-index: 1 !important;
      /* ✅ Web端：标准顶部间距 */
      padding-top: 8px !important;
    }
    
    .dashboard-date-info {
      flex: 1 !important;
      min-width: 0 !important;
      
      .dashboard-title {
        font-size: 16px !important;
        margin: 0 0 1px 0 !important;
        color: var(--text-primary) !important;
        font-weight: bold !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        line-height: 1.2 !important;
      }
      
      .dashboard-subtitle {
        font-size: 12px !important;
        margin: 0 !important;
        color: var(--text-secondary) !important;
        line-height: 1.2 !important;
      }
    }
    
    .header-actions {
      flex-shrink: 0 !important;
    }
  }
  
  /* 🎯 Web端主内容区域 */
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    /* ✅ Web端：对应50px固定Header高度 */
    padding-top: 50px !important;
    
    /* ✅ Web端底部导航预留 */
    &.with-bottom-nav {
      padding-bottom: 60px;
    }
  }
  
  /* 📄 页面内容区域 */
  .page-content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 16px;
    min-height: max-content;
    height: auto;
    position: relative;
  }

  /* 🔻 底部导航 - 环境自适应 */
  .bottom-navigation {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: var(--z-sticky); /* 1020 */
    
    /* ✅ Web端：固定高度 */
    height: 60px;
    
    background: var(--bg-surface);
    border-top: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    
    will-change: transform;
    transform: translateZ(0);
    
    .bottom-nav-container {
      display: flex;
      height: 50px;
      align-items: center;
      justify-content: space-around;
      padding: 0 var(--space-2, 0.5rem);
      position: relative;
      margin-top: 5px;
    }
    
    .bottom-nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--space-2, 0.5rem);
      background: none;
      border: none;
      color: var(--text-secondary);
      cursor: pointer;
      border-radius: var(--radius-md, 6px);
      transition: all 0.2s ease;
      position: relative;
      min-width: 60px;
      gap: 0.25rem;
      flex: 1;
      max-width: 80px;
      min-height: 44px;
      
      &:hover:not(.active) {
        color: var(--text-primary);
      }
      
      &.active {
        color: var(--accent-500);
        
        .bottom-nav-icon .hn {
          transform: scale(1.1);
          color: var(--accent-500);
        }
      }
    }
    
    .bottom-nav-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 4px;
      
      .hn {
        transition: all var(--transition-normal);
        font-size: 18px;
        width: 22px;
        height: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    
    .bottom-nav-label {
      font-size: 0.75rem;
      font-weight: 500;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }
  }
  
  /* 💬 聊天按钮 - Web端适配 */
  .dashboard-v2__chat-button {
    position: fixed;
    right: 16px;
    z-index: var(--z-popover); /* 1060 */
    
    /* ✅ Web端：适配60px底部导航 */
    bottom: calc(60px + 16px);
    
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);
    transition: all 0.3s ease;
    min-height: 44px;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 32px rgba(79, 70, 229, 0.4);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

/* 🍎 iOS端布局 - Safe Area环境自适应 */
@supports (-webkit-touch-callout: none) {
  @media (max-width: 768px) {
    
    /* 🔝 iOS端Header - 上移+垂直居中优化 */
    .page-header {
      /* ✅ 高度调整：上下各增加5px，总共+10px */
      height: calc(env(safe-area-inset-top, 44px) + 38px) !important;
      min-height: calc(env(safe-area-inset-top, 44px) + 38px) !important;
      padding-top: env(safe-area-inset-top, 44px) !important;
      /* ✅ 移除padding-bottom，最小化空间 */
      
      /* iOS原生状态栏融合效果 */
      background: var(--bg-primary) !important;
      
      .dashboard-header-content {
        /* ✅ 高度调整：从28px增加到38px，给icon更多空间 */
        height: 38px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        width: 100% !important;
        gap: 12px !important;
        position: relative !important;
        z-index: 1 !important;
        /* ✅ 保持垂直居中，无padding-top */
        padding-top: 0 !important;
      }
    }
    
    /* 🎯 iOS端主内容区域 - 适配调整后的Header */
    .main-content {
      /* ✅ 高度适配：对应增加的Header高度 */
      padding-top: calc(env(safe-area-inset-top, 44px) + 38px) !important;
      
      &.with-bottom-nav {
        /* ✅ iOS端：Safe Area适应的底部预留 */
        padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px));
      }
    }
    
    /* 🔻 iOS端底部导航 - Safe Area适应 */
    .bottom-navigation {
      /* ✅ iOS端：只计算一次Safe Area */
      height: calc(60px + env(safe-area-inset-bottom, 0px)) !important;
    }
    
    /* 💬 iOS端聊天按钮适配 */
    .dashboard-v2__chat-button {
      bottom: calc(60px + env(safe-area-inset-bottom, 0px) + 16px) !important;
    }
  }
  
  /* 📱 iPhone SE / 小屏设备优化 (375px及以下) */
  @media (max-width: 375px) {
    .page-header {
      /* ✅ 小屏高度调整：从26px增加到36px */
      height: calc(env(safe-area-inset-top, 44px) + 36px) !important;
      
      .dashboard-header-content {
        height: 36px !important;
        /* 垂直居中保持 */
        
        .dashboard-title {
          font-size: 15px !important;
        }
        
        .dashboard-subtitle {
          font-size: 11px !important;
        }
      }
    }
    
    .main-content {
      padding-top: calc(env(safe-area-inset-top, 44px) + 36px) !important;
    }
  }
  
  /* 📱 iPhone 12/13/14 标准尺寸优化 (376px-413px) */
  @media (min-width: 376px) and (max-width: 413px) {
    .page-header {
      height: calc(env(safe-area-inset-top, 44px) + 38px) !important;
      
      .dashboard-header-content {
        height: 38px !important;
        /* 垂直居中保持 */
      }
    }
    
    .main-content {
      padding-top: calc(env(safe-area-inset-top, 44px) + 38px) !important;
    }
  }
  
  /* 📱 iPhone 12/13/14 Plus / Pro Max 大屏优化 (414px-430px) */
  @media (min-width: 414px) and (max-width: 430px) {
    .page-header {
      height: calc(env(safe-area-inset-top, 44px) + 40px) !important;
      
      .dashboard-header-content {
        height: 40px !important;
        /* 垂直居中保持 */
        
        .dashboard-title {
          font-size: 17px !important;
        }
      }
    }
    
    .main-content {
      padding-top: calc(env(safe-area-inset-top, 44px) + 40px) !important;
    }
  }
  
  /* 📱 iPhone 15 Pro Max / 未来大屏设备 (431px及以上) */
  @media (min-width: 431px) {
    .page-header {
      height: calc(env(safe-area-inset-top, 44px) + 42px) !important;
      
      .dashboard-header-content {
        height: 42px !important;
        /* 垂直居中保持 */
        
        .dashboard-title {
          font-size: 17px !important;
        }
        
        .dashboard-subtitle {
          font-size: 13px !important;
        }
      }
    }
    
    .main-content {
      padding-top: calc(env(safe-area-inset-top, 44px) + 42px) !important;
    }
  }
}

/* ===== 横屏模式适配 - 环境自适应 ===== */
@media (max-width: 768px) and (orientation: landscape) {
  
  /* 🌐 Web端横屏模式 */
  .page-header {
    height: 44px !important;
    padding-bottom: 2px !important;
    
    .dashboard-header-content {
      min-height: 38px !important;
      padding-top: 4px !important;
    }
  }
  
  .main-content {
    padding-top: 44px !important;
    
    &.with-bottom-nav {
      padding-bottom: 50px;
    }
  }
  
  .bottom-navigation {
    height: 50px;
    
    .bottom-nav-container {
      height: 40px;
      margin-top: 5px;
    }
    
    .bottom-nav-item {
      padding: var(--space-1, 0.25rem);
      min-height: 36px;
    }
    
    .bottom-nav-label {
      font-size: 0.7rem;
    }
  }
  
  .dashboard-v2__chat-button {
    bottom: calc(50px + 12px);
    padding: 8px 16px;
  }
}

/* 🍎 iOS端横屏模式 - Safe Area适应 */
@supports (-webkit-touch-callout: none) {
  @media (max-width: 768px) and (orientation: landscape) {
    .page-header {
      /* ✅ 横屏高度调整：从24px增加到34px，上下各+5px */
      height: calc(env(safe-area-inset-top, 20px) + 34px) !important;
      padding-top: env(safe-area-inset-top, 20px) !important;
      /* ✅ 移除padding-bottom，最小化空间 */
      
      .dashboard-header-content {
        /* ✅ 横屏内容高度调整：从24px增加到34px，给icon更多空间 */
        height: 34px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        padding-top: 0 !important;
      }
    }
    
    .main-content {
      /* ✅ 横屏适配：对应增加的Header高度 */
      padding-top: calc(env(safe-area-inset-top, 20px) + 34px) !important;
      
      &.with-bottom-nav {
        padding-bottom: calc(50px + env(safe-area-inset-bottom, 0px));
      }
    }
    
    .bottom-navigation {
      height: calc(50px + env(safe-area-inset-bottom, 0px)) !important;
    }
    
    .dashboard-v2__chat-button {
      bottom: calc(50px + env(safe-area-inset-bottom, 0px) + 12px) !important;
    }
  }
}

/* ===== 超小屏幕优化 - 环境自适应 ===== */
@media (max-width: 480px) {
  
  /* 🌐 Web端超小屏优化 */
  .page-header {
    padding-left: 12px !important;
    padding-right: 12px !important;
    
    .dashboard-header-content {
      gap: 8px !important;
    }
    
    .dashboard-date-info .dashboard-title {
      font-size: 14px !important;
    }
    
    .dashboard-date-info .dashboard-subtitle {
      font-size: 11px !important;
    }
  }
  
  .page-content {
    padding: 12px;
  }
  
  .bottom-navigation {
    .bottom-nav-item {
      min-width: 50px;
      padding: var(--space-1, 0.25rem);
      
      .bottom-nav-icon .hn {
        font-size: 16px;
        width: 20px;
        height: 20px;
      }
    }
    
    .bottom-nav-label {
      font-size: 0.65rem;
    }
  }
  
  .dashboard-v2__chat-button {
    right: 12px;
    padding: 10px 16px;
    font-size: 13px;
    
    .chat-icon {
      font-size: 14px;
    }
  }
}

/* ===== 主题适配 - 环境自适应 ===== */
@media (max-width: 768px) {
  /* 浅色主题 */
  .theme-light {
    .page-header {
      background: #ffffff !important;
      border-bottom-color: #e2e8f0 !important;
      
      .dashboard-title {
        color: #1e293b !important;
      }
      
      .dashboard-subtitle {
        color: #475569 !important;
      }
    }
    
    .bottom-navigation {
      background: #ffffff;
      border-top-color: #e2e8f0;
      
      .bottom-nav-item {
        color: #6b7280;
        
        &:hover:not(.active) {
          color: #1e293b;
        }
        
        &.active {
          color: var(--accent-500);
        }
      }
    }
  }
  
  /* 暗色主题 */
  .theme-dark {
    .page-header {
      background: #0f172a !important;
      border-bottom-color: #374151 !important;
      
      .dashboard-title {
        color: #f8fafc !important;
      }
      
      .dashboard-subtitle {
        color: #cbd5e1 !important;
      }
    }
    
    .bottom-navigation {
      background: #1e293b;
      border-top-color: #374151;
      
      .bottom-nav-item {
        color: #cbd5e1;
        
        &:hover:not(.active) {
          color: #f8fafc;
        }
        
        &.active {
          color: var(--accent-500);
        }
      }
    }
  }
}

/* ===== 调试模式 - 环境自适应（可选开启） ===== */
.debug-ios-layout-fix {
  @media (max-width: 768px) {
    .page-header {
      background: rgba(0, 255, 0, 0.2);
      border: 1px solid lime;
      
      &::before {
        content: 'Header (Web固定50px)';
        position: absolute;
        top: 2px;
        left: 2px;
        color: black;
        font-size: 10px;
        font-weight: bold;
        background: rgba(255, 255, 255, 0.9);
        padding: 2px 4px;
        border-radius: 2px;
        z-index: 999;
      }
    }
    
    .main-content {
      background: rgba(0, 255, 0, 0.05);
      border: 1px solid green;
    }
    
    .page-content {
      background: rgba(0, 0, 255, 0.05);
      border: 1px solid blue;
    }
    
    .bottom-navigation {
      background: rgba(255, 255, 0, 0.2);
      border: 1px solid yellow;
      
      &::before {
        content: 'Navigation (环境适应)';
        position: absolute;
        top: 2px;
        left: 2px;
        color: black;
        font-size: 10px;
        font-weight: bold;
        background: rgba(255, 255, 255, 0.9);
        padding: 2px 4px;
        border-radius: 2px;
        z-index: 999;
      }
    }
    
    .dashboard-v2__chat-button {
      background: rgba(255, 0, 255, 0.3);
      border: 1px solid magenta;
      
      &::before {
        content: 'Chat (Multi-Env)';
        position: absolute;
        top: -18px;
        left: 0;
        color: black;
        font-size: 8px;
        background: rgba(255, 255, 255, 0.9);
        padding: 1px 3px;
        border-radius: 2px;
        white-space: nowrap;
      }
    }
  }
  
  /* iOS端调试信息 */
  @supports (-webkit-touch-callout: none) {
    @media (max-width: 768px) {
      .page-header::before {
        content: 'Header (高度已调整+居中)' !important;
      }
      
      .bottom-navigation::before {
        content: 'Navigation (iOS适应)' !important;
      }
    }
  }
}

/* ===== 辅助功能支持 ===== */
@media (max-width: 768px) {
  /* 减少动画模式支持 */
  @media (prefers-reduced-motion: reduce) {
    .page-header,
    .bottom-navigation,
    .dashboard-v2__chat-button {
      transition: none;
      backdrop-filter: none;
      -webkit-backdrop-filter: none;
    }
  }
  
  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    .page-header {
      border-bottom-width: 2px;
    }
    
    .bottom-navigation {
      border-top-width: 2px;
    }
  }
}