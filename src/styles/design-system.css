/* FitMaster Design System Variables */
/* Modern fitness application design system */

:root {
  /* === Color System === */
  
  /* Primary Colors - Deep Blue/Navy */
  --primary-900: #1a1a2e;       /* Deepest background */
  --primary-800: #16213e;       /* Main background */
  --primary-700: #0f172a;       /* Sidebar background */
  --primary-600: #1e293b;       /* Card background */
  --primary-500: #334155;       /* Border color */
  --primary-400: #475569;       /* Light border */
  --primary-300: #64748b;       /* Lighter border */
  --primary-200: #94a3b8;       /* Very light border */
  --primary-100: #cbd5e1;       /* Extremely light */
  
  /* Accent Colors - Blue */
  --accent-500: #3b82f6;        /* Primary blue */
  --accent-400: #60a5fa;        /* Hover blue */
  --accent-300: #93c5fd;        /* Light blue */
  --accent-200: #dbeafe;        /* Very light blue */
  --accent-100: #eff6ff;        /* Extremely light blue */
  
  /* Success Colors - Green */
  --success-500: #22c55e;       /* Success green */
  --success-400: #4ade80;       /* Light success green */
  --success-300: #86efac;       /* Very light success green */
  --success-200: #bbf7d0;       /* Extremely light success */
  --success-100: #dcfce7;       /* Lightest success */
  
  /* Warning Colors - Orange */
  --warning-500: #f59e0b;       /* Warning orange */
  --warning-400: #fbbf24;       /* Light warning orange */
  --warning-300: #fcd34d;       /* Very light warning */
  --warning-200: #fde68a;       /* Extremely light warning */
  --warning-100: #fef3c7;       /* Lightest warning */
  
  /* Error Colors - Red */
  --error-500: #ef4444;         /* Error red */
  --error-400: #f87171;         /* Light error red */
  --error-300: #fca5a5;         /* Very light error */
  --error-200: #fecaca;         /* Extremely light error */
  --error-100: #fee2e2;         /* Lightest error */
  
  /* Text Colors */
  --text-primary: #f8fafc;      /* Primary text - white */
  --text-secondary: #cbd5e1;    /* Secondary text - light gray */
  --text-tertiary: #94a3b8;     /* Tertiary text - gray */
  --text-disabled: #64748b;     /* Disabled text - dark gray */
  --text-on-primary: #ffffff;   /* Text on primary colors */
  --text-on-accent: #ffffff;    /* Text on accent colors */
  --text-on-success: #ffffff;   /* Text on success colors */
  --text-on-warning: #ffffff;   /* Text on warning colors */
  --text-on-error: #ffffff;     /* Text on error colors */
  
  /* Background Colors */
  --bg-primary: #0f172a;        /* Main background */
  --bg-secondary: #1e293b;      /* Secondary background */
  --bg-tertiary: #334155;       /* Tertiary background */
  --bg-surface: #1e293b;        /* Surface background */
  --bg-card: #374151;           /* Card background - darker */
  --bg-hover: #4b5563;          /* Hover background */
  --bg-overlay: rgba(15, 23, 42, 0.8); /* Overlay background */
  
  /* Border Colors */
  --border-color: #374151;      /* Default border */
  --border-light: #f3f4f6;      /* Light border */
  
  /* Component Colors */
  --color-primary: #3b82f6;     /* Primary component color */
  --color-success: #22c55e;     /* Success component color */
  --color-warning: #f59e0b;     /* Warning component color */
  --color-danger: #ef4444;      /* Danger component color */
  
  /* === Typography System === */
  
  /* Font Families */
  --font-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  --font-display: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  
  /* Font Sizes */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  --text-4xl: 2.25rem;     /* 36px */
  --text-5xl: 3rem;        /* 48px */
  --text-6xl: 3.75rem;     /* 60px */
  
  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;
  
  /* Line Heights */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* === Spacing System === */
  /* Based on 8px grid system */
  --space-0: 0;
  --space-1: 0.25rem;      /* 4px */
  --space-2: 0.5rem;       /* 8px */
  --space-3: 0.75rem;      /* 12px */
  --space-4: 1rem;         /* 16px */
  --space-5: 1.25rem;      /* 20px */
  --space-6: 1.5rem;       /* 24px */
  --space-7: 1.75rem;      /* 28px */
  --space-8: 2rem;         /* 32px */
  --space-9: 2.25rem;      /* 36px */
  --space-10: 2.5rem;      /* 40px */
  --space-11: 2.75rem;     /* 44px */
  --space-12: 3rem;        /* 48px */
  --space-14: 3.5rem;      /* 56px */
  --space-16: 4rem;        /* 64px */
  --space-20: 5rem;        /* 80px */
  --space-24: 6rem;        /* 96px */
  --space-28: 7rem;        /* 112px */
  --space-32: 8rem;        /* 128px */
  
  /* === Border Radius === */
  --radius-none: 0;
  --radius-sm: 0.125rem;    /* 2px */
  --radius-md: 0.375rem;    /* 6px */
  --radius-lg: 0.5rem;      /* 8px */
  --radius-xl: 0.75rem;     /* 12px */
  --radius-2xl: 1rem;       /* 16px */
  --radius-3xl: 1.5rem;     /* 24px */
  --radius-full: 9999px;    /* Full rounded */
  
  /* === Shadows === */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* === Animation === */
  /* Transition Durations */
  --transition-fast: 0.15s;
  --transition-normal: 0.2s;
  --transition-slow: 0.3s;
  --transition-slower: 0.5s;
  
  /* Easing Functions */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* === Layout === */
  /* Container Sizes */
  --container-xs: 20rem;     /* 320px */
  --container-sm: 24rem;     /* 384px */
  --container-md: 28rem;     /* 448px */
  --container-lg: 32rem;     /* 512px */
  --container-xl: 36rem;     /* 576px */
  --container-2xl: 42rem;    /* 672px */
  --container-3xl: 48rem;    /* 768px */
  --container-4xl: 56rem;    /* 896px */
  --container-5xl: 64rem;    /* 1024px */
  --container-6xl: 72rem;    /* 1152px */
  --container-7xl: 80rem;    /* 1280px */
  --container-full: 100%;
  
  /* Sidebar */
  --sidebar-width: 16rem;    /* 256px */
  --sidebar-width-collapsed: 4rem; /* 64px */
  
  /* Header */
  --header-height: 4rem;     /* 64px */
  --header-height-mobile: 3.5rem; /* 56px */
  
  /* === Breakpoints === */
  --breakpoint-sm: 640px;    /* Mobile landscape */
  --breakpoint-md: 768px;    /* Tablet */
  --breakpoint-lg: 1024px;   /* Desktop */
  --breakpoint-xl: 1280px;   /* Large desktop */
  --breakpoint-2xl: 1536px;  /* Extra large desktop */
  
  /* === Z-Index Scale === */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* === Fitness Specific === */
  /* Progress Ring Colors */
  --progress-bg: var(--primary-600);
  --progress-calories: var(--error-500);
  --progress-exercise: var(--accent-500);
  --progress-move: var(--success-500);
  
  /* Achievement Rarities */
  --rarity-common: var(--text-tertiary);
  --rarity-rare: var(--accent-500);
  --rarity-epic: var(--warning-500);
  --rarity-legendary: linear-gradient(45deg, var(--warning-500), var(--error-500));
  
  /* Workout Status Colors */
  --status-completed: var(--success-500);
  --status-in-progress: var(--accent-500);
  --status-planned: var(--warning-500);
  --status-skipped: var(--text-tertiary);
  
  /* === Data Visualization === */
  /* Chart Colors */
  --chart-primary: var(--accent-500);
  --chart-secondary: var(--success-500);
  --chart-tertiary: var(--warning-500);
  --chart-quaternary: var(--error-500);
  --chart-quinary: var(--primary-300);
  --chart-grid: var(--primary-500);
  --chart-text: var(--text-secondary);
  --chart-axis: var(--text-tertiary);
  
  /* === Interactive States === */
  /* Focus Ring */
  --focus-ring: 0 0 0 2px var(--accent-500);
  --focus-ring-offset: 2px;
  
  /* Selection */
  --selection-bg: rgba(59, 130, 246, 0.2);
  --selection-color: var(--text-primary);
  
  /* === Glass Effect === */
  --glass-bg: rgba(30, 41, 59, 0.8);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-blur: blur(10px);
  
  /* === Gradients === */
  --gradient-primary: linear-gradient(135deg, var(--accent-500) 0%, var(--accent-400) 100%);
  --gradient-surface: linear-gradient(135deg, var(--bg-surface) 0%, var(--primary-600) 100%);
  --gradient-success: linear-gradient(135deg, var(--success-500) 0%, var(--success-400) 100%);
  --gradient-warning: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-400) 100%);
  --gradient-error: linear-gradient(135deg, var(--error-500) 0%, var(--error-400) 100%);
  --gradient-brand: linear-gradient(90deg, var(--accent-500), var(--success-500));
}

/* === Light Theme === */
.theme-light {
  /* Primary Colors - Light Gray */
  --primary-900: #f8fafc;       /* Lightest background */
  --primary-800: #f1f5f9;       /* Very light background */
  --primary-700: #ffffff;       /* Sidebar background */
  --primary-600: #f8fafc;       /* Card background */
  --primary-500: #e2e8f0;       /* Border color */
  --primary-400: #cbd5e1;       /* Light border */
  --primary-300: #94a3b8;       /* Lighter border */
  --primary-200: #64748b;       /* Very light border */
  --primary-100: #475569;       /* Extremely light */
  
  /* Text Colors - Dark for light theme */
  --text-primary: #1e293b;      /* Primary text - dark */
  --text-secondary: #475569;    /* Secondary text - medium gray */
  --text-tertiary: #64748b;     /* Tertiary text - light gray */
  --text-disabled: #94a3b8;     /* Disabled text - very light gray */
  --text-on-primary: #ffffff;   /* Text on primary colors */
  --text-on-accent: #ffffff;    /* Text on accent colors */
  --text-on-success: #ffffff;   /* Text on success colors */
  --text-on-warning: #ffffff;   /* Text on warning colors */
  --text-on-error: #ffffff;     /* Text on error colors */
  
  /* Background Colors */
  --bg-primary: #ffffff;        /* Main background */
  --bg-secondary: #f8fafc;      /* Secondary background */
  --bg-tertiary: #f1f5f9;       /* Tertiary background */
  --bg-surface: #ffffff;        /* Surface background */
  --bg-card: #f8fafc;           /* Card background - light gray */
  --bg-hover: #f1f5f9;          /* Hover background */
  --bg-overlay: rgba(0, 0, 0, 0.1); /* Overlay background */
  
  /* Border Colors */
  --border-color: #e2e8f0;      /* Default border */
  --border-light: #f3f4f6;      /* Light border */
  
  /* Component Colors - same for light theme */
  --color-primary: #3b82f6;     /* Primary component color */
  --color-success: #22c55e;     /* Success component color */
  --color-warning: #f59e0b;     /* Warning component color */
  --color-danger: #ef4444;      /* Danger component color */
  
  /* Shadows for light theme */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.15), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.15), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.08);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.3);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  
  /* Selection for light theme */
  --selection-bg: rgba(59, 130, 246, 0.15);
  --selection-color: var(--text-primary);
  
  /* Glass effect for light theme */
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(0, 0, 0, 0.1);
  
  /* Gradients for light theme */
  --gradient-surface: linear-gradient(135deg, var(--bg-surface) 0%, var(--primary-600) 100%);
}

/* === Base Styles === */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* === Selection Styles === */
::selection {
  background-color: var(--selection-bg);
  color: var(--selection-color);
}

::-moz-selection {
  background-color: var(--selection-bg);
  color: var(--selection-color);
}

/* === Focus Styles === */
:focus {
  outline: 2px solid var(--accent-500);
  outline-offset: var(--focus-ring-offset);
}

:focus:not(:focus-visible) {
  outline: none;
}

/* === Scrollbar Styles === */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--primary-600);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-500);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-500);
}

/* === Accessibility === */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  html {
    scroll-behavior: auto;
  }
}

@media (prefers-contrast: high) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
    --bg-primary: #000000;
    --bg-secondary: #1a1a1a;
    --primary-500: #ffffff;
  }
}

/* === Print Styles === */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }
  
  blockquote, pre {
    page-break-inside: avoid;
  }
  
  tr, img {
    page-break-inside: avoid;
  }
  
  img {
    max-width: 100% !important;
  }
  
  @page {
    margin: 0.5in;
  }
  
  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }
} 