/**
 * 认证服务测试
 */

import { AuthService } from '../authService';

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage
});

// Mock fetch
global.fetch = jest.fn();

describe('AuthService', () => {
  let authService: AuthService;

  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    
    authService = new AuthService({
      baseUrl: 'http://test-api.com',
      mockMode: true
    });
  });

  describe('loginWithTestUser', () => {
    it('should login successfully in mock mode', async () => {
      const user = await authService.loginWithTestUser();

      expect(user).toMatchObject({
        id: 1,
        email: '<EMAIL>',
        openid: 'oCU0j7Rg9kzigLzquCBje3KfnQXk',
        nickName: '测试用户'
      });

      expect(authService.isAuthenticated()).toBe(true);
      expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(2);
    });

    it('should handle login failure and fallback to mock', async () => {
      const authServiceReal = new AuthService({
        baseUrl: 'http://test-api.com',
        mockMode: false
      });

      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      const user = await authServiceReal.loginWithTestUser();

      expect(user).toMatchObject({
        id: 1,
        email: '<EMAIL>',
        openid: 'oCU0j7Rg9kzigLzquCBje3KfnQXk'
      });
    });
  });

  describe('getAuthHeaders', () => {
    it('should return auth headers when authenticated', async () => {
      await authService.loginWithTestUser();
      
      const headers = authService.getAuthHeaders();

      expect(headers).toHaveProperty('Authorization');
      expect(headers.Authorization).toMatch(/^Bearer /);
      expect(headers).toHaveProperty('X-User-ID', '1');
      expect(headers).toHaveProperty('X-User-OpenID', 'oCU0j7Rg9kzigLzquCBje3KfnQXk');
    });

    it('should throw error when not authenticated', () => {
      expect(() => authService.getAuthHeaders()).toThrow('用户未登录');
    });
  });

  describe('isTokenValid', () => {
    it('should return true for valid token', async () => {
      await authService.loginWithTestUser();
      
      expect(authService.isTokenValid()).toBe(true);
    });

    it('should return false for expired token', async () => {
      await authService.loginWithTestUser();
      
      // 手动设置过期时间为过去
      (authService as any).tokens.expiresAt = new Date(Date.now() - 1000).toISOString();
      
      expect(authService.isTokenValid()).toBe(false);
    });
  });

  describe('logout', () => {
    it('should clear auth data on logout', async () => {
      await authService.loginWithTestUser();
      
      expect(authService.isAuthenticated()).toBe(true);
      
      authService.logout();
      
      expect(authService.isAuthenticated()).toBe(false);
      expect(authService.getCurrentUser()).toBe(null);
      expect(mockLocalStorage.removeItem).toHaveBeenCalledTimes(2);
    });
  });

  describe('refreshTokenIfNeeded', () => {
    it('should not refresh valid token', async () => {
      await authService.loginWithTestUser();
      
      const originalToken = authService.getAuthHeaders().Authorization;
      
      await authService.refreshTokenIfNeeded();
      
      expect(authService.getAuthHeaders().Authorization).toBe(originalToken);
    });

    it('should refresh expired token', async () => {
      await authService.loginWithTestUser();
      
      // 设置token为过期
      (authService as any).tokens.expiresAt = new Date(Date.now() - 1000).toISOString();
      
      await authService.refreshTokenIfNeeded();
      
      // 应该重新登录并获得新token
      expect(authService.isAuthenticated()).toBe(true);
    });
  });

  describe('storage operations', () => {
    it('should load stored auth on initialization', () => {
      const mockTokens = JSON.stringify({
        accessToken: 'stored_token',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      });
      
      const mockUser = JSON.stringify({
        id: 1,
        email: '<EMAIL>',
        openid: 'oCU0j7Rg9kzigLzquCBje3KfnQXk',
        nickName: '存储用户'
      });

      mockLocalStorage.getItem
        .mockReturnValueOnce(mockTokens)
        .mockReturnValueOnce(mockUser);

      const newAuthService = new AuthService({ mockMode: true });

      expect(newAuthService.isAuthenticated()).toBe(true);
      expect(newAuthService.getCurrentUser()?.nickName).toBe('存储用户');
    });

    it('should handle corrupted storage data', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid json');

      expect(() => new AuthService({ mockMode: true })).not.toThrow();
    });
  });
});
