/**
 * Exercise数据服务模块
 * 提供统一的动作数据管理，包含缓存机制、分页加载、筛选逻辑
 */

import { apiService } from './api';
import { 
  ApiExerciseResponse, 
  Exercise, 
  ExerciseSearchParams, 
  mapApiExercisesToUIExercises 
} from '../utils/exerciseDataMapper';
import { BODY_PART_CATEGORIES, EQUIPMENT_CATEGORIES } from '../constants/exerciseCategories';

// 缓存配置
const CACHE_KEY_PREFIX = 'exercises';
const CACHE_EXPIRY_MS = 10 * 60 * 1000; // 10分钟缓存
const PAGE_SIZE = 20;

// 筛选条件接口
export interface ExerciseFilters {
  bodyPartId?: number;
  equipmentId?: number;
  searchTerm?: string;
  favoritesOnly?: boolean;
}

// 分页数据接口
export interface ExercisePage {
  exercises: Exercise[];
  hasMore: boolean;
  total: number;
  currentPage: number;
}

// 缓存数据结构
interface CachedExerciseData {
  exercises: Exercise[];
  hasMore: boolean;
  total: number;
  timestamp: number;
  filters: ExerciseFilters;
}

class ExerciseService {
  private cache = new Map<string, CachedExerciseData>();
  private readonly cacheExpiry = CACHE_EXPIRY_MS;
  private readonly pageSize = PAGE_SIZE;

  /**
   * 生成缓存键
   */
  private generateCacheKey(filters: ExerciseFilters, page: number): string {
    const { bodyPartId = 0, equipmentId = 0, searchTerm = '', favoritesOnly = false } = filters;
    return `${CACHE_KEY_PREFIX}_${bodyPartId}_${equipmentId}_${searchTerm}_${favoritesOnly}_${page}`;
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cachedData: CachedExerciseData): boolean {
    return Date.now() - cachedData.timestamp < this.cacheExpiry;
  }

  /**
   * 将UI筛选条件转换为API参数
   */
  private buildApiParams(filters: ExerciseFilters, page: number): ExerciseSearchParams {
    const params: ExerciseSearchParams = {
      skip: page * this.pageSize,
      limit: this.pageSize,
      // 设置默认值，确保API调用包含所有必需参数
      body_part_id: filters.bodyPartId ? [filters.bodyPartId] : [0],
      equipment_id: filters.equipmentId ? [filters.equipmentId] : [0]
    };

    if (filters.searchTerm && filters.searchTerm.trim()) {
      params.search = filters.searchTerm.trim();
    }

    return params;
  }

  /**
   * 根据筛选条件名称获取ID
   */
  public getBodyPartId(bodyPartName: string): number {
    if (bodyPartName === '全部') return 0;
    const bodyPart = BODY_PART_CATEGORIES.find(bp => bp.name === bodyPartName);
    return bodyPart ? bodyPart.id : 0;
  }

  public getEquipmentId(equipmentName: string): number {
    if (equipmentName === '全部') return 0;
    const equipment = EQUIPMENT_CATEGORIES.find(eq => eq.name === equipmentName);
    return equipment ? equipment.id : 0;
  }

  /**
   * 获取动作数据（带缓存）
   */
  public async getExercises(
    filters: ExerciseFilters = {}, 
    page: number = 0,
    forceRefresh: boolean = false
  ): Promise<ExercisePage> {
    const cacheKey = this.generateCacheKey(filters, page);
    
    // 检查缓存
    if (!forceRefresh) {
      const cachedData = this.cache.get(cacheKey);
      if (cachedData && this.isCacheValid(cachedData)) {
        console.log('🎯 使用缓存数据:', { filters, page, cacheKey });
        return {
          exercises: cachedData.exercises,
          hasMore: cachedData.hasMore,
          total: cachedData.total,
          currentPage: page
        };
      }
    }

    // 发起API请求
    const apiParams = this.buildApiParams(filters, page);
    
    console.log('📡 发起API请求:', {
      filters,
      page,
      apiParams,
      cacheKey
    });

    try {
      let apiExercises: ApiExerciseResponse[];
      
      if (filters.searchTerm && filters.searchTerm.trim()) {
        // 搜索请求
        apiExercises = await apiService.searchExercises(filters.searchTerm, apiParams);
      } else {
        // 普通列表请求
        apiExercises = await apiService.getExercises(apiParams);
      }

      const exercises = mapApiExercisesToUIExercises(apiExercises);
      const hasMore = apiExercises.length === this.pageSize;

      // 客户端筛选（收藏功能）
      let filteredExercises = exercises;
      if (filters.favoritesOnly) {
        filteredExercises = exercises.filter(ex => ex.is_favorite);
      }

      // 缓存数据
      const cacheData: CachedExerciseData = {
        exercises: filteredExercises,
        hasMore,
        total: filteredExercises.length, // 实际我们不知道总数，使用当前页数量
        timestamp: Date.now(),
        filters
      };
      
      this.cache.set(cacheKey, cacheData);

      console.log('✅ API请求成功，已缓存:', {
        receivedCount: apiExercises.length,
        filteredCount: filteredExercises.length,
        hasMore,
        cacheKey
      });

      return {
        exercises: filteredExercises,
        hasMore,
        total: filteredExercises.length,
        currentPage: page
      };

    } catch (error) {
      console.error('❌ Exercise API请求失败:', error);
      throw error;
    }
  }

  /**
   * 加载更多数据（追加到现有数据）
   */
  public async loadMore(
    filters: ExerciseFilters = {},
    currentExercises: Exercise[] = []
  ): Promise<{ exercises: Exercise[]; hasMore: boolean }> {
    const currentPage = Math.floor(currentExercises.length / this.pageSize);
    const nextPage = currentPage + 1;

    const result = await this.getExercises(filters, nextPage);
    
    // 合并数据，避免重复
    const existingIds = new Set(currentExercises.map(ex => ex.id));
    const newExercises = result.exercises.filter(ex => !existingIds.has(ex.id));
    
    return {
      exercises: [...currentExercises, ...newExercises],
      hasMore: result.hasMore
    };
  }

  /**
   * 清除缓存
   */
  public clearCache(filters?: ExerciseFilters): void {
    if (filters) {
      // 清除特定筛选条件的缓存
      const pattern = this.generateCacheKey(filters, 0).split('_').slice(0, -1).join('_');
      const keysToDelete = Array.from(this.cache.keys()).filter(key => key.startsWith(pattern));
      keysToDelete.forEach(key => this.cache.delete(key));
      console.log('🗑️ 清除缓存:', { pattern, count: keysToDelete.length });
    } else {
      // 清除所有缓存
      this.cache.clear();
      console.log('🗑️ 清除所有Exercise缓存');
    }
  }

  /**
   * 获取缓存状态信息
   */
  public getCacheStats(): { 
    totalEntries: number; 
    validEntries: number; 
    expiredEntries: number; 
  } {
    let validEntries = 0;
    let expiredEntries = 0;

    this.cache.forEach(data => {
      if (this.isCacheValid(data)) {
        validEntries++;
      } else {
        expiredEntries++;
      }
    });

    return {
      totalEntries: this.cache.size,
      validEntries,
      expiredEntries
    };
  }

  /**
   * 清理过期缓存
   */
  public cleanupExpiredCache(): void {
    const keysToDelete: string[] = [];
    
    this.cache.forEach((data, key) => {
      if (!this.isCacheValid(data)) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => this.cache.delete(key));
    
    if (keysToDelete.length > 0) {
      console.log('🧹 清理过期缓存:', keysToDelete.length);
    }
  }
}

// 导出单例实例
export const exerciseService = new ExerciseService();
export default exerciseService; 