import { Routes, Route } from 'react-router-dom';

// Import pixel-retroui setup for true pixelated effects
import '../pixel-retroui-setup.js';

import { StagewiseToolbar } from '@stagewise/toolbar-react'
import ReactPlugin from '@stagewise-plugins/react'
import Layout from './components/common/Layout'
// import StatusBarManager from './components/common/StatusBarManager' // 已由useUnifiedSystem替代
import WorkoutPage from './pages/WorkoutPage'
import DashboardV2 from './pages/DashboardPage/DashboardV2'
import ProfilePage from './pages/user-profile/ProfilePage'
import FeedPage from './pages/feed/FeedPage'
import RoutinesPage from './pages/routines/RoutinesPage'
import ExercisesPage from './pages/exercises/ExercisesPage'
import ExerciseDetailPage from './pages/exercises/ExerciseDetailPage'
import SettingsPage from './pages/settings/SettingsPage'
import UiTestPage from './pages/ui-test/UiTestPage'
import IconShowcase from './pages/IconShowcase'
import IconUsageGuide from './pages/IconShowcase/IconUsageGuide'
import { MuscleVisualizationDemo } from './pages/MuscleVisualizationDemo'
import EnhancedMuscleVisualizationDemo from './pages/EnhancedMuscleVisualizationDemo'
import ImprovedMuscleDemo from './pages/ImprovedMuscleDemo'
import { useState, useEffect } from 'react'
import StatusBarDebugger from './components/common/StatusBarDebugger'
// 🚀 主题系统（支持浅色/暗色主题切换）
import { useUnifiedSystem } from './hooks'
import './styles/theme-system.scss'

function App() {
  const [dashboardDate, setDashboardDate] = useState<Date>(new Date());

  // 🚀 使用新的iOS主题与布局统一系统（替代旧的initializeiOSLayoutManager）
  const system = useUnifiedSystem();

  // 📋 开发环境调试信息
  useEffect(() => {
    if (import.meta.env.DEV && system.layoutReady) {
      console.log('🎯 App集成统一系统完成:', system.getSystemState());
    }
  }, [system.layoutReady, system]);

  const handleNotificationPress = () => {
    console.log('通知按钮被点击');
  };

  const handleSettingsPress = () => {
    console.log('设置按钮被点击');
  };

  // 🎨 主题切换处理
  const handleThemeToggle = () => {
    system.toggleTheme();
    console.log(`🎨 主题切换至: ${system.theme === 'light' ? 'dark' : 'light'}`);
  };

  return (
    <div className={`app theme-${system.theme}`}>
      {/* 🚀 新的iOS主题与布局统一系统已集成到Layout组件中 */}
      {/* StatusBarManager已由useUnifiedSystem中的useiOSStatusBar替代 */}
      
      {/* 🔍 状态栏调试器 - 开发环境可用 */}
      {process.env.NODE_ENV === 'development' && <StatusBarDebugger />}
      

      
      {/* 🎨 开发环境主题切换按钮 */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ 
          position: 'fixed', 
          top: '60px', 
          right: '16px', 
          zIndex: 9999,
          background: 'var(--bg-primary)',
          border: '2px solid var(--border-color)',
          borderRadius: '50%',
          width: '44px',
          height: '44px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer'
        }}>
          <button 
            onClick={handleThemeToggle}
            style={{ 
              background: 'none', 
              border: 'none', 
              fontSize: '20px',
              cursor: 'pointer'
            }}
          >
            {system.isDark ? '🌞' : '🌙'}
          </button>
        </div>
      )}
      
      <Routes>
        <Route path="/" element={
          <Layout 
            title="仪表盘" 
            headerType="dashboard"
            dashboardDate={dashboardDate}
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <DashboardV2 
              selectedDate={dashboardDate}
              onDateChange={setDashboardDate}
            />
          </Layout>
        } />
        <Route path="/dashboard" element={
          <Layout 
            title="仪表盘" 
            headerType="dashboard"
            dashboardDate={dashboardDate}
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <DashboardV2 
              selectedDate={dashboardDate}
              onDateChange={setDashboardDate}
            />
          </Layout>
        } />
        <Route path="/workout" element={
          <Layout 
            title="训练记录"
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <WorkoutPage />
          </Layout>
        } />
        <Route path="/profile" element={
          <Layout 
            title="个人资料"
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <ProfilePage />
          </Layout>
        } />
        <Route path="/feed" element={
          <Layout 
            title="健身动态"
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <FeedPage />
          </Layout>
        } />
        <Route path="/routines" element={
          <Layout 
            title="训练计划"
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <RoutinesPage />
          </Layout>
        } />
        <Route path="/exercises" element={
          <Layout 
            title="运动库"
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <ExercisesPage />
          </Layout>
        } />
        <Route path="/exercises/:id" element={
          <ExerciseDetailPage />
        } />
        <Route path="/settings" element={
          <Layout 
            title="设置"
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <SettingsPage />
          </Layout>
        } />
        <Route path="/ui-test" element={
          <Layout 
            title="UI库测试"
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <UiTestPage />
          </Layout>
        } />
        <Route path="/icons" element={
          <Layout 
            title="图标库展示"
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <IconShowcase />
          </Layout>
        } />
        <Route path="/icons/guide" element={
          <Layout 
            title="图标使用指南"
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <IconUsageGuide />
          </Layout>
        } />
        <Route path="/muscle-demo" element={
          <Layout 
            title="肌肉可视化演示"
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <MuscleVisualizationDemo />
          </Layout>
        } />
        <Route path="/enhanced-muscle-demo" element={
          <Layout 
            title="增强版肌肉可视化演示"
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <EnhancedMuscleVisualizationDemo />
          </Layout>
        } />
        <Route path="/improved-muscle-demo" element={
          <Layout 
            title="改进版肌肉可视化演示"
            onNotificationPress={handleNotificationPress}
            onSettingsPress={handleSettingsPress}
          >
            <ImprovedMuscleDemo />
          </Layout>
        } />
      </Routes>
      
      <StagewiseToolbar 
        config={{
          plugins: [ReactPlugin]
        }}
      />
    </div>
  )
}

export default App 