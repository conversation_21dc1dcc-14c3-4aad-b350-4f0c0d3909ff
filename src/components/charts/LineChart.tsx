import React from 'react';
import './LineChart.scss';

interface DataPoint {
  date: string;
  value: number;
  label?: string;
}

interface LineChartProps {
  data: DataPoint[];
  width?: number;
  height?: number;
  color?: string;
  title?: string;
  className?: string;
}

const LineChart: React.FC<LineChartProps> = ({
  data,
  width = 400,
  height = 200,
  color = '#4F46E5',
  title,
  className = ''
}) => {
  if (!data || data.length === 0) {
    return (
      <div className={`line-chart empty ${className}`}>
        <div className="empty-state">
          <div className="empty-icon">📊</div>
          <p>暂无数据</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const valueRange = maxValue - minValue || 1;
  
  const padding = 40;
  const chartWidth = width - 2 * padding;
  const chartHeight = height - 2 * padding;

  // 生成路径点
  const points = data.map((point, index) => {
    const x = padding + (index * chartWidth) / (data.length - 1);
    const y = padding + chartHeight - ((point.value - minValue) / valueRange) * chartHeight;
    return { x, y, ...point };
  });

  // 生成SVG路径
  const pathData = points.reduce((path, point, index) => {
    const command = index === 0 ? 'M' : 'L';
    return `${path} ${command} ${point.x} ${point.y}`;
  }, '');

  return (
    <div className={`line-chart ${className}`}>
      {title && <h3 className="chart-title">{title}</h3>}
      
      <div className="chart-container">
        <svg width={width} height={height} className="chart-svg">
          <defs>
            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor={color} stopOpacity="0.3" />
              <stop offset="100%" stopColor={color} stopOpacity="0.05" />
            </linearGradient>
          </defs>

          {/* 主线条 */}
          <path
            d={pathData}
            fill="none"
            stroke={color}
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="line-path"
          />

          {/* 数据点 */}
          {points.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r="4"
              fill="white"
              stroke={color}
              strokeWidth="3"
              className="data-dot"
            />
          ))}

          {/* X轴标签 */}
          {points.map((point, index) => (
            <text
              key={`label-${index}`}
              x={point.x}
              y={height - 10}
              textAnchor="middle"
              fill="#6B7280"
              fontSize="12"
              className="axis-label"
            >
              {new Date(point.date).toLocaleDateString('zh-CN', { 
                month: 'short', 
                day: 'numeric' 
              })}
            </text>
          ))}
        </svg>

        {/* 统计信息 */}
        <div className="chart-stats">
          <div className="stat-item">
            <span className="stat-label">最高</span>
            <span className="stat-value">{maxValue}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">最低</span>
            <span className="stat-value">{minValue}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">平均</span>
            <span className="stat-value">
              {Math.round(data.reduce((sum, d) => sum + d.value, 0) / data.length)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LineChart;
