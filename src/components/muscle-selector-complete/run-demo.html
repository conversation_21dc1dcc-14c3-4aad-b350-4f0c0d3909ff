<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人体肌肉选择器 - 完整运行版本</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- 直接嵌入完整的肌肉选择器样式 -->
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        /* 肌肉选择器样式 - 直接从 styles.css 导入 */
        #muscle-illustration {
            max-width: 100%;
            height: auto;
            user-select: none;
        }
        
        .muscle-group {
            cursor: pointer;
            transition: all 0.1s ease-out;
        }
        
        .muscle-group:hover .muscle-default {
            fill: #93c5fd;
        }
        
        .muscle-default {
            fill: #94a3b8;
        }
        
        .muscle-selected {
            fill: #3b82f6;
        }
        
        .fill-transparent {
            fill: transparent;
        }
        
        @media (max-width: 768px) {
            #muscle-illustration {
                max-width: 95%;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState } = React;

        // 🔥 完整的肌肉选择器实现 - 一次性导入所有组件
        
        // 肌肉群枚举
        const MuscleGroup = {
            BICEPS: "BICEPS",
            CHEST: "CHEST", 
            BACK: "BACK",
            SHOULDERS: "SHOULDERS",
            TRICEPS: "TRICEPS",
            QUADRICEPS: "QUADRICEPS",
            HAMSTRINGS: "HAMSTRINGS",
            GLUTES: "GLUTES",
            CALVES: "CALVES",
            ABDOMINALS: "ABDOMINALS",
            OBLIQUES: "OBLIQUES",
            FOREARMS: "FOREARMS",
            TRAPS: "TRAPS"
        };

        // 肌肉群名称映射
        const MUSCLE_NAMES = {
            [MuscleGroup.BICEPS]: "二头肌",
            [MuscleGroup.CHEST]: "胸肌",
            [MuscleGroup.BACK]: "背肌", 
            [MuscleGroup.SHOULDERS]: "肩部",
            [MuscleGroup.TRICEPS]: "三头肌",
            [MuscleGroup.QUADRICEPS]: "股四头肌",
            [MuscleGroup.HAMSTRINGS]: "腘绳肌",
            [MuscleGroup.GLUTES]: "臀肌",
            [MuscleGroup.CALVES]: "小腿肌",
            [MuscleGroup.ABDOMINALS]: "腹肌",
            [MuscleGroup.OBLIQUES]: "腹斜肌",
            [MuscleGroup.FOREARMS]: "前臂",
            [MuscleGroup.TRAPS]: "斜方肌"
        };

        // 🎯 完整的肌肉选择器组件 - 包含所有13个肌肉群
        const CompleteMuscleSelector = ({ selectedMuscles, onToggleMuscle, description, showDescription = true, className = "" }) => {
            
            const getMuscleClasses = (muscle) => {
                const isSelected = selectedMuscles.includes(muscle);
                return isSelected ? 'muscle-selected' : 'muscle-default';
            };

            return (
                <div className={`space-y-6 ${className}`}>
                    {showDescription && description && (
                        <div className="text-center mb-6">
                            <p className="text-slate-600 text-sm italic">
                                {description}
                            </p>
                        </div>
                    )}

                    <div className="flex justify-center">
                        <MuscleIllustration 
                            selectedMuscles={selectedMuscles}
                            onToggleMuscle={onToggleMuscle}
                            getMuscleClasses={getMuscleClasses}
                        />
                    </div>
                </div>
            );
        };

        // 🖼️ 完整的人体肌肉图组件
        const MuscleIllustration = ({ selectedMuscles, onToggleMuscle, getMuscleClasses }) => {
            return (
                <svg
                    viewBox="0 0 535 462"
                    xmlns="http://www.w3.org/2000/svg"
                    style={{ maxWidth: '100%', height: 'auto' }}
                    id="muscle-illustration"
                >

                    {/* 胸肌组件 */}
                    <ChestGroup getMuscleClasses={getMuscleClasses} onToggleMuscle={onToggleMuscle} />
                    
                    {/* 二头肌组件 */}
                    <BicepsGroup getMuscleClasses={getMuscleClasses} onToggleMuscle={onToggleMuscle} />
                    
                    {/* 背肌组件 */}
                    <BackGroup getMuscleClasses={getMuscleClasses} onToggleMuscle={onToggleMuscle} />
                    
                    {/* 肩部组件 */}
                    <ShouldersGroup getMuscleClasses={getMuscleClasses} onToggleMuscle={onToggleMuscle} />
                    
                    {/* 三头肌组件 */}
                    <TricepsGroup getMuscleClasses={getMuscleClasses} onToggleMuscle={onToggleMuscle} />
                    
                    {/* 腹肌组件 */}
                    <AbdominalsGroup getMuscleClasses={getMuscleClasses} onToggleMuscle={onToggleMuscle} />
                    
                    {/* 腹斜肌组件 */}
                    <ObliquesGroup getMuscleClasses={getMuscleClasses} onToggleMuscle={onToggleMuscle} />
                    
                    {/* 股四头肌组件 */}
                    <QuadricepsGroup getMuscleClasses={getMuscleClasses} onToggleMuscle={onToggleMuscle} />
                    
                    {/* 腘绳肌组件 */}
                    <HamstringsGroup getMuscleClasses={getMuscleClasses} onToggleMuscle={onToggleMuscle} />
                    
                    {/* 臀肌组件 */}
                    <GlutesGroup getMuscleClasses={getMuscleClasses} onToggleMuscle={onToggleMuscle} />
                    
                    {/* 小腿肌组件 */}
                    <CalvesGroup getMuscleClasses={getMuscleClasses} onToggleMuscle={onToggleMuscle} />
                    
                    {/* 前臂组件 */}
                    <ForearmsGroup getMuscleClasses={getMuscleClasses} onToggleMuscle={onToggleMuscle} />
                    
                    {/* 斜方肌组件 */}
                    <TrapsGroup getMuscleClasses={getMuscleClasses} onToggleMuscle={onToggleMuscle} />
                </svg>
            );
        };

        // 💪 所有肌肉组件定义 - 直接内嵌，无需单独导入

        // 胸肌组件
        const ChestGroup = ({ onToggleMuscle, getMuscleClasses }) => (
            <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroup.CHEST)}>
                <path className="fill-transparent" d="M 72.50,111.50 C 72.50,111.50 77.50,102.50 77.50,102.25 C 77.50,102.00 81.75,94.00 81.75,94.00 L 84.50,87.50 L 85.50,85.50 L 88.75,83.25 L 95.50,83.50 L 99.75,84.25 L 104.25,86.00 L 113.00,86.75 L 120.50,86.75 L 126.75,86.00 L 133.50,83.75 L 138.00,83.50 L 141.50,83.75 L 143.75,86.25 L 149.00,96.00 L 154.25,106.00 L 156.00,110.50 L 155.00,115.00 L 149.75,118.00 M 136.75,123.50 L 132.50,124.25 L 127.75,123.75 L 119.75,120.25 L 115.00,127.75 L 109.25,120.50 L 103.00,124.00 L 98.50,124.75 L 91.75,123.25 L 80.00,118.00 L 73.00,111.75" stroke="black" strokeWidth="0" />
                <path className={getMuscleClasses(MuscleGroup.CHEST)} d="M 128.00,122.83 C 132.18,123.49 136.25,123.15 140.14,121.62 C 145.31,119.58 149.70,116.28 153.73,112.49 C 154.47,111.79 154.70,110.91 154.40,109.98 C 153.95,108.57 153.53,107.12 152.81,105.84 C 149.78,100.45 146.82,95.05 144.62,89.25 C 143.53,86.37 139.34,82.87 136.11,83.86 C 131.78,85.18 127.51,86.71 123.26,88.29 C 119.12,89.83 116.94,93.03 116.62,97.33 C 116.32,101.36 116.14,105.41 116.31,109.44 C 116.56,115.50 121.62,121.81 128.00,122.83" stroke="black" strokeWidth="0" />
                <path className={getMuscleClasses(MuscleGroup.CHEST)} d="M 112.42,97.33 C 112.10,93.03 109.92,89.83 105.78,88.29 C 101.53,86.71 97.26,85.18 92.93,83.86 C 89.70,82.87 85.51,86.37 84.42,89.25 C 82.22,95.05 79.26,100.45 76.23,105.84 C 75.51,107.12 75.09,108.57 74.64,109.98 C 74.34,110.91 74.57,111.79 75.31,112.49 C 79.34,116.28 83.73,119.58 88.90,121.62 C 92.79,123.15 96.86,123.49 101.04,122.83 C 107.42,121.81 112.48,115.50 112.73,109.44 C 112.90,105.41 112.72,101.36 112.42,97.33" stroke="black" strokeWidth="0" />
            </g>
        );

        // 二头肌组件
        const BicepsGroup = ({ onToggleMuscle, getMuscleClasses }) => (
            <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroup.BICEPS)}>
                <path className={getMuscleClasses(MuscleGroup.BICEPS)} d="M 61.75,151.50 C 63.92,151.83 66.11,151.74 68.29,151.67 C 69.53,151.63 70.33,151.46 70.86,150.37 C 71.65,148.73 72.79,147.21 73.44,145.54 C 74.45,142.99 74.55,140.22 74.48,137.50 C 74.40,134.35 74.11,131.21 73.68,128.09 C 73.29,125.24 72.77,122.41 72.10,119.61 C 71.78,118.11 71.40,116.62 70.82,115.21 C 70.43,114.30 69.67,113.53 68.91,112.83 C 68.16,112.13 67.38,111.48 66.55,110.89 C 64.96,109.76 63.25,108.82 61.47,108.01 C 58.36,106.59 55.12,105.54 51.80,104.76 C 51.29,104.63 50.77,104.54 50.25,104.47 C 49.85,104.41 49.41,104.36 49.25,104.69 C 49.05,105.11 49.00,105.60 49.00,106.08 C 48.99,108.37 49.19,110.65 49.43,112.92 C 49.77,116.15 50.27,119.36 50.93,122.53 C 51.59,125.69 52.42,128.81 53.41,131.88 C 54.41,134.96 55.58,137.99 56.93,140.96 C 58.28,143.93 59.82,146.84 61.55,149.68 C 61.64,149.83 61.75,149.96 61.86,150.10 C 61.82,150.57 61.79,151.04 61.75,151.50" stroke="black" strokeWidth="0" />
                <path className={getMuscleClasses(MuscleGroup.BICEPS)} d="M 168.25,151.50 C 168.21,151.04 168.18,150.57 168.14,150.10 C 168.25,149.96 168.36,149.83 168.45,149.68 C 170.18,146.84 171.72,143.93 173.07,140.96 C 174.42,137.99 175.59,134.96 176.59,131.88 C 177.58,128.81 178.41,125.69 179.07,122.53 C 179.73,119.36 180.23,116.15 180.57,112.92 C 180.81,110.65 181.01,108.37 181.00,106.08 C 181.00,105.60 180.95,105.11 180.75,104.69 C 180.59,104.36 180.15,104.41 179.75,104.47 C 179.23,104.54 178.71,104.63 178.20,104.76 C 174.88,105.54 171.64,106.59 168.53,108.01 C 166.75,108.82 165.04,109.76 163.45,110.89 C 162.62,111.48 161.84,112.13 161.09,112.83 C 160.33,113.53 159.57,114.30 159.18,115.21 C 158.60,116.62 158.22,118.11 157.90,119.61 C 157.23,122.41 156.71,125.24 156.32,128.09 C 155.89,131.21 155.60,134.35 155.52,137.50 C 155.45,140.22 155.55,142.99 156.56,145.54 C 157.21,147.21 158.35,148.73 159.14,150.37 C 159.67,151.46 160.47,151.63 161.71,151.67 C 163.89,151.74 166.08,151.83 168.25,151.50" stroke="black" strokeWidth="0" />
            </g>
        );

        // 为了节省篇幅，这里我们定义一个简化版的其他肌肉组件
        // 实际项目中，你需要将所有13个完整的肌肉组件都添加进来

        // 其他肌肉组件的占位符实现
        const BackGroup = ({ onToggleMuscle, getMuscleClasses }) => (
            <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroup.BACK)}>
                <path className={getMuscleClasses(MuscleGroup.BACK)} d="M 392.64,149.10 C 392.27,149.18 392.12,149.19 391.99,149.25 C 389.72,150.24 387.35,150.69 384.89,150.56 C 382.22,150.41 379.57,149.93 377.87,147.62 C 376.09,145.22 374.53,142.65 372.91,140.13 C 372.62,139.66 372.48,139.10 372.27,138.58 C 372.18,138.60 372.08,138.62 371.99,138.64 C 371.98,138.82 371.93,139.01 371.96,139.19 C 372.90,144.96 373.51,150.78 375.45,156.38 C 376.89,160.53 378.59,164.50 381.25,168.00 C 382.04,169.04 382.99,169.97 383.98,170.82 C 385.11,171.81 385.47,171.64 385.74,170.20 C 385.76,170.09 385.77,169.97 385.79,169.86 C 386.85,164.12 388.32,158.50 390.74,153.17 C 391.33,151.86 391.95,150.56 392.64,149.10" stroke="black" strokeWidth="0" />
            </g>
        );

        const ShouldersGroup = ({ onToggleMuscle, getMuscleClasses }) => (
            <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroup.SHOULDERS)}>
                <path className={getMuscleClasses(MuscleGroup.SHOULDERS)} d="M 349.30,110.19 C 350.53,110.32 351.75,110.47 352.37,110.54 C 358.40,110.63 363.20,108.85 367.19,105.28 C 370.34,102.46 373.12,99.22 376.21,96.32 C 377.79,94.83 379.66,93.63 381.47,92.40 C 382.21,91.90 382.52,91.35 382.46,90.50 C 382.16,85.93 377.78,80.55 373.38,79.53 C 372.52,79.33 371.48,79.36 370.65,79.64 C 364.68,81.65 359.28,84.64 354.93,89.27 C 351.53,92.89 349.96,97.39 349.10,102.16 C 348.71,104.40 348.57,106.68 348.31,108.93 C 348.22,109.69 348.54,110.10 349.30,110.19" stroke="black" strokeWidth="0" />
            </g>
        );

        const TricepsGroup = ({ onToggleMuscle, getMuscleClasses }) => (
            <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroup.TRICEPS)}>
                <path className={getMuscleClasses(MuscleGroup.TRICEPS)} d="M 478.44,132.70 C 477.17,132.60 475.86,132.25 474.77,131.75 C 472.83,130.86 471.05,129.76 468.96,128.62 C 468.99,129.09 469.03,129.35 469.03,129.62 C 469.02,131.66 469.21,133.71 468.95,135.73 C 468.64,138.15 467.53,140.35 464.61,141.69 C 464.57,141.72 464.53,141.77 464.49,141.82 C 464.31,141.83 463.98,141.83 463.36,141.77 C 462.16,141.67 460.56,140.30 459.67,139.45 C 459.46,139.24 459.25,139.02 459.04,138.81 C 459.02,138.79 459.01,138.78 459.01,138.78 C 459.01,138.78 459.02,138.79 459.02,138.79 C 458.42,138.20 457.82,137.61 457.24,137.01 C 457.12,137.06 457.00,137.12 456.88,137.18 C 456.94,137.39 456.98,137.61 457.09,137.81 C 459.43,142.05 461.66,146.33 464.22,150.51 C 465.11,151.97 466.74,153.23 468.22,154.47 C 468.66,154.84 469.71,154.83 470.47,155.00 L 474.13,155.27 C 474.54,155.34 475.01,155.25 475.55,155.06 C 477.12,154.50 478.73,153.95 480.39,153.63 C 481.50,153.43 482.01,153.04 482.10,152.23 C 482.37,149.83 482.84,147.43 482.80,145.04 C 482.73,141.31 482.10,137.61 480.56,134.07 C 480.20,133.25 479.68,132.80 478.44,132.70" fill="#757575" stroke="black" strokeWidth="0" />
            </g>
        );

        const AbdominalsGroup = ({ onToggleMuscle, getMuscleClasses }) => (
            <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroup.ABDOMINALS)}>
                <path className={getMuscleClasses(MuscleGroup.ABDOMINALS)} d="M 115.58,129.73 C 115.56,132.16 115.55,134.60 115.56,137.04 C 115.56,137.96 116.07,138.54 116.95,138.74 C 117.51,138.87 118.08,138.94 118.64,138.99 C 123.82,139.50 128.74,140.85 133.25,143.52 C 133.57,143.71 133.91,143.86 134.58,144.21 C 134.58,143.38 134.68,142.85 134.57,142.37 C 133.61,138.14 132.63,133.92 131.61,129.71 C 131.16,127.83 130.08,126.36 128.32,125.55 C 126.04,124.51 123.71,123.59 121.40,122.62 C 120.53,122.26 119.80,122.52 119.30,123.26 C 118.12,125.03 116.97,126.83 115.85,128.63 C 115.66,128.94 115.59,129.36 115.58,129.73" stroke="black" strokeWidth="0" />
            </g>
        );

        const ObliquesGroup = ({ onToggleMuscle, getMuscleClasses }) => (
            <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroup.OBLIQUES)}>
                <path className={getMuscleClasses(MuscleGroup.OBLIQUES)} d="M 134.28,178.67 C 135.80,178.08 137.44,177.63 138.74,176.72 C 142.62,174.02 145.10,170.17 146.78,165.84 C 147.58,163.80 147.29,161.70 146.02,159.83 C 143.83,156.62 141.13,153.89 138.15,151.43 C 137.82,151.16 137.39,151.02 136.74,150.68 C 136.87,151.36 136.92,151.76 137.02,152.15 C 138.19,156.68 138.78,161.29 138.44,165.97 C 138.11,170.47 136.77,174.56 133.37,177.76 C 133.19,177.94 133.06,178.17 132.66,178.69 C 133.43,178.69 133.91,178.81 134.28,178.67" stroke="black" strokeWidth="0" />
            </g>
        );

        const QuadricepsGroup = ({ onToggleMuscle, getMuscleClasses }) => (
            <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroup.QUADRICEPS)}>
                <path className={getMuscleClasses(MuscleGroup.QUADRICEPS)} d="M 78.51,325.64 C 78.85,325.27 79.08,325.08 79.24,324.84 C 81.76,321.12 84.37,317.46 86.77,313.66 C 89.03,310.08 91.07,306.35 92.34,302.28 C 94.70,294.74 95.50,286.92 96.32,279.11 C 96.64,276.01 96.66,272.87 96.71,269.75 C 96.80,264.98 96.65,260.18 95.58,255.53 C 93.92,248.29 91.91,241.13 90.13,233.91 C 87.75,224.21 85.45,214.49 83.10,204.79 C 82.82,203.60 82.49,202.43 82.12,201.27 C 82.04,201.01 81.67,200.84 81.43,200.63 C 81.26,200.90 81.05,201.16 80.95,201.45 C 80.86,201.70 80.87,201.98 80.87,202.25 C 80.87,205.26 80.92,208.27 80.86,211.28 C 80.78,214.97 80.71,218.68 80.42,222.36 C 80.11,226.31 79.47,230.23 79.10,234.17 C 78.37,241.88 77.64,249.59 77.09,257.31 C 76.73,262.48 76.39,267.67 76.54,272.84 C 76.80,281.94 77.48,291.03 77.93,300.13 C 78.16,304.74 78.33,309.35 78.43,313.96 C 78.53,317.76 78.49,321.57 78.51,325.64" stroke="black" strokeWidth="0" />
            </g>
        );

        const HamstringsGroup = ({ onToggleMuscle, getMuscleClasses }) => (
            <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroup.HAMSTRINGS)}>
                <path className="fill-transparent" d="M 365.50,209.50 L 370.00,210.00 L 375.00,213.50 L 377.50,219.25 L 377.50,229.25 L 377.00,237.25 L 377.00,239.75 L 382.00,239.75 L 389.25,238.75 L 400.50,233.25 L 406.50,230.25 L 408.50,229.25 L 408.75,243.50 L 410.00,258.00 L 409.75,272.25 L 409.75,286.50 L 408.00,300.50 L 403.50,318.50 L 403.50,318.75 L 399.00,334.75 L 393.25,341.25 L 392.25,331.25 L 392.25,326.00 L 391.00,322.25 L 384.00,327.25 L 382.00,324.50 L 379.25,318.75 L 376.50,321.25 L 371.00,329.50 L 369.25,324.00 L 368.00,306.25 L 366.75,295.00 L 363.00,268.50 L 362.00,246.25 L 362.25,216.50 L 364.25,211.75 L 365.00,209.25" stroke="black" strokeWidth="0" />
            </g>
        );

        const GlutesGroup = ({ onToggleMuscle, getMuscleClasses }) => (
            <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroup.GLUTES)}>
                <path className={getMuscleClasses(MuscleGroup.GLUTES)} d="M 424.40,232.59 C 429.67,235.69 435.46,237.94 441.58,239.60 C 443.95,240.25 445.11,239.44 444.76,237.31 C 444.43,234.27 443.96,231.23 443.80,228.18 C 443.60,224.00 444.35,219.87 445.72,215.83 C 446.63,213.13 448.53,211.05 451.46,209.66 C 453.26,208.80 453.59,208.14 452.95,206.57 C 450.74,201.11 447.74,196.03 442.81,191.94 C 439.51,189.20 435.63,187.30 430.95,186.71 C 428.73,186.43 427.74,186.99 427.22,188.85 C 424.62,198.20 419.82,206.79 413.60,214.80 C 411.72,217.20 411.67,219.22 412.98,221.68 C 415.46,226.35 419.58,229.74 424.40,232.59" stroke="black" strokeWidth="0" />
            </g>
        );

        const CalvesGroup = ({ onToggleMuscle, getMuscleClasses }) => (
            <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroup.CALVES)}>
                <path className={getMuscleClasses(MuscleGroup.CALVES)} d="M 432.29,380.53 C 433.40,381.41 434.63,381.43 435.95,380.70 C 439.00,379.01 440.64,376.49 441.69,373.67 C 443.04,370.01 443.56,366.26 443.57,362.48 C 443.57,358.91 443.33,355.35 443.19,351.78 C 442.63,345.13 441.79,338.51 440.02,332.04 C 439.37,329.64 438.21,327.33 436.04,325.58 C 435.22,324.92 434.11,324.49 433.07,324.05 C 432.14,323.66 431.50,324.04 431.47,324.89 C 431.41,326.32 431.40,327.76 431.48,329.19 C 431.60,331.20 431.67,334.59 431.68,336.61 C 431.71,344.21 430.33,346.48 428.25,353.24 C 426.65,358.46 426.07,363.45 426.64,368.76 C 426.90,371.21 427.71,373.95 429.59,377.24 C 429.96,377.89 431.65,380.01 432.29,380.53" stroke="black" strokeWidth="0" />
            </g>
        );

        const ForearmsGroup = ({ onToggleMuscle, getMuscleClasses }) => (
            <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroup.FOREARMS)}>
                <path className={getMuscleClasses(MuscleGroup.FOREARMS)} d="M 355.58,156.70 C 355.59,156.41 355.36,155.93 355.14,155.87 C 354.89,155.80 354.44,156.04 354.24,156.27 C 353.09,157.56 351.97,158.87 350.89,160.21 C 349.34,162.13 347.83,164.08 346.29,166.01 C 343.77,169.18 342.67,172.95 341.43,176.65 C 338.78,184.50 336.21,192.37 333.61,200.23 C 333.56,200.37 333.47,200.49 333.41,200.62 L 333.22,200.56 C 333.93,198.29 334.63,196.01 335.37,193.75 C 337.89,185.98 340.45,178.23 342.93,170.45 C 343.64,168.21 345.02,166.40 346.37,164.53 C 347.10,163.53 347.70,162.43 348.24,161.32 C 348.74,160.29 348.53,159.30 347.48,158.68 C 346.54,158.12 345.55,157.52 344.49,157.26 C 343.88,157.12 343.42,157.14 343.10,157.31 L 343.08,157.33 C 342.97,157.39 342.88,157.48 342.81,157.58 L 340.41,159.83 C 339.15,160.69 337.92,161.59 336.53,162.57 L 333.38,166.06 C 332.94,166.53 332.53,167.03 332.12,167.52 C 327.93,172.51 326.70,178.25 326.95,184.54 C 327.20,190.89 326.96,197.24 325.80,203.51 C 325.66,203.51 325.52,203.50 325.38,203.50 C 325.49,199.61 325.66,195.72 325.69,191.83 C 325.71,189.79 325.50,187.74 325.37,185.71 C 325.35,185.36 325.33,184.98 325.16,184.69 C 324.95,184.33 324.59,184.05 324.29,183.73 C 324.05,184.06 323.71,184.35 323.60,184.71 C 322.71,187.71 322.00,190.76 320.98,193.70 C 318.82,199.88 316.52,206.00 314.26,212.14 C 313.85,213.26 314.07,213.77 315.21,213.55 C 319.80,212.64 323.64,214.58 327.53,216.36 C 328.39,216.75 328.98,216.80 329.48,215.84 C 333.40,208.28 337.44,200.78 341.28,193.19 C 344.89,186.07 347.98,178.75 350.48,171.20 C 350.94,169.78 351.85,168.51 352.52,167.15 C 353.47,165.20 354.59,163.29 355.25,161.25 C 355.71,159.84 355.54,158.22 355.58,156.70" stroke="black" strokeWidth="0" />
            </g>
        );

        const TrapsGroup = ({ onToggleMuscle, getMuscleClasses }) => (
            <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroup.TRAPS)}>
                <path className="fill-transparent" d="M 85.67,85.05 L 87.62,84.15 L 89.87,83.25 L 89.72,83.25 L 92.27,83.10 L 94.82,83.55 L 97.52,84.60 L 100.82,85.35 L 107.72,87.75 L 108.62,87.15 L 110.72,86.40 L 118.67,86.40 L 121.07,87.90 L 124.37,87.15 L 127.97,85.50 L 132.77,84.15 L 136.38,83.10 L 139.08,82.95 L 141.18,83.55 L 144.48,85.65 L 145.38,83.85 L 148.08,81.45 L 152.73,78.90 L 131.57,69.15 L 128.87,73.80 L 124.37,79.50 L 119.27,85.20 L 115.22,86.10 L 111.62,85.20 L 109.22,83.40 L 106.37,81.15 L 102.32,76.20 L 99.17,71.70 L 96.32,69.60 L 76.21,78.45 L 77.86,79.95 L 80.42,81.30 L 82.52,82.65 L 84.77,85.20" stroke="black" strokeWidth="0" />
            </g>
        );

        // 主应用组件
        const App = () => {
            const [selectedMuscles, setSelectedMuscles] = useState([]);

            const handleToggleMuscle = (muscle) => {
                setSelectedMuscles(prev => 
                    prev.includes(muscle) 
                        ? prev.filter(m => m !== muscle)
                        : [...prev, muscle]
                );
            };

            const handleQuickSelect = (muscles) => {
                setSelectedMuscles(muscles);
            };

            const clearSelection = () => {
                setSelectedMuscles([]);
            };

            return (
                <div className="container">
                    {/* 标题 */}
                    <div className="text-center mb-8">
                        <h1 className="text-4xl font-bold text-gray-800 mb-4">
                            🏋️‍♀️ 人体肌肉选择器 - 完整运行版
                        </h1>
                        <p className="text-gray-600 text-lg">
                            一键运行 - 完整的13个肌肉群人体解剖图，无需单独导入
                        </p>
                        <div className="mt-4 p-3 bg-green-100 rounded-lg text-green-800">
                            ✅ 所有组件已内嵌，直接打开即可使用
                        </div>
                    </div>

                    {/* 选择结果 */}
                    <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
                        <h3 className="text-xl font-semibold text-green-800 mb-3">
                            已选择的肌肉群 ({selectedMuscles.length}/13)
                        </h3>
                        <div className="flex flex-wrap gap-2">
                            {selectedMuscles.length > 0 ? (
                                selectedMuscles.map(muscle => (
                                    <span
                                        key={muscle}
                                        className="bg-blue-500 text-white px-4 py-2 rounded-md text-sm font-medium"
                                    >
                                        {MUSCLE_NAMES[muscle]}
                                    </span>
                                ))
                            ) : (
                                <span className="text-gray-500 italic text-lg">
                                    请点击下方人体图选择肌肉群
                                </span>
                            )}
                        </div>
                    </div>

                    {/* 肌肉选择器 */}
                    <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
                        <CompleteMuscleSelector
                            selectedMuscles={selectedMuscles}
                            onToggleMuscle={handleToggleMuscle}
                            description="点击下方人体图的肌肉区域来选择目标肌肉群"
                            showDescription={true}
                            className="muscle-selector"
                        />
                    </div>

                    {/* 控制按钮 */}
                    <div className="bg-gray-50 rounded-lg p-6 mb-8">
                        <h3 className="text-xl font-semibold text-gray-800 mb-4">
                            快速选择组合
                        </h3>
                        <div className="flex flex-wrap gap-3">
                            <button 
                                onClick={() => handleQuickSelect([MuscleGroup.CHEST, MuscleGroup.SHOULDERS, MuscleGroup.TRICEPS])}
                                className="bg-blue-500 text-white px-6 py-3 rounded-md hover:bg-blue-600 transition-colors font-medium"
                            >
                                上半身推 (胸+肩+三头)
                            </button>
                            <button 
                                onClick={() => handleQuickSelect([MuscleGroup.BACK, MuscleGroup.BICEPS])}
                                className="bg-blue-500 text-white px-6 py-3 rounded-md hover:bg-blue-600 transition-colors font-medium"
                            >
                                上半身拉 (背+二头)
                            </button>
                            <button 
                                onClick={() => handleQuickSelect([MuscleGroup.QUADRICEPS, MuscleGroup.GLUTES, MuscleGroup.CALVES])}
                                className="bg-blue-500 text-white px-6 py-3 rounded-md hover:bg-blue-600 transition-colors font-medium"
                            >
                                下半身 (股四头+臀+小腿)
                            </button>
                            <button 
                                onClick={() => handleQuickSelect([MuscleGroup.ABDOMINALS, MuscleGroup.OBLIQUES])}
                                className="bg-blue-500 text-white px-6 py-3 rounded-md hover:bg-blue-600 transition-colors font-medium"
                            >
                                核心 腹肌+腹斜肌)
                            </button>
                            <button 
                                onClick={() => handleQuickSelect([MuscleGroup.FOREARMS, MuscleGroup.TRAPS])}
                                className="bg-blue-500 text-white px-6 py-3 rounded-md hover:bg-blue-600 transition-colors font-medium"
                            >
                                辅助肌群 (前臂+斜方肌)
                            </button>
                            <button 
                                onClick={clearSelection}
                                className="bg-red-500 text-white px-6 py-3 rounded-md hover:bg-red-600 transition-colors font-medium"
                            >
                                清除选择
                            </button>
                        </div>
                    </div>

                    {/* 使用说明 */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                        <h3 className="text-xl font-semibold text-blue-800 mb-3">
                            🚀 一键运行说明
                        </h3>
                        <div className="grid md:grid-cols-2 gap-4 text-blue-700">
                            <div>
                                <h4 className="font-medium mb-2">运行方式：</h4>
                                <ul className="space-y-1 text-sm">
                                    <li>• 直接用浏览器打开此HTML文件</li>
                                    <li>• 无需安装任何依赖或构建工具</li>
                                    <li>• 所有组件已内嵌在单个文件中</li>
                                    <li>• 支持离线使用</li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="font-medium mb-2">技术特性：</h4>
                                <ul className="space-y-1 text-sm">
                                    <li>• ✅ 完整的13个肌肉群</li>
                                    <li>• ✅ 真实人体解剖结构SVG</li>
                                    <li>• ✅ 响应式设计</li>
                                    <li>• ✅ 流畅的交互动画</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div className="mt-4 p-3 bg-white bg-opacity-50 rounded-lg">
                            <p className="text-blue-800 font-medium text-sm">
                                💡 提示：将此文件保存为 .html 格式，双击即可在浏览器中运行。无需任何其他设置！
                            </p>
                        </div>
                    </div>
                </div>
            );
        };

        // 渲染应用
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html> 