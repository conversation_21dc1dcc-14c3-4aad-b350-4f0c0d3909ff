import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { MuscleGroup } from '../types';
export var BicepsGroup = function (_a) {
    var onToggleMuscle = _a.onToggleMuscle, getMuscleClasses = _a.getMuscleClasses;
    return (_jsxs("g", { className: "group cursor-pointer", onClick: function () { return onToggleMuscle(MuscleGroup.BICEPS); }, children: [_jsx("path", { className: "fill-transparent", d: "M 49.25,117.25\n           C 49.25,117.25 45.50,120.75 45.50,120.75\n             45.50,120.75 42.00,129.00 42.00,129.00\n             42.00,129.00 41.25,137.75 41.25,137.75\n             41.25,137.75 42.00,145.25 42.00,145.25\n             42.00,145.25 46.50,149.50 46.50,149.50\n             46.50,149.50 48.25,153.25 48.25,153.25\n             48.25,153.25 49.25,166.50 49.25,166.50\n             49.25,166.50 60.25,153.00 60.25,153.00\n             60.25,153.00 61.25,149.50 61.25,149.50\n             61.25,149.50 63.75,151.50 63.75,151.50\n             63.75,151.50 68.00,148.75 68.00,148.75\n             68.00,148.75 73.00,140.50 73.00,140.50\n             73.00,140.50 74.25,129.50 74.25,129.50\n             74.25,129.50 74.50,120.25 74.50,120.25\n             74.50,120.25 74.50,116.50 74.50,116.50\n             74.50,116.50 73.00,113.00 73.00,113.00\n             73.00,113.00 71.25,111.50 71.25,111.50\n             71.25,111.50 68.00,110.50 68.00,110.50\n             68.00,110.50 57.75,114.25 57.75,114.25\n             57.75,114.25 49.50,117.50 49.50,117.50", "data-elem": MuscleGroup.BICEPS, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.BICEPS), d: "M 61.75,151.50\n           C 63.92,151.83 66.11,151.74 68.29,151.67\n             69.53,151.63 70.33,151.46 70.86,150.37\n             71.65,148.73 72.79,147.21 73.44,145.54\n             74.45,142.99 74.55,140.22 74.48,137.50\n             74.40,134.35 74.11,131.21 73.68,128.09\n             73.29,125.24 72.77,122.41 72.10,119.61\n             71.78,118.11 71.40,116.62 70.82,115.21\n             70.43,114.30 69.67,113.53 68.91,112.83\n             68.16,112.13 67.38,111.48 66.55,110.89\n             64.96,109.76 63.25,108.82 61.47,108.01\n             58.36,106.59 55.12,105.54 51.80,104.76\n             51.29,104.63 50.77,104.54 50.25,104.47\n             49.85,104.41 49.41,104.36 49.25,104.69\n             49.05,105.11 49.00,105.60 49.00,106.08\n             48.99,108.37 49.19,110.65 49.43,112.92\n             49.77,116.15 50.27,119.36 50.93,122.53\n             51.59,125.69 52.42,128.81 53.41,131.88\n             54.41,134.96 55.58,137.99 56.93,140.96\n             58.28,143.93 59.82,146.84 61.55,149.68\n             61.64,149.83 61.75,149.96 61.86,150.10\n             61.82,150.57 61.79,151.04 61.75,151.50", "data-elem": MuscleGroup.BICEPS, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: "fill-transparent", d: "M 155.50,113.75\n           C 155.50,113.75 155.25,124.75 155.25,124.75\n             155.25,124.75 156.00,137.75 156.00,137.75\n             156.00,137.75 159.50,143.75 159.50,143.75\n             159.50,143.75 163.25,151.00 163.25,151.00\n             163.25,151.00 168.75,152.50 168.75,152.50\n             168.75,152.50 174.50,160.25 174.50,160.25\n             174.50,160.25 180.25,168.00 180.25,168.00\n             180.25,168.00 181.00,154.50 181.00,154.50\n             181.00,154.50 183.50,148.25 183.50,148.25\n             183.50,148.25 187.25,144.75 187.25,144.75\n             187.25,144.75 188.75,136.25 188.75,136.25\n             188.75,136.25 188.50,129.00 188.50,129.00\n             188.50,129.00 184.50,121.75 184.50,121.75\n             184.50,121.75 181.00,118.25 181.00,118.25\n             181.00,118.25 174.50,115.00 174.50,115.00\n             174.50,115.00 165.50,111.75 165.50,111.75\n             165.50,111.75 158.25,111.00 158.25,111.00\n             158.25,111.00 155.25,113.50 155.25,113.50", "data-elem": MuscleGroup.BICEPS, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.BICEPS), d: "M 168.25,151.50\n           C 168.21,151.04 168.18,150.57 168.14,150.10\n             168.25,149.96 168.36,149.83 168.45,149.68\n             170.18,146.84 171.72,143.93 173.07,140.96\n             174.42,137.99 175.59,134.96 176.59,131.88\n             177.58,128.81 178.41,125.69 179.07,122.53\n             179.73,119.36 180.23,116.15 180.57,112.92\n             180.81,110.65 181.01,108.37 181.00,106.08\n             181.00,105.60 180.95,105.11 180.75,104.69\n             180.59,104.36 180.15,104.41 179.75,104.47\n             179.23,104.54 178.71,104.63 178.20,104.76\n             174.88,105.54 171.64,106.59 168.53,108.01\n             166.75,108.82 165.04,109.76 163.45,110.89\n             162.62,111.48 161.84,112.13 161.09,112.83\n             160.33,113.53 159.57,114.30 159.18,115.21\n             158.60,116.62 158.22,118.11 157.90,119.61\n             157.23,122.41 156.71,125.24 156.32,128.09\n             155.89,131.21 155.60,134.35 155.52,137.50\n             155.45,140.22 155.55,142.99 156.56,145.54\n             157.21,147.21 158.35,148.73 159.14,150.37\n             159.67,151.46 160.47,151.63 161.71,151.67\n             163.89,151.74 166.08,151.83 168.25,151.50", "data-elem": MuscleGroup.BICEPS, stroke: "black", strokeWidth: "0" })] }));
};
