import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { MuscleGroup } from '../types';
export var HamstringsGroup = function (_a) {
    var onToggleMuscle = _a.onToggleMuscle, getMuscleClasses = _a.getMuscleClasses;
    return (_jsxs("g", { className: "group cursor-pointer", onClick: function () { return onToggleMuscle(MuscleGroup.HAMSTRINGS); }, children: [_jsx("path", { className: "fill-transparent", d: "M 365.50,209.50\n           C 365.50,209.50 370.00,210.00 370.00,210.00\n             370.00,210.00 375.00,213.50 375.00,213.50\n             375.00,213.50 377.50,219.25 377.50,219.25\n             377.50,219.25 377.50,229.25 377.50,229.25\n             377.50,229.25 377.00,237.25 377.00,237.25\n             377.00,237.25 377.00,239.75 377.00,239.75\n             377.00,239.75 382.00,239.75 382.00,239.75\n             382.00,239.75 389.25,238.75 389.25,238.75\n             389.25,238.75 400.50,233.25 400.50,233.25\n             400.50,233.25 406.50,230.25 406.50,230.25\n             406.50,230.25 408.50,229.25 408.50,229.25\n             408.50,229.25 408.75,243.50 408.75,243.50\n             408.75,243.50 410.00,258.00 410.00,258.00\n             410.00,258.00 409.75,272.25 409.75,272.25\n             409.75,272.25 409.75,286.50 409.75,286.50\n             409.75,286.50 408.00,300.50 408.00,300.50\n             408.00,300.50 403.50,318.50 403.50,318.75\n             403.50,319.00 399.00,334.75 399.00,334.75\n             399.00,334.75 393.25,341.25 393.25,341.25\n             393.25,341.25 392.25,331.25 392.25,331.25\n             392.25,331.25 392.25,326.00 392.25,326.00\n             392.25,326.00 391.00,322.25 391.00,322.25\n             391.00,322.25 384.00,327.25 384.00,327.25\n             384.00,327.25 382.00,324.50 382.00,324.50\n             382.00,324.50 379.25,318.75 379.25,318.75\n             379.25,318.75 376.50,321.25 376.50,321.25\n             376.50,321.25 371.00,329.50 371.00,329.50\n             371.00,329.50 369.25,324.00 369.25,324.00\n             369.25,324.00 368.00,306.25 368.00,306.25\n             368.00,306.25 366.75,295.00 366.75,295.00\n             366.75,295.00 363.00,268.50 363.00,268.50\n             363.00,268.50 362.00,246.25 362.00,246.25\n             362.00,246.25 362.25,216.50 362.25,216.50\n             362.25,216.50 364.25,211.75 365.00,209.25", "data-elem": MuscleGroup.HAMSTRINGS, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: "fill-transparent", d: "M 414.25,228.25\n           C 414.25,228.25 421.50,232.00 421.50,232.00\n             421.50,232.00 437.75,239.75 437.75,239.75\n             437.75,239.75 445.50,240.50 445.50,240.50\n             445.50,240.50 445.00,227.00 445.00,227.00\n             445.00,227.00 446.00,217.00 446.00,217.00\n             446.00,217.00 450.25,210.75 450.25,210.75\n             450.25,210.75 455.25,210.50 455.25,210.50\n             455.25,210.50 457.25,208.75 457.25,208.75\n             457.25,208.75 459.75,211.50 459.75,211.50\n             459.75,211.50 461.00,228.00 461.00,228.00\n             461.00,228.00 461.00,243.75 461.00,243.75\n             461.00,243.75 460.50,261.25 460.50,261.25\n             460.50,261.25 456.75,282.75 456.75,282.75\n             456.75,282.75 453.75,302.50 453.75,302.50\n             453.75,302.50 453.75,321.50 453.75,321.50\n             453.75,321.50 452.00,329.25 452.00,329.25\n             452.00,329.25 447.00,322.25 447.00,322.25\n             447.00,322.25 444.50,318.00 444.50,318.00\n             444.50,318.00 441.75,322.00 441.75,322.00\n             441.75,322.00 439.75,327.75 439.75,327.75\n             439.75,327.75 433.00,322.00 433.00,322.00\n             433.00,322.00 431.50,323.25 431.50,323.25\n             431.50,323.25 430.25,340.00 430.25,340.00\n             430.25,340.00 425.75,338.00 425.75,338.00\n             425.75,338.00 421.25,326.00 421.25,326.00\n             421.25,326.00 418.75,315.00 418.50,315.00\n             418.25,315.00 414.50,295.25 414.50,295.25\n             414.50,295.25 414.25,274.50 414.25,274.50\n             414.25,274.50 414.00,251.50 414.00,251.50\n             414.00,251.50 414.25,237.25 414.25,237.25\n             414.25,237.25 414.75,232.00 414.75,229.25", "data-elem": MuscleGroup.HAMSTRINGS, stroke: "black", strokeWidth: "0" })] }));
};
