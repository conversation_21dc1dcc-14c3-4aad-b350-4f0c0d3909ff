import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { MuscleGroup } from '../types';
export var ChestGroup = function (_a) {
    var onToggleMuscle = _a.onToggleMuscle, getMuscleClasses = _a.getMuscleClasses;
    return (_jsxs("g", { className: "group cursor-pointer", onClick: function () { return onToggleMuscle(MuscleGroup.CHEST); }, children: [_jsx("path", { className: "fill-transparent", d: "M 72.50,111.50\n           C 72.50,111.50 77.50,102.50 77.50,102.25\n             77.50,102.00 81.75,94.00 81.75,94.00\n             81.75,94.00 84.50,87.50 84.50,87.50\n             84.50,87.50 85.50,85.50 85.50,85.50\n             85.50,85.50 88.75,83.25 88.75,83.25\n             88.75,83.25 95.50,83.50 95.50,83.50\n             95.50,83.50 99.75,84.25 99.75,84.25\n             99.75,84.25 104.25,86.00 104.25,86.00\n             104.25,86.00 113.00,86.75 113.00,86.75\n             113.00,86.75 120.50,86.75 120.50,86.75\n             120.50,86.75 126.75,86.00 126.75,86.00\n             126.75,86.00 133.50,83.75 133.50,83.75\n             133.50,83.75 138.00,83.50 138.00,83.50\n             138.00,83.50 141.50,83.75 141.50,83.75\n             141.50,83.75 143.75,86.25 143.75,86.25\n             143.75,86.25 149.00,96.00 149.00,96.00\n             149.00,96.00 154.25,106.00 154.25,106.00\n             154.25,106.00 156.00,110.50 156.00,110.50\n             156.00,110.50 155.00,115.00 155.00,115.00\n             155.00,115.00 149.75,118.00 149.75,118.00M 136.75,123.50\n           C 136.75,123.50 132.50,124.25 132.50,124.25\n             132.50,124.25 127.75,123.75 127.75,123.75\n             127.75,123.75 119.75,120.25 119.75,120.25\n             119.75,120.25 115.00,127.75 115.00,127.75\n             115.00,127.75 109.25,120.50 109.25,120.50\n             109.25,120.50 103.00,124.00 103.00,124.00\n             103.00,124.00 98.50,124.75 98.50,124.75\n             98.50,124.75 91.75,123.25 91.75,123.25\n             91.75,123.25 80.00,118.00 80.00,118.00\n             80.00,118.00 73.00,111.75 73.00,111.75", "data-elem": MuscleGroup.CHEST, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.CHEST), d: "M 128.00,122.83\n           C 132.18,123.49 136.25,123.15 140.14,121.62\n             145.31,119.58 149.70,116.28 153.73,112.49\n             154.47,111.79 154.70,110.91 154.40,109.98\n             153.95,108.57 153.53,107.12 152.81,105.84\n             149.78,100.45 146.82,95.05 144.62,89.25\n             143.53,86.37 139.34,82.87 136.11,83.86\n             131.78,85.18 127.51,86.71 123.26,88.29\n             119.12,89.83 116.94,93.03 116.62,97.33\n             116.32,101.36 116.14,105.41 116.31,109.44\n             116.56,115.50 121.62,121.81 128.00,122.83", "data-elem": MuscleGroup.CHEST, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.CHEST), d: "M 115.70,124.93\n           C 116.59,123.70 117.47,122.46 118.32,121.20\n             118.61,120.76 118.81,120.26 119.06,119.77\n             118.96,119.45 118.93,119.13 118.77,118.89\n             117.79,117.39 116.84,115.87 115.76,114.45\n             114.99,113.43 114.47,113.43 113.69,114.49\n             112.62,115.92 111.67,117.44 110.74,118.97\n             110.55,119.30 110.53,119.94 110.72,120.25\n             111.72,121.86 112.79,123.44 113.91,124.98\n             114.45,125.72 115.15,125.69 115.70,124.93", "data-elem": MuscleGroup.CHEST, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.CHEST), d: "M 112.42,97.33\n           C 112.10,93.03 109.92,89.83 105.78,88.29\n             101.53,86.71 97.26,85.18 92.93,83.86\n             89.70,82.87 85.51,86.37 84.42,89.25\n             82.22,95.05 79.26,100.45 76.23,105.84\n             75.51,107.12 75.09,108.57 74.64,109.98\n             74.34,110.91 74.57,111.79 75.31,112.49\n             79.34,116.28 83.73,119.58 88.90,121.62\n             92.79,123.15 96.86,123.49 101.04,122.83\n             107.42,121.81 112.48,115.50 112.73,109.44\n             112.90,105.41 112.72,101.36 112.42,97.33", "data-elem": MuscleGroup.CHEST, stroke: "black", strokeWidth: "0" }), _jsx("path", { d: "M 114.71,95.58\n           C 114.89,95.35 115.00,95.27 115.02,95.18\n             115.68,92.72 116.69,90.44 118.43,88.53\n             118.64,88.30 118.61,87.85 118.69,87.50\n             118.33,87.47 117.94,87.32 117.63,87.42\n             115.83,88.01 114.05,88.05 112.23,87.51\n             111.81,87.38 111.31,87.52 110.84,87.54\n             111.01,87.97 111.09,88.49 111.37,88.83\n             112.74,90.47 113.65,92.33 114.26,94.36\n             114.37,94.74 114.53,95.10 114.71,95.58", "data-elem": MuscleGroup.CHEST, fill: "#757575", stroke: "black", strokeWidth: "0" })] }));
};
