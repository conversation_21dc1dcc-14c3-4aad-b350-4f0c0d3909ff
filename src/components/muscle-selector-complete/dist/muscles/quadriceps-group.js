import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { MuscleGroup } from '../types';
export var QuadricepsGroup = function (_a) {
    var onToggleMuscle = _a.onToggleMuscle, getMuscleClasses = _a.getMuscleClasses;
    return (_jsxs("g", { className: "group cursor-pointer", onClick: function () { return onToggle<PERSON>uscle(MuscleGroup.QUADRICEPS); }, children: [_jsx("path", { className: "fill-transparent", d: "M 77.00,190.50\n           C 77.00,190.50 83.75,201.75 83.75,201.75\n             83.75,201.75 88.25,208.75 88.25,208.75\n             88.25,208.75 95.25,214.50 95.25,214.50\n             95.25,214.50 106.75,222.50 106.75,222.50\n             106.75,222.50 112.25,224.50 112.25,224.50\n             112.25,224.50 112.75,239.50 112.75,239.50\n             112.75,239.50 107.75,269.00 107.75,269.00\n             107.75,269.00 104.00,280.75 104.00,280.75\n             104.00,280.75 105.75,298.25 105.75,298.25\n             105.75,298.25 104.50,314.50 104.50,314.50\n             104.50,314.50 99.25,331.25 99.25,331.25\n             99.25,331.25 95.50,336.75 95.50,336.75\n             95.50,336.75 94.25,316.00 94.25,316.00\n             94.25,316.00 90.75,307.75 90.75,307.75\n             90.75,307.75 85.50,315.75 85.50,315.75\n             85.50,315.75 78.00,326.25 78.00,326.25\n             78.00,326.25 75.25,328.75 75.25,328.75\n             75.25,328.75 74.00,303.00 74.00,303.00\n             74.00,303.00 64.75,276.75 64.75,276.75\n             64.75,276.75 64.25,264.25 64.25,264.25\n             64.25,264.25 64.25,248.00 64.25,248.00\n             64.25,248.00 65.75,221.25 65.75,221.25\n             65.75,221.25 69.50,210.75 69.50,210.75\n             69.50,210.75 74.00,198.50 74.00,198.50\n             74.00,198.50 77.00,190.75 77.00,190.75M 118.25,225.00\n           C 118.25,225.00 128.75,219.00 128.75,219.00\n             128.75,219.00 137.25,212.25 137.25,212.25\n             137.25,212.25 145.25,202.00 145.25,202.00\n             145.25,202.00 152.75,190.75 152.75,190.75\n             152.75,190.75 157.25,198.50 157.25,198.50\n             157.25,198.50 162.00,212.25 162.00,212.25\n             162.00,212.25 165.50,229.00 165.50,229.00\n             165.50,229.00 166.50,244.25 166.50,244.25\n             166.50,244.25 167.50,256.25 167.50,256.25\n             167.50,256.25 164.25,273.00 164.25,273.00\n             164.25,273.00 160.50,288.00 160.50,288.00\n             160.50,288.00 157.00,298.75 157.00,298.75\n             157.00,298.75 155.50,303.75 155.50,303.75\n             155.50,303.75 155.50,316.25 155.50,316.25\n             155.50,316.25 155.00,331.00 155.00,331.00\n             155.00,331.00 147.75,319.75 147.75,319.75\n             147.75,319.75 139.50,307.50 139.50,307.50\n             139.50,307.50 138.00,308.75 138.00,308.75\n             138.00,308.75 136.75,316.00 136.75,316.00\n             136.75,316.00 135.75,324.00 135.75,324.00\n             135.75,324.00 134.50,337.50 134.50,337.50\n             134.50,337.50 128.00,324.25 128.00,324.25\n             128.00,324.25 125.50,310.50 125.50,310.50\n             125.50,310.50 125.50,298.25 125.50,298.25\n             125.50,298.25 125.50,282.75 125.50,282.75\n             125.50,282.75 125.75,279.50 125.75,279.50\n             125.75,279.50 120.75,258.75 120.75,258.75\n             120.75,258.75 118.25,247.00 118.25,247.00\n             118.25,247.00 118.25,236.75 118.25,236.75\n             118.25,236.75 118.00,228.75 118.00,228.75\n             118.00,228.75 118.00,224.50 118.00,224.50", "data-elem": MuscleGroup.QUADRICEPS, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.QUADRICEPS), d: "M 78.51,325.64\n           C 78.85,325.27 79.08,325.08 79.24,324.84\n             81.76,321.12 84.37,317.46 86.77,313.66\n             89.03,310.08 91.07,306.35 92.34,302.28\n             94.70,294.74 95.50,286.92 96.32,279.11\n             96.64,276.01 96.66,272.87 96.71,269.75\n             96.80,264.98 96.65,260.18 95.58,255.53\n             93.92,248.29 91.91,241.13 90.13,233.91\n             87.75,224.21 85.45,214.49 83.10,204.79\n             82.82,203.60 82.49,202.43 82.12,201.27\n             82.04,201.01 81.67,200.84 81.43,200.63\n             81.26,200.90 81.05,201.16 80.95,201.45\n             80.86,201.70 80.87,201.98 80.87,202.25\n             80.87,205.26 80.92,208.27 80.86,211.28\n             80.78,214.97 80.71,218.68 80.42,222.36\n             80.11,226.31 79.47,230.23 79.10,234.17\n             78.37,241.88 77.64,249.59 77.09,257.31\n             76.73,262.48 76.39,267.67 76.54,272.84\n             76.80,281.94 77.48,291.03 77.93,300.13\n             78.16,304.74 78.33,309.35 78.43,313.96\n             78.53,317.76 78.49,321.57 78.51,325.64", "data-elem": MuscleGroup.QUADRICEPS, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.QUADRICEPS), d: "M 152.52,325.64\n           C 152.54,321.57 152.50,317.76 152.60,313.96\n             152.70,309.35 152.87,304.74 153.10,300.13\n             153.55,291.03 154.23,281.94 154.49,272.84\n             154.64,267.67 154.30,262.48 153.94,257.31\n             153.39,249.59 152.66,241.88 151.93,234.17\n             151.56,230.23 150.92,226.31 150.61,222.36\n             150.32,218.68 150.25,214.97 150.17,211.28\n             150.11,208.27 150.16,205.26 150.16,202.25\n             150.16,201.98 150.17,201.70 150.08,201.45\n             149.98,201.16 149.77,200.90 149.60,200.63\n             149.36,200.84 148.99,201.01 148.91,201.27\n             148.54,202.43 148.21,203.60 147.93,204.79\n             145.58,214.49 143.28,224.21 140.90,233.91\n             139.12,241.13 137.11,248.29 135.45,255.53\n             134.38,260.18 134.23,264.98 134.32,269.75\n             134.37,272.87 134.39,276.01 134.71,279.11\n             135.53,286.92 136.33,294.74 138.69,302.28\n             139.96,306.35 142.00,310.08 144.26,313.66\n             146.66,317.46 149.27,321.12 151.79,324.84\n             151.95,325.08 152.18,325.27 152.52,325.64", "data-elem": MuscleGroup.QUADRICEPS, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.QUADRICEPS), d: "M 129.16,270.61\n           C 132.77,254.88 136.72,239.25 141.63,223.88\n             143.77,217.18 145.61,210.42 146.74,203.48\n             146.86,202.76 146.90,202.02 146.98,201.28\n             146.86,201.26 146.74,201.24 146.61,201.22\n             145.70,202.70 144.80,204.20 143.85,205.66\n             139.69,212.08 136.88,213.68 131.26,218.80\n             129.14,220.73 123.33,223.43 121.12,225.24\n             119.90,226.24 119.95,227.97 119.95,232.26\n             119.95,236.87 120.14,238.30 121.12,246.48\n             122.17,254.80 122.29,256.43 124.43,266.37\n             124.83,269.24 126.49,274.88 126.96,277.74\n             127.02,278.05 127.13,278.34 127.45,278.62\n             128.01,275.95 128.55,273.27 129.16,270.61", "data-elem": MuscleGroup.QUADRICEPS, stroke: "black", strokeWidth: "0" })] }));
};
