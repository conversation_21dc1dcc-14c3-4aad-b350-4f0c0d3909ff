import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { MuscleGroup } from '../types';
export var ForearmsGroup = function (_a) {
    var onToggleMuscle = _a.onToggleMuscle, getMuscleClasses = _a.getMuscleClasses;
    return (_jsxs("g", { className: "group cursor-pointer", onClick: function () { return onToggleMuscle(MuscleGroup.FOREARMS); }, children: [_jsx("path", { className: getMuscleClasses(MuscleGroup.FOREARMS), d: "M 355.58,156.70\n           C 355.59,156.41 355.36,155.93 355.14,155.87\n             354.89,155.80 354.44,156.04 354.24,156.27\n             353.09,157.56 351.97,158.87 350.89,160.21\n             349.34,162.13 347.83,164.08 346.29,166.01\n             343.77,169.18 342.67,172.95 341.43,176.65\n             338.78,184.50 336.21,192.37 333.61,200.23\n             333.56,200.37 333.47,200.49 333.41,200.62\n             333.41,200.62 333.22,200.56 333.22,200.56\n             333.93,198.29 334.63,196.01 335.37,193.75\n             337.89,185.98 340.45,178.23 342.93,170.45\n             343.64,168.21 345.02,166.40 346.37,164.53\n             347.10,163.53 347.70,162.43 348.24,161.32\n             348.74,160.29 348.53,159.30 347.48,158.68\n             346.54,158.12 345.55,157.52 344.49,157.26\n             343.88,157.12 343.42,157.14 343.10,157.31\n             343.10,157.31 343.10,157.31 343.10,157.31\n             343.10,157.31 343.08,157.33 343.08,157.33\n             342.97,157.39 342.88,157.48 342.81,157.58\n             342.81,157.58 340.41,159.83 340.41,159.83\n             339.15,160.69 337.92,161.59 336.53,162.57\n             336.53,162.57 333.38,166.06 333.38,166.06\n             332.94,166.53 332.53,167.03 332.12,167.52\n             327.93,172.51 326.70,178.25 326.95,184.54\n             327.20,190.89 326.96,197.24 325.80,203.51\n             325.66,203.51 325.52,203.50 325.38,203.50\n             325.49,199.61 325.66,195.72 325.69,191.83\n             325.71,189.79 325.50,187.74 325.37,185.71\n             325.35,185.36 325.33,184.98 325.16,184.69\n             324.95,184.33 324.59,184.05 324.29,183.73\n             324.05,184.06 323.71,184.35 323.60,184.71\n             322.71,187.71 322.00,190.76 320.98,193.70\n             318.82,199.88 316.52,206.00 314.26,212.14\n             313.85,213.26 314.07,213.77 315.21,213.55\n             319.80,212.64 323.64,214.58 327.53,216.36\n             328.39,216.75 328.98,216.80 329.48,215.84\n             333.40,208.28 337.44,200.78 341.28,193.19\n             344.89,186.07 347.98,178.75 350.48,171.20\n             350.94,169.78 351.85,168.51 352.52,167.15\n             353.47,165.20 354.59,163.29 355.25,161.25\n             355.71,159.84 355.54,158.22 355.58,156.70", "data-elem": MuscleGroup.FOREARMS, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.FOREARMS), d: "M 468.26,156.70\n           C 468.30,158.22 468.13,159.84 468.59,161.25\n             469.25,163.29 470.37,165.20 471.32,167.15\n             471.99,168.51 472.90,169.78 473.36,171.20\n             475.86,178.75 478.95,186.07 482.56,193.19\n             486.40,200.78 490.44,208.28 494.36,215.84\n             494.86,216.80 495.45,216.75 496.31,216.36\n             500.20,214.58 504.04,212.64 508.63,213.55\n             509.77,213.77 509.99,213.26 509.58,212.14\n             507.32,206.00 505.02,199.88 502.86,193.70\n             501.84,190.76 501.13,187.71 500.24,184.71\n             500.13,184.35 499.79,184.06 499.55,183.73\n             499.25,184.05 498.89,184.33 498.68,184.69\n             498.51,184.98 498.49,185.36 498.47,185.71\n             498.34,187.74 498.13,189.79 498.15,191.83\n             498.18,195.72 498.35,199.61 498.46,203.50\n             498.32,203.50 498.18,203.51 498.04,203.51\n             496.88,197.24 496.64,190.89 496.89,184.54\n             497.14,178.25 495.91,172.51 491.72,167.52\n             491.31,167.03 490.90,166.53 490.46,166.06\n             490.46,166.06 487.31,162.57 487.31,162.57\n             485.92,161.59 484.69,160.69 483.43,159.83\n             483.43,159.83 481.03,157.58 481.03,157.58\n             480.96,157.48 480.87,157.39 480.76,157.33\n             480.76,157.33 480.74,157.31 480.74,157.31\n             480.74,157.31 480.74,157.31 480.74,157.31\n             480.42,157.14 479.96,157.12 479.35,157.26\n             478.29,157.52 477.30,158.12 476.36,158.68\n             475.31,159.30 475.10,160.29 475.60,161.32\n             476.14,162.43 476.74,163.53 477.47,164.53\n             478.82,166.40 480.20,168.21 480.91,170.45\n             483.39,178.23 485.95,185.98 488.47,193.75\n             489.21,196.01 489.91,198.29 490.62,200.56\n             490.62,200.56 490.43,200.62 490.43,200.62\n             490.37,200.49 490.28,200.37 490.23,200.23\n             487.63,192.37 485.06,184.50 482.41,176.65\n             481.17,172.95 480.07,169.18 477.55,166.01\n             476.01,164.08 474.50,162.13 472.95,160.21\n             471.87,158.87 470.75,157.56 469.60,156.27\n             469.40,156.04 468.95,155.80 468.70,155.87\n             468.48,155.93 468.25,156.41 468.26,156.70", "data-elem": MuscleGroup.FOREARMS, stroke: "black", strokeWidth: "0" })] }));
};
