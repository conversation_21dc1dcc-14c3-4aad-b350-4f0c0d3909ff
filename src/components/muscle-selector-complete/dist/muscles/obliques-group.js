import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { MuscleGroup } from '../types';
export var ObliquesGroup = function (_a) {
    var onToggleMuscle = _a.onToggleMuscle, getMuscleClasses = _a.getMuscleClasses;
    return (_jsxs("g", { className: "group cursor-pointer", onClick: function () { return onToggleMuscle(MuscleGroup.OBLIQUES); }, children: [_jsx("path", { className: getMuscleClasses(MuscleGroup.OBLIQUES), d: "M 134.28,178.67\n           C 135.80,178.08 137.44,177.63 138.74,176.72\n             142.62,174.02 145.10,170.17 146.78,165.84\n             147.58,163.80 147.29,161.70 146.02,159.83\n             143.83,156.62 141.13,153.89 138.15,151.43\n             137.82,151.16 137.39,151.02 136.74,150.68\n             136.87,151.36 136.92,151.76 137.02,152.15\n             138.19,156.68 138.78,161.29 138.44,165.97\n             138.11,170.47 136.77,174.56 133.37,177.76\n             133.19,177.94 133.06,178.17 132.66,178.69\n             133.43,178.69 133.91,178.81 134.28,178.67", "data-elem": MuscleGroup.OBLIQUES, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.OBLIQUES), d: "M 95.56,178.67\n           C 95.93,178.81 96.41,178.69 96.18,178.69\n             95.78,178.17 95.65,177.94 95.47,177.76\n             92.07,174.56 90.73,170.47 90.40,165.97\n             90.06,161.29 90.65,156.68 91.82,152.15\n             91.92,151.76 91.97,151.36 92.10,150.68\n             91.45,151.02 91.02,151.16 90.69,151.43\n             87.71,153.89 85.01,156.62 82.82,159.83\n             81.55,161.70 81.26,163.80 82.06,165.84\n             83.74,170.17 86.22,174.02 90.10,176.72\n             91.40,177.63 93.04,178.08 94.56,178.67", "data-elem": MuscleGroup.OBLIQUES, stroke: "black", strokeWidth: "0" })] }));
};
