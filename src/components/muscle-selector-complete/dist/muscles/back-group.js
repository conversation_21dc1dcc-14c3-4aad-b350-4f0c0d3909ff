import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { MuscleGroup } from '../types';
export var BackGroup = function (_a) {
    var onToggleMuscle = _a.onToggleMuscle, getMuscleClasses = _a.getMuscleClasses;
    return (_jsxs("g", { className: "group cursor-pointer", onClick: function () { return onToggleMuscle(MuscleGroup.BACK); }, children: [_jsx("path", { className: getMuscleClasses(MuscleGroup.BACK), d: "M 392.64,149.10\n           C 392.27,149.18 392.12,149.19 391.99,149.25\n             389.72,150.24 387.35,150.69 384.89,150.56\n             382.22,150.41 379.57,149.93 377.87,147.62\n             376.09,145.22 374.53,142.65 372.91,140.13\n             372.62,139.66 372.48,139.10 372.27,138.58\n             372.18,138.60 372.08,138.62 371.99,138.64\n             371.98,138.82 371.93,139.01 371.96,139.19\n             372.90,144.96 373.51,150.78 375.45,156.38\n             376.89,160.53 378.59,164.50 381.25,168.00\n             382.04,169.04 382.99,169.97 383.98,170.82\n             385.11,171.81 385.47,171.64 385.74,170.20\n             385.76,170.09 385.77,169.97 385.79,169.86\n             386.85,164.12 388.32,158.50 390.74,153.17\n             391.33,151.86 391.95,150.56 392.64,149.10", "data-elem": MuscleGroup.BACK, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.BACK), d: "M 431.20,149.10\n           C 431.89,150.56 432.51,151.86 433.10,153.17\n             435.52,158.50 436.99,164.12 438.05,169.86\n             438.07,169.97 438.08,170.09 438.10,170.20\n             438.37,171.64 438.73,171.81 439.86,170.82\n             440.85,169.97 441.80,169.04 442.59,168.00\n             445.25,164.50 446.95,160.53 448.39,156.38\n             450.33,150.78 450.94,144.96 451.88,139.19\n             451.91,139.01 451.86,138.82 451.85,138.64\n             451.76,138.62 451.66,138.60 451.57,138.58\n             451.36,139.10 451.22,139.66 450.93,140.13\n             449.31,142.65 447.75,145.22 445.97,147.62\n             444.27,149.93 441.62,150.41 438.95,150.56\n             436.49,150.69 434.12,150.24 431.85,149.25\n             431.72,149.19 431.57,149.18 431.20,149.10", "data-elem": MuscleGroup.BACK, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.BACK), d: "M 412.34,201.13\n           C 412.33,204.02 412.23,206.91 412.21,209.80\n             412.20,210.15 412.41,210.50 412.52,210.85\n             412.85,210.64 413.29,210.51 413.49,210.21\n             414.63,208.52 415.79,206.83 416.81,205.06\n             419.82,199.80 421.42,193.97 423.33,188.28\n             424.66,184.33 425.93,180.34 428.26,176.84\n             429.59,174.85 430.96,172.87 433.48,172.15\n             435.22,171.65 435.24,171.61 435.12,169.77\n             434.62,162.73 432.68,156.10 428.46,150.45\n             425.26,146.17 421.44,142.37 417.87,138.37\n             417.58,138.04 417.14,137.81 416.73,137.61\n             416.04,137.26 415.36,137.37 415.11,138.14\n             414.24,140.80 413.03,143.44 412.73,146.18\n             412.28,150.18 412.47,154.26 412.45,158.31\n             412.42,165.95 412.44,173.58 412.44,181.22\n             412.41,181.22 412.38,181.22 412.35,181.22\n             412.35,187.86 412.36,194.49 412.34,201.13", "data-elem": MuscleGroup.BACK, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.BACK), d: "M 411.50,201.13\n           C 411.48,194.49 411.49,187.86 411.49,181.22\n             411.46,181.22 411.43,181.22 411.40,181.22\n             411.40,173.58 411.42,165.95 411.39,158.31\n             411.37,154.26 411.56,150.18 411.11,146.18\n             410.81,143.44 409.60,140.80 408.73,138.14\n             408.48,137.37 407.80,137.26 407.11,137.61\n             406.70,137.81 406.26,138.04 405.97,138.37\n             402.40,142.37 398.58,146.17 395.38,150.45\n             391.16,156.10 389.22,162.73 388.72,169.77\n             388.60,171.61 388.62,171.65 390.36,172.15\n             392.88,172.87 394.25,174.85 395.58,176.84\n             397.91,180.34 399.18,184.33 400.51,188.28\n             402.42,193.97 404.02,199.80 407.03,205.06\n             408.05,206.83 409.21,208.52 410.35,210.21\n             410.55,210.51 410.99,210.64 411.32,210.85\n             411.43,210.50 411.64,210.15 411.63,209.80\n             411.61,206.91 411.51,204.02 411.50,201.13", "data-elem": MuscleGroup.BACK, stroke: "black", strokeWidth: "0" })] }));
};
