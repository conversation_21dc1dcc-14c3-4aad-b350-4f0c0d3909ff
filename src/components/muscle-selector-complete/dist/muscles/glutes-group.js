import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { MuscleGroup } from '../types';
export var GlutesGroup = function (_a) {
    var onToggleMuscle = _a.onToggleMuscle, getMuscleClasses = _a.getMuscleClasses;
    return (_jsxs("g", { className: "group cursor-pointer", onClick: function () { return onToggleMuscle(MuscleGroup.GLUTES); }, children: [_jsx("path", { className: "fill-transparent", d: "M 394.25,186.00\n           C 394.25,186.00 387.50,187.25 387.50,187.25\n             387.50,187.25 376.00,194.00 376.00,194.00\n             376.00,194.00 370.00,202.75 370.00,202.75\n             370.00,202.75 366.25,207.25 366.25,207.25\n             366.25,207.25 372.75,211.25 372.75,211.25\n             372.75,211.25 377.00,217.25 377.00,217.25\n             377.00,217.25 378.75,228.25 378.75,228.25\n             378.75,228.25 377.25,238.75 377.25,238.75\n             377.25,238.75 377.00,241.25 377.00,241.25\n             377.00,241.25 384.50,240.00 384.50,240.00\n             384.50,240.00 396.25,235.75 396.25,235.75\n             396.25,235.75 405.75,229.50 405.75,229.50\n             405.75,229.50 411.25,225.50 411.25,225.50\n             411.25,225.50 418.75,231.50 418.75,231.50\n             418.75,231.50 429.75,236.25 429.75,236.25\n             429.75,236.25 441.50,240.00 441.50,240.00\n             441.50,240.00 445.75,240.00 445.75,240.00\n             445.75,240.00 445.00,231.75 445.00,231.75\n             445.00,231.75 444.50,223.50 444.50,223.50\n             444.50,223.50 446.00,215.25 446.00,215.25\n             446.00,215.25 451.50,209.75 451.50,209.75\n             451.50,209.75 455.75,208.00 455.75,208.00\n             455.75,208.00 452.00,200.75 452.00,200.75\n             452.00,200.75 448.00,196.75 448.00,196.75\n             448.00,196.75 441.00,190.00 441.00,190.00\n             441.00,190.00 436.75,187.75 436.75,187.75\n             436.75,187.75 429.25,186.00 429.25,186.00\n             429.25,186.00 426.00,185.75 426.00,185.75\n             426.00,185.75 423.50,194.75 423.50,194.75\n             423.50,194.75 418.75,205.25 418.75,205.25\n             418.75,205.25 413.75,211.25 413.75,211.25\n             413.75,211.25 411.00,213.75 411.00,213.75\n             411.00,213.75 408.25,210.75 408.25,210.75\n             408.25,210.75 402.50,201.50 402.50,201.50\n             402.50,201.50 399.50,192.50 399.50,192.50\n             399.50,192.50 397.25,186.25 397.25,186.25\n             397.25,186.25 394.50,185.75 394.50,185.75", "data-elem": MuscleGroup.GLUTES, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.GLUTES), d: "M 424.40,232.59\n           C 429.67,235.69 435.46,237.94 441.58,239.60\n             443.95,240.25 445.11,239.44 444.76,237.31\n             444.43,234.27 443.96,231.23 443.80,228.18\n             443.60,224.00 444.35,219.87 445.72,215.83\n             446.63,213.13 448.53,211.05 451.46,209.66\n             453.26,208.80 453.59,208.14 452.95,206.57\n             450.74,201.11 447.74,196.03 442.81,191.94\n             439.51,189.20 435.63,187.30 430.95,186.71\n             428.73,186.43 427.74,186.99 427.22,188.85\n             424.62,198.20 419.82,206.79 413.60,214.80\n             411.72,217.20 411.67,219.22 412.98,221.68\n             415.46,226.35 419.58,229.74 424.40,232.59", "data-elem": MuscleGroup.GLUTES, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.GLUTES), d: "M 395.62,188.85\n           C 395.10,186.99 394.11,186.43 391.89,186.71\n             387.21,187.30 383.33,189.20 380.03,191.94\n             375.10,196.03 372.10,201.11 369.89,206.57\n             369.25,208.14 369.58,208.80 371.38,209.66\n             374.31,211.05 376.21,213.13 377.12,215.83\n             378.49,219.87 379.24,224.00 379.04,228.18\n             378.88,231.23 378.41,234.27 378.08,237.31\n             377.73,239.44 378.89,240.25 381.26,239.60\n             387.38,237.94 393.17,235.69 398.44,232.59\n             403.26,229.74 407.38,226.35 409.86,221.68\n             411.17,219.22 411.12,217.20 409.24,214.80\n             403.02,206.79 398.22,198.20 395.62,188.85", "data-elem": MuscleGroup.GLUTES, stroke: "black", strokeWidth: "0" })] }));
};
