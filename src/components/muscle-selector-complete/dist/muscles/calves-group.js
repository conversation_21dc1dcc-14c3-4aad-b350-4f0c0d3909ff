import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { MuscleGroup } from '../types';
export var CalvesGroup = function (_a) {
    var onToggleMuscle = _a.onToggleMuscle, getMuscleClasses = _a.getMuscleClasses;
    return (_jsxs("g", { className: "group cursor-pointer", onClick: function () { return onToggleMuscle(MuscleGroup.CALVES); }, children: [_jsx("path", { className: getMuscleClasses(MuscleGroup.CALVES), d: "M 432.29,380.53\n           C 433.40,381.41 434.63,381.43 435.95,380.70\n             439.00,379.01 440.64,376.49 441.69,373.67\n             443.04,370.01 443.56,366.26 443.57,362.48\n             443.57,358.91 443.33,355.35 443.19,351.78\n             442.63,345.13 441.79,338.51 440.02,332.04\n             439.37,329.64 438.21,327.33 436.04,325.58\n             435.22,324.92 434.11,324.49 433.07,324.05\n             432.14,323.66 431.50,324.04 431.47,324.89\n             431.41,326.32 431.40,327.76 431.48,329.19\n             431.60,331.20 431.67,334.59 431.68,336.61\n             431.71,344.21 430.33,346.48 428.25,353.24\n             426.65,358.46 426.07,363.45 426.64,368.76\n             426.90,371.21 427.71,373.95 429.59,377.24\n             429.96,377.89 431.65,380.01 432.29,380.53", "data-elem": MuscleGroup.CALVES, stroke: "black", strokeWidth: "0" }), _jsx("path", { className: getMuscleClasses(MuscleGroup.CALVES), d: "M 391.16,336.61\n           C 391.17,334.59 391.24,331.20 391.35,329.19\n             391.44,327.76 391.43,326.32 391.37,324.89\n             391.34,324.04 390.70,323.66 389.77,324.05\n             388.73,324.49 387.62,324.92 386.79,325.58\n             384.63,327.33 383.47,329.64 382.82,332.04\n             381.05,338.51 380.21,345.13 379.65,351.78\n             379.51,355.35 379.27,358.91 379.27,362.48\n             379.28,366.26 379.80,370.01 381.15,373.67\n             382.20,376.49 383.84,379.01 386.89,380.70\n             388.21,381.43 389.44,381.41 390.54,380.53\n             391.19,380.01 392.88,377.89 393.25,377.24\n             395.13,373.95 395.94,371.21 396.20,368.76\n             396.77,363.45 396.19,358.46 394.59,353.24\n             392.50,346.48 391.13,344.21 391.16,336.61", "data-elem": MuscleGroup.CALVES, stroke: "black", strokeWidth: "0" })] }));
};
