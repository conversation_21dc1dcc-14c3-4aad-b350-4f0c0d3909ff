export declare enum MuscleGroup {
    BICEPS = "BICEPS",
    CHEST = "CHEST",
    BACK = "BACK",
    SHOULDERS = "SHOULDERS",
    TRICEPS = "TRICEPS",
    QUADRICEPS = "QUADRICEPS",
    HAMSTRINGS = "HAMSTRINGS",
    GLUTES = "GLUTES",
    CALVES = "CALVES",
    ABDOMINALS = "ABDOMINALS",
    OBLIQUES = "OBLIQUES",
    FOREARMS = "FOREARMS",
    TRAPS = "TRAPS"
}
export declare const MUSCLE_NAMES: Record<MuscleGroup, string>;
export interface MuscleSelectionProps {
    selectedMuscles: MuscleGroup[];
    onToggleMuscle: (muscle: MuscleGroup) => void;
    className?: string;
    description?: string;
    showDescription?: boolean;
}
export interface MuscleComponentProps {
    onToggleMuscle: (muscle: MuscleGroup) => void;
    getMuscleClasses: (muscle: MuscleGroup) => string;
}
