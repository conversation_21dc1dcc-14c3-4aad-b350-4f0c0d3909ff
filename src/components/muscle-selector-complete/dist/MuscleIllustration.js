import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { BicepsGroup, ChestGroup, BackGroup, ShouldersGroup, TricepsGroup, AbdominalsGroup, QuadricepsGroup, ObliquesGroup, HamstringsGroup, GlutesGroup, CalvesGroup, ForearmsGroup, TrapsGroup } from './muscles';
export var MuscleIllustration = function (_a) {
    var selectedMuscles = _a.selectedMuscles, onToggleMuscle = _a.onToggleMuscle, getMuscleClasses = _a.getMuscleClasses;
    return (_jsxs("svg", { viewBox: "0 0 535 462", xmlns: "http://www.w3.org/2000/svg", style: { maxWidth: '100%', height: 'auto' }, id: "muscle-illustration", children: [_jsx("path", { d: "M 389.54,40.30\n           C 389.99,41.24 390.42,42.20 391.03,43.03\n             391.30,43.39 392.00,43.57 392.50,43.55\n             392.50,43.55 394.11,43.55 394.11,43.55\n             394.27,44.74 394.42,45.94 394.57,47.13\n             394.84,49.23 394.98,51.35 395.23,53.45\n             395.41,54.97 396.50,55.70 397.88,56.40\n             401.75,58.75 405.62,61.10 409.49,63.45\n             413.13,65.68 416.77,67.91 420.41,70.14\n             424.05,72.37 427.69,74.60 431.33,76.83\n             434.97,79.06 438.61,81.29 442.25,83.52\n             445.89,85.75 449.53,87.98 453.17,90.21\n             456.81,92.44 460.45,94.67 464.09,96.90\n             467.73,99.13 471.37,101.36 475.01,103.59\n             478.65,105.82 482.29,108.05 485.93,110.28\n             489.57,112.51 493.21,114.74 496.85,116.97\n             500.49,119.20 504.13,121.43 507.77,123.66\n             511.41,125.89 515.05,128.12 518.69,130.35\n             522.33,132.58 525.97,134.81 529.61,137.04\n             533.25,139.27 536.89,141.50 540.53,143.73\n             544.17,145.96 547.81,148.19 551.45,150.42", fill: "#f5f5f5", stroke: "black", strokeWidth: "1" }), _jsx("path", { d: "M 389.54,40.30\n           C 389.99,41.24 390.42,42.20 391.03,43.03\n             391.30,43.39 392.00,43.57 392.50,43.55\n             392.50,43.55 394.11,43.55 394.11,43.55\n             394.27,44.74 394.42,45.94 394.57,47.13\n             394.84,49.23 394.98,51.35 395.23,53.45\n             395.41,54.97 396.50,55.70 397.88,56.40\n             397.97,55.89 398.04,55.52 398.10,55.16", fill: "#757575", stroke: "black", strokeWidth: "0" }), _jsx("path", { d: "M 386.48,416.75\n           C 384.13,415.88 379.40,417.50 378.57,419.80\n             378.09,421.14 377.53,422.44 376.94,423.74\n             376.94,423.74 376.92,423.73 376.92,423.73\n             375.29,427.30 375.14,427.42 373.23,430.09", fill: "#757575", stroke: "black", strokeWidth: "0" }), _jsx("path", { d: "M 461.30,429.86\n           C 458.70,429.23 451.30,429.96 449.61,430.09\n             447.70,427.42 447.55,427.30 445.92,423.73\n             445.92,423.73 445.90,423.74 445.90,423.74\n             445.31,422.44 444.75,421.14 444.27,419.80", fill: "#757575", stroke: "black", strokeWidth: "0" }), _jsx(BicepsGroup, { getMuscleClasses: getMuscleClasses, onToggleMuscle: onToggleMuscle }), _jsx(ForearmsGroup, { getMuscleClasses: getMuscleClasses, onToggleMuscle: onToggleMuscle }), _jsx(ChestGroup, { getMuscleClasses: getMuscleClasses, onToggleMuscle: onToggleMuscle }), _jsx(TricepsGroup, { getMuscleClasses: getMuscleClasses, onToggleMuscle: onToggleMuscle }), _jsx(AbdominalsGroup, { getMuscleClasses: getMuscleClasses, onToggleMuscle: onToggleMuscle }), _jsx(ObliquesGroup, { getMuscleClasses: getMuscleClasses, onToggleMuscle: onToggleMuscle }), _jsx(QuadricepsGroup, { getMuscleClasses: getMuscleClasses, onToggleMuscle: onToggleMuscle }), _jsx(ShouldersGroup, { getMuscleClasses: getMuscleClasses, onToggleMuscle: onToggleMuscle }), _jsx(BackGroup, { getMuscleClasses: getMuscleClasses, onToggleMuscle: onToggleMuscle }), _jsx(HamstringsGroup, { getMuscleClasses: getMuscleClasses, onToggleMuscle: onToggleMuscle }), _jsx(GlutesGroup, { getMuscleClasses: getMuscleClasses, onToggleMuscle: onToggleMuscle }), _jsx(CalvesGroup, { getMuscleClasses: getMuscleClasses, onToggleMuscle: onToggleMuscle }), _jsx(TrapsGroup, { getMuscleClasses: getMuscleClasses, onToggleMuscle: onToggleMuscle })] }));
};
