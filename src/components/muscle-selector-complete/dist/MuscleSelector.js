import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { MuscleIllustration } from './MuscleIllustration';
import { cn } from './utils';
export var MuscleSelector = function (_a) {
    var selectedMuscles = _a.selectedMuscles, onToggleMuscle = _a.onToggleMuscle, className = _a.className, _b = _a.description, description = _b === void 0 ? "点击选择目标肌肉群" : _b, _c = _a.showDescription, showDescription = _c === void 0 ? true : _c;
    // 使用简洁可靠的样式系统，避免动态颜色值
    var getMuscleClasses = function (muscle) {
        var isSelected = selectedMuscles.includes(muscle);
        return cn("cursor-pointer transition-all duration-100 ease-out", isSelected ? "fill-blue-500" : "fill-slate-400 group-hover:fill-blue-400");
    };
    return (_jsxs("div", { className: cn("space-y-6", className), children: [showDescription && description && (_jsx("div", { className: "text-center mb-6", children: _jsx("p", { className: "text-slate-600 dark:text-slate-300 text-sm italic", children: description }) })), _jsx("div", { className: "flex justify-center", children: _jsx(MuscleIllustration, { selectedMuscles: selectedMuscles, onToggleMuscle: onToggleMuscle, getMuscleClasses: getMuscleClasses }) })] }));
};
