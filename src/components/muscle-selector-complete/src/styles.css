/* 肌肉选择器基础样式 */
.muscle-selector {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* SVG容器样式 */
#muscle-illustration {
  max-width: 100%;
  height: auto;
  user-select: none;
}

/* 肌肉组默认样式 */
.muscle-group {
  cursor: pointer;
  transition: all 0.1s ease-out;
}

/* 默认状态 */
.muscle-default {
  fill: #94a3b8; /* slate-400 */
}

/* 悬停状态 */
.muscle-hover:hover {
  fill: #93c5fd; /* blue-300 */
}

/* 选中状态 */
.muscle-selected {
  fill: #3b82f6; /* blue-500 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  #muscle-illustration {
    max-width: 90%;
  }
}

@media (max-width: 480px) {
  #muscle-illustration {
    max-width: 100%;
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  .muscle-selector {
    color: #f1f5f9;
  }
  
  .muscle-default {
    fill: #64748b; /* slate-500 */
  }
} 