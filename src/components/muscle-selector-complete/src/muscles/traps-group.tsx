import React from 'react';
import { MuscleGroup, MuscleComponentProps } from '../types';

export const TrapsGroup: React.FC<MuscleComponentProps> = ({
  onToggleMuscle,
}) => {
  return (
    <g className="group cursor-pointer" onClick={() => onToggleMuscle(MuscleGroup.TRAPS)}>
      <path
        className="fill-transparent"
        d="M 85.67,85.05
           C 85.67,85.05 87.62,84.15 87.62,84.15
             87.62,84.15 89.87,83.25 89.72,83.25
             89.57,83.25 92.27,83.10 92.27,83.10
             92.27,83.10 94.82,83.55 94.82,83.55
             94.82,83.55 97.52,84.60 97.52,84.60
             97.52,84.60 100.82,85.35 100.82,85.35
             100.82,85.35 107.72,87.75 107.72,87.75
             107.72,87.75 108.62,87.15 108.62,87.15
             108.62,87.15 110.72,86.40 110.72,86.40
             110.72,86.40 118.67,86.40 118.67,86.40
             118.67,86.40 121.07,87.90 121.07,87.90
             121.07,87.90 124.37,87.15 124.37,87.15
             124.37,87.15 127.97,85.50 127.97,85.50
             127.97,85.50 132.77,84.15 132.77,84.15
             132.77,84.15 136.38,83.10 136.38,83.10
             136.38,83.10 139.08,82.95 139.08,82.95
             139.08,82.95 141.18,83.55 141.18,83.55
             141.18,83.55 144.48,85.65 144.48,85.65
             144.48,85.65 145.38,83.85 145.38,83.85
             145.38,83.85 148.08,81.45 148.08,81.45
             148.08,81.45 152.73,78.90 152.73,78.90
             152.73,78.90 131.57,69.15 131.57,69.15
             131.57,69.15 128.87,73.80 128.87,73.80
             128.87,73.80 124.37,79.50 124.37,79.50
             124.37,79.50 119.27,85.20 119.27,85.20
             119.27,85.20 115.22,86.10 115.22,86.10
             115.22,86.10 111.62,85.20 111.62,85.20
             111.62,85.20 109.22,83.40 109.22,83.40
             109.22,83.40 106.37,81.15 106.37,81.15
             106.37,81.15 102.32,76.20 102.32,76.20
             102.32,76.20 99.17,71.70 99.17,71.70
             99.17,71.70 96.32,69.60 96.32,69.60
             96.32,69.60 76.21,78.45 76.21,78.45
             76.21,78.45 77.86,79.95 77.86,79.95
             77.86,79.95 80.42,81.30 80.42,81.30
             80.42,81.30 82.52,82.65 82.52,82.65
             82.52,82.65 84.77,85.20 84.77,85.20"
        data-elem={MuscleGroup.TRAPS}
        stroke="black"
        strokeWidth="0"
      />
    </g>
  );
}; 