import React from 'react';
import { MuscleGroup, MuscleComponentProps } from '../types';

export const AbdominalsGroup: React.FC<MuscleComponentProps> = ({
  onToggleMuscle,
  getMuscleClasses,
}) => {
  return (
    <g className="group cursor-pointer" onClick={() => onToggleMuscle(MuscleGroup.ABDOMINALS)}>
      <path
        className="fill-transparent"
        d="M 100.75,123.75
           C 100.75,123.75 97.50,126.50 97.50,126.50
             97.50,126.50 94.50,140.25 94.50,140.25
             94.50,140.25 93.00,152.50 93.00,152.50
             93.00,152.50 93.00,162.25 93.00,162.25
             93.00,162.25 92.75,172.25 93.00,172.25
             93.25,172.25 97.00,177.00 97.00,177.00
             97.00,177.00 95.50,180.75 95.50,180.75
             95.50,180.75 96.50,193.75 96.50,193.75
             96.50,193.75 99.00,202.00 99.00,202.00
             99.00,202.00 103.50,215.75 103.50,215.75
             103.50,215.75 107.25,220.75 107.25,220.75
             107.25,220.75 111.75,224.50 111.75,224.50
             111.75,224.50 119.25,224.75 119.25,224.75
             119.25,224.75 125.25,217.00 125.25,217.00
             125.25,217.00 131.25,201.75 131.25,201.75
             131.25,201.75 134.75,190.50 134.75,190.50
             134.75,190.50 134.75,179.50 134.75,179.50
             134.75,179.50 132.25,178.25 132.25,178.25
             132.25,178.25 135.25,173.00 135.25,173.00
             135.25,173.00 137.00,166.75 137.00,166.75
             137.00,166.75 136.75,156.50 136.75,156.50
             136.75,156.50 135.75,144.50 135.75,144.50
             135.75,144.50 133.75,133.25 133.75,133.25
             133.75,133.25 130.00,124.75 130.00,124.75
             130.00,124.75 122.50,122.00 122.50,122.00
             122.50,122.00 119.50,122.00 119.50,122.00
             119.50,122.00 114.50,126.75 114.50,126.75
             114.50,126.75 109.50,120.75 109.50,120.75
             109.50,120.75 101.00,124.00 101.00,124.00"
        data-elem={MuscleGroup.ABDOMINALS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroup.ABDOMINALS)}
        d="M 115.58,129.73
           C 115.56,132.16 115.55,134.60 115.56,137.04
             115.56,137.96 116.07,138.54 116.95,138.74
             117.51,138.87 118.08,138.94 118.64,138.99
             123.82,139.50 128.74,140.85 133.25,143.52
             133.57,143.71 133.91,143.86 134.58,144.21
             134.58,143.38 134.68,142.85 134.57,142.37
             133.61,138.14 132.63,133.92 131.61,129.71
             131.16,127.83 130.08,126.36 128.32,125.55
             126.04,124.51 123.71,123.59 121.40,122.62
             120.53,122.26 119.80,122.52 119.30,123.26
             118.12,125.03 116.97,126.83 115.85,128.63
             115.66,128.94 115.59,129.36 115.58,129.73"
        data-elem={MuscleGroup.ABDOMINALS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroup.ABDOMINALS)}
        d="M 113.76,128.72
           C 112.63,126.83 111.41,124.99 110.18,123.16
             109.73,122.48 109.05,122.30 108.26,122.60
             106.58,123.22 104.88,123.78 103.23,124.46
             100.23,125.70 98.19,127.72 97.53,131.07
             96.82,134.65 95.87,138.19 95.08,141.76
             94.93,142.43 95.06,143.16 95.06,143.96
             95.38,143.93 95.50,143.94 95.60,143.90
             95.87,143.77 96.14,143.62 96.40,143.47
             99.73,141.54 103.25,140.17 107.06,139.57
             108.79,139.30 110.53,139.08 112.26,138.76
             113.68,138.50 114.03,138.01 114.04,136.57
             114.04,134.51 114.05,132.45 114.03,130.39
             114.02,129.83 114.03,129.18 113.76,128.72"
        data-elem={MuscleGroup.ABDOMINALS}
        stroke="black"
        strokeWidth="0"
      />
    </g>
  );
}; 