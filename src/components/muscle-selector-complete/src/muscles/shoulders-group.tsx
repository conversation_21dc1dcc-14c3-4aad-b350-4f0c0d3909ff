import React from 'react';
import { MuscleGroup, MuscleComponentProps } from '../types';

export const ShouldersGroup: React.FC<MuscleComponentProps> = ({
  onToggleMuscle,
  getMuscleClasses,
}) => {
  return (
    <g className="group cursor-pointer" onClick={() => onToggleMuscle(MuscleGroup.SHOULDERS)}>
      <path
        className={getMuscleClasses(MuscleGroup.SHOULDERS)}
        d="M 349.30,110.19
           C 350.53,110.32 351.75,110.47 352.37,110.54
             358.40,110.63 363.20,108.85 367.19,105.28
             370.34,102.46 373.12,99.22 376.21,96.32
             377.79,94.83 379.66,93.63 381.47,92.40
             382.21,91.90 382.52,91.35 382.46,90.50
             382.16,85.93 377.78,80.55 373.38,79.53
             372.52,79.33 371.48,79.36 370.65,79.64
             364.68,81.65 359.28,84.64 354.93,89.27
             351.53,92.89 349.96,97.39 349.10,102.16
             348.71,104.40 348.57,106.68 348.31,108.93
             348.22,109.69 348.54,110.10 349.30,110.19"
        data-elem={MuscleGroup.SHOULDERS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroup.SHOULDERS)}
        d="M 474.58,110.19
           C 475.34,110.10 475.66,109.69 475.57,108.93
             475.31,106.68 475.17,104.40 474.78,102.16
             473.92,97.39 472.35,92.89 468.95,89.27
             464.60,84.64 459.20,81.65 453.23,79.64
             452.40,79.36 451.36,79.33 450.50,79.53
             446.10,80.55 441.72,85.93 441.42,90.50
             441.36,91.35 441.67,91.90 442.41,92.40
             444.22,93.63 446.09,94.83 447.67,96.32
             450.76,99.22 453.54,102.46 456.69,105.28
             460.68,108.85 465.48,110.63 471.51,110.54
             472.13,110.47 473.35,110.32 474.58,110.19"
        data-elem={MuscleGroup.SHOULDERS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroup.SHOULDERS)}
        d="M 53.58,115.39
           C 55.17,114.35 57.67,113.27 59.35,112.42
             61.32,111.43 66.87,110.54 68.88,109.64
             72.80,107.90 72.98,109.06 75.32,104.96
             78.40,99.57 82.58,92.75 83.94,86.76
             84.18,85.71 83.88,84.81 83.10,84.18
             81.70,83.08 80.27,82.00 78.74,81.09
             78.18,80.76 77.27,80.82 76.60,80.92
             70.44,81.86 66.80,82.66 62.16,86.15
             59.33,88.28 55.15,92.97 53.87,96.29
             51.53,102.38 52.08,109.59 51.85,116.06
             51.84,116.23 51.93,116.40 52.02,116.76
             52.40,116.42 53.28,115.59 53.58,115.39"
        data-elem={MuscleGroup.SHOULDERS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroup.SHOULDERS)}
        d="M 175.30,115.39
           C 174.71,115.59 173.83,116.42 173.45,116.76
             173.54,116.40 173.63,116.23 173.62,116.06
             173.39,109.59 173.94,102.38 171.60,96.29
             170.32,92.97 166.14,88.28 163.31,86.15
             158.67,82.66 155.03,81.86 148.87,80.92
             148.20,80.82 147.29,80.76 146.73,81.09
             145.20,82.00 143.77,83.08 142.37,84.18
             141.59,84.81 141.29,85.71 141.53,86.76
             142.89,92.75 147.07,99.57 150.15,104.96
             152.49,109.06 152.67,107.90 156.59,109.64
             158.60,110.54 164.15,111.43 166.12,112.42
             167.80,113.27 170.30,114.35 171.89,115.39"
        data-elem={MuscleGroup.SHOULDERS}
        stroke="black"
        strokeWidth="0"
      />
    </g>
  );
}; 