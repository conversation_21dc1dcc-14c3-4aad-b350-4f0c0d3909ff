import React from 'react';
import { MuscleGroup, MuscleComponentProps } from '../types';

export const TricepsGroup: React.FC<MuscleComponentProps> = ({
  onToggleMuscle,
  getMuscleClasses,
}) => {
  return (
    <g className="group cursor-pointer" onClick={() => onToggleMuscle(MuscleGroup.TRICEPS)}>
      <path
        className={getMuscleClasses(MuscleGroup.TRICEPS)}
        d="M 478.44,132.70
           C 477.17,132.60 475.86,132.25 474.77,131.75
             472.83,130.86 471.05,129.76 468.96,128.62
             468.99,129.09 469.03,129.35 469.03,129.62
             469.02,131.66 469.21,133.71 468.95,135.73
             468.64,138.15 467.53,140.35 464.61,141.69
             464.57,141.72 464.53,141.77 464.49,141.82
             464.31,141.83 463.98,141.83 463.36,141.77
             462.16,141.67 460.56,140.30 459.67,139.45
             459.46,139.24 459.25,139.02 459.04,138.81
             459.02,138.79 459.01,138.78 459.01,138.78
             459.01,138.78 459.02,138.79 459.02,138.79
             458.42,138.20 457.82,137.61 457.24,137.01
             457.12,137.06 457.00,137.12 456.88,137.18
             456.94,137.39 456.98,137.61 457.09,137.81
             459.43,142.05 461.66,146.33 464.22,150.51
             465.11,151.97 466.74,153.23 468.22,154.47
             468.66,154.84 469.71,154.83 470.47,155.00
             470.47,155.00 474.13,155.27 474.13,155.27
             474.54,155.34 475.01,155.25 475.55,155.06
             477.12,154.50 478.73,153.95 480.39,153.63
             481.50,153.43 482.01,153.04 482.10,152.23
             482.37,149.83 482.84,147.43 482.80,145.04
             482.73,141.31 482.10,137.61 480.56,134.07
             480.20,133.25 479.68,132.80 478.44,132.70"
        data-elem={MuscleGroup.TRICEPS}
        fill="#757575"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroup.TRICEPS)}
        d="M 346.98,130.86
           C 350.90,128.13 354.53,125.22 356.32,121.01
             357.54,118.12 359.19,115.34 360.62,112.50
             360.88,112.00 361.00,111.45 361.31,110.56
             359.62,110.93 358.34,111.23 357.05,111.50
             354.23,112.09 351.37,112.47 348.49,111.81
             347.26,111.53 346.76,112.16 346.44,112.89
             345.29,115.56 344.01,118.21 343.15,120.94
             342.44,123.17 342.21,125.50 341.84,127.79
             341.75,128.33 341.89,128.91 342.05,129.45
             342.72,131.80 344.74,132.42 346.98,130.86"
        data-elem={MuscleGroup.TRICEPS}
        fill="#757575"
        stroke="black"
        strokeWidth="0"
      />
    </g>
  );
}; 