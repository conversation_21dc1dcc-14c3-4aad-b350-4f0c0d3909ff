import React from 'react';
import { MuscleGroup, MuscleComponentProps } from '../types';

export const BackGroup: React.FC<MuscleComponentProps> = ({
  onToggleMuscle,
  getMuscleClasses,
}) => {
  return (
    <g className="group cursor-pointer" onClick={() => onToggleMuscle(MuscleGroup.BACK)}>
      <path
        className={getMuscleClasses(MuscleGroup.BACK)}
        d="M 392.64,149.10
           C 392.27,149.18 392.12,149.19 391.99,149.25
             389.72,150.24 387.35,150.69 384.89,150.56
             382.22,150.41 379.57,149.93 377.87,147.62
             376.09,145.22 374.53,142.65 372.91,140.13
             372.62,139.66 372.48,139.10 372.27,138.58
             372.18,138.60 372.08,138.62 371.99,138.64
             371.98,138.82 371.93,139.01 371.96,139.19
             372.90,144.96 373.51,150.78 375.45,156.38
             376.89,160.53 378.59,164.50 381.25,168.00
             382.04,169.04 382.99,169.97 383.98,170.82
             385.11,171.81 385.47,171.64 385.74,170.20
             385.76,170.09 385.77,169.97 385.79,169.86
             386.85,164.12 388.32,158.50 390.74,153.17
             391.33,151.86 391.95,150.56 392.64,149.10"
        data-elem={MuscleGroup.BACK}
        id="path30"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroup.BACK)}
        d="M 451.05,138.61
           C 450.93,138.58 450.82,138.55 450.71,138.52
             450.53,138.88 450.38,139.24 450.18,139.59
             449.02,141.63 447.96,143.74 446.65,145.68
             445.30,147.67 443.64,149.45 441.18,150.09
             437.83,150.96 434.53,150.71 431.32,149.38
             431.04,149.26 430.74,149.20 430.17,149.02
             434.12,156.19 436.09,163.69 437.56,171.73
             438.40,171.16 439.01,170.83 439.51,170.39
             441.99,168.16 443.69,165.37 445.13,162.40
             448.22,156.01 449.70,149.17 450.64,142.19
             450.80,141.00 450.91,139.80 451.05,138.61"
        data-elem={MuscleGroup.BACK}
        id="path28"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroup.BACK)}
        d="M 403.69,137.15
           C 405.75,134.61 405.91,133.56 404.16,130.82
             401.53,126.69 398.77,122.65 396.09,118.55
             396.09,118.55 394.01,115.00 394.01,115.00
             394.01,115.00 393.84,114.97 393.84,114.97
             393.73,114.71 393.57,114.43 393.34,114.11
             392.00,112.22 390.57,110.40 389.33,108.45
             386.85,104.57 385.39,100.28 384.61,95.75
             384.32,94.04 383.40,93.64 381.89,94.51
             381.26,94.88 380.66,95.31 380.09,95.76
             375.26,99.63 372.19,104.64 370.49,110.55
             370.35,111.05 370.27,111.45 370.24,111.80
             370.24,111.80 370.24,111.80 370.24,111.80
             370.24,111.80 370.05,116.31 370.05,116.31
             369.93,118.08 369.84,119.85 369.73,121.63
             369.82,123.32 369.87,125.01 370.02,126.70
             370.53,132.63 372.41,138.07 375.97,142.87
             378.15,145.81 381.03,147.83 384.60,148.72
             388.07,149.59 391.14,148.39 393.98,146.49
             397.77,143.96 400.84,140.67 403.69,137.15"
        data-elem={MuscleGroup.BACK}
        id="path26"
        stroke="black"
        strokeWidth="0"
      />
      <path
        className={getMuscleClasses(MuscleGroup.BACK)}
        d="M 452.92,116.24
           C 452.92,116.24 452.75,111.80 452.75,111.80
             452.75,111.80 452.74,111.80 452.74,111.80
             452.71,111.52 452.65,111.21 452.55,110.84
             452.25,109.81 451.90,108.79 451.53,107.77
             449.58,102.43 446.27,98.13 441.63,94.86
             439.64,93.45 438.68,93.88 438.25,96.24
             437.60,99.79 436.62,103.26 434.81,106.37
             433.22,109.09 431.31,111.63 429.54,114.24
             429.54,114.24 426.99,118.41 426.99,118.41
             424.12,122.80 421.24,127.18 418.38,131.58
             417.40,133.09 417.39,134.61 418.48,136.10
             421.78,140.59 425.64,144.48 430.44,147.38
             434.04,149.56 437.71,149.46 441.37,147.65
             445.32,145.70 447.79,142.32 449.73,138.50
             452.39,133.25 453.16,127.59 453.20,120.88
             453.14,119.93 453.06,118.08 452.92,116.24"
        data-elem={MuscleGroup.BACK}
        id="path24"
        stroke="black"
        strokeWidth="0"
      />
      <path
        className={getMuscleClasses(MuscleGroup.BACK)}
        d="M 410.63,151.32
           C 410.49,151.32 410.49,146.96 410.13,144.87
             409.76,142.69 408.79,140.61 408.02,138.52
             407.56,137.28 406.89,137.09 405.79,137.79
             405.34,138.08 404.93,138.45 404.57,138.85
             403.06,140.50 401.58,142.17 400.09,143.84
             398.48,145.65 396.85,147.45 395.30,149.32
             393.16,151.90 391.78,154.91 390.52,157.99
             388.87,162.00 388.06,166.16 387.83,170.47
             387.78,171.37 388.11,171.83 388.97,172.01
             390.71,172.37 392.02,173.38 393.15,174.72
             396.18,178.32 397.67,182.67 399.17,187.01
             400.63,191.28 402.01,195.58 403.62,199.79
             405.05,203.55 406.98,207.07 409.47,210.26
             409.68,210.53 410.06,210.67 410.37,210.86
             410.50,210.53 410.75,210.20 410.76,209.86
             410.78,209.06 410.64,208.25 410.64,207.44
             410.63,188.73 410.63,170.03 410.63,151.32"
        data-elem={MuscleGroup.BACK}
        id="path22"
        stroke="black"
        strokeWidth="0"
      />
      <path
        className={getMuscleClasses(MuscleGroup.BACK)}
        d="M 412.34,201.13
           C 412.33,204.02 412.23,206.91 412.21,209.80
             412.20,210.15 412.41,210.50 412.52,210.85
             412.85,210.64 413.29,210.51 413.49,210.21
             414.63,208.52 415.79,206.83 416.81,205.06
             419.82,199.80 421.42,193.97 423.33,188.28
             424.66,184.33 425.93,180.34 428.26,176.84
             429.59,174.85 430.96,172.87 433.48,172.15
             435.22,171.65 435.24,171.61 435.12,169.77
             434.62,162.73 432.68,156.10 428.46,150.45
             425.26,146.17 421.44,142.37 417.87,138.37
             417.58,138.04 417.14,137.81 416.73,137.61
             416.04,137.26 415.36,137.37 415.11,138.14
             414.24,140.80 413.03,143.44 412.73,146.18
             412.28,150.18 412.47,154.26 412.45,158.31
             412.42,165.95 412.44,173.58 412.44,181.22
             412.41,181.22 412.38,181.22 412.35,181.22
             412.35,187.86 412.36,194.49 412.34,201.13"
        data-elem={MuscleGroup.BACK}
        id="path20"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className="fill-transparent"
        d="M 385.00,92.75
           C 385.00,92.75 379.75,94.25 379.75,94.50
             379.75,94.75 372.75,102.25 372.75,102.25
             372.75,102.25 370.25,106.75 370.25,106.75
             370.25,106.75 369.50,115.75 369.50,115.75
             369.50,115.75 369.25,130.00 369.25,130.00
             369.25,130.00 369.25,139.25 369.25,139.25
             369.25,139.25 370.00,151.25 370.00,151.25
             370.00,151.25 373.25,159.75 373.25,159.75
             373.25,159.75 375.00,163.25 375.00,163.25
             375.00,163.25 369.75,170.50 369.75,170.50
             369.75,170.50 369.75,177.25 369.75,177.25
             369.75,177.25 370.25,180.25 370.25,180.25
             370.25,180.25 368.25,191.00 368.25,191.00
             368.25,191.00 367.25,202.00 367.25,202.00
             367.25,202.00 367.75,207.25 367.75,207.25
             367.75,207.25 375.75,196.25 375.75,196.00
             375.75,195.75 381.75,189.25 381.75,189.25
             381.75,189.25 396.25,184.50 396.25,184.50
             396.25,184.50 391.75,176.25 391.75,176.25
             391.75,176.25 386.50,172.25 386.50,172.25
             386.50,172.25 388.25,161.50 388.25,161.50
             388.25,161.50 393.75,148.75 393.75,148.75
             393.75,148.75 399.75,141.50 399.75,141.50
             399.75,141.50 406.50,135.75 406.50,135.75
             406.50,135.75 405.50,129.75 405.50,129.75
             405.50,129.75 398.75,120.75 398.75,120.75
             398.75,120.75 393.75,112.75 393.75,112.75
             393.75,112.75 388.75,106.00 388.75,106.00
             388.75,106.00 386.25,99.25 386.25,99.25
             386.25,99.25 385.25,93.75 385.25,93.75"
        data-elem={MuscleGroup.BACK}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className="fill-transparent"
        d="M 415.75,134.25
           C 415.75,134.25 422.25,123.00 422.25,123.00
             422.25,123.00 432.00,109.75 432.00,109.75
             432.00,109.75 436.25,101.25 436.25,101.25
             436.25,101.25 437.75,93.00 437.75,93.00
             437.75,93.00 443.00,95.50 443.00,95.50
             443.00,95.50 449.75,100.25 449.75,100.25
             449.75,100.25 453.00,107.00 453.00,107.00
             453.00,107.00 453.50,118.25 453.50,118.25
             453.50,118.25 453.75,132.25 453.75,132.25
             453.75,132.25 453.75,139.75 453.75,139.75
             453.75,139.75 451.00,153.25 451.00,153.25
             451.00,153.25 448.25,163.25 448.25,163.25
             448.25,163.25 452.00,168.50 452.00,168.50
             452.00,168.50 452.50,177.00 452.50,177.00
             452.50,177.00 452.50,182.00 452.50,182.00
             452.50,182.00 457.00,197.50 457.00,197.50
             457.00,197.50 456.25,205.75 456.25,205.75
             456.25,205.75 451.50,199.75 451.50,199.75
             451.50,199.75 446.75,195.00 446.75,195.00
             446.75,195.00 443.00,190.75 443.00,190.75
             443.00,190.75 437.00,187.50 437.00,187.50
             437.00,187.50 428.75,186.00 428.75,186.00
             428.75,186.00 427.25,184.25 427.25,184.25
             427.25,184.25 430.00,178.50 430.00,178.50
             430.00,178.50 433.00,174.00 433.00,174.00
             433.00,174.00 436.25,172.75 436.25,172.75
             436.25,172.75 436.00,165.75 436.00,165.75
             436.00,165.75 433.25,156.25 433.25,156.25
             433.25,156.25 428.25,148.25 428.25,148.25
             428.25,148.25 422.50,142.00 422.50,142.00
             422.50,142.00 418.50,137.75 416.00,134.75"
        data-elem={MuscleGroup.BACK}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroup.BACK)}
        d="M 446.16,166.84
           C 444.58,168.72 443.04,170.63 441.50,172.50
             443.05,173.60 444.59,174.58 446.00,175.71
             446.86,176.40 447.49,177.34 448.30,178.09
             448.60,178.37 449.21,178.65 449.51,178.53
             449.82,178.40 450.13,177.76 450.06,177.41
             449.34,173.89 448.56,170.38 447.72,166.88
             447.53,166.08 446.81,166.06 446.16,166.84"
        data-elem={MuscleGroup.BACK}
        id="path60"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroup.BACK)}
        d="M 431.03,186.03
           C 434.21,186.80 437.40,187.52 440.48,188.54
             441.32,188.82 442.10,189.23 442.87,189.68
             442.87,189.68 445.13,191.11 445.13,191.11
             445.13,191.11 449.09,195.08 449.09,195.08
             449.33,195.53 449.57,195.98 449.80,196.44
             450.49,197.83 451.46,199.09 452.41,200.34
             452.67,200.67 453.30,200.72 453.76,200.90
             453.87,200.44 454.15,199.95 454.07,199.53
             453.35,195.95 452.67,192.36 451.79,188.83
             450.99,185.60 449.99,182.38 447.97,179.71
             445.23,176.07 441.55,174.00 437.18,173.73
             436.64,173.83 436.26,173.80 436.01,173.96
             435.76,174.12 435.47,174.28 435.22,174.45
             435.22,174.45 435.21,174.45 435.21,174.45
             435.21,174.45 435.20,174.46 435.20,174.46
             435.00,174.60 434.83,174.75 434.70,174.91
             434.70,174.91 432.49,176.89 432.49,176.89
             432.49,176.89 432.52,176.99 432.52,176.99
             432.21,177.19 431.96,177.57 431.75,178.14
             430.99,180.15 430.27,182.18 429.71,184.24
             429.36,185.52 429.71,185.71 431.03,186.03"
        data-elem={MuscleGroup.BACK}
        id="path56"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroup.BACK)}
        d="M 374.54,178.09
           C 375.35,177.34 375.98,176.40 376.84,175.71
             378.25,174.58 379.79,173.60 381.34,172.50
             379.80,170.63 378.26,168.72 376.68,166.84
             376.02,166.06 375.31,166.08 375.12,166.88
             374.28,170.38 373.50,173.89 372.78,177.41
             372.70,177.76 373.02,178.40 373.33,178.53
             373.63,178.65 374.24,178.37 374.54,178.09"
        data-elem={MuscleGroup.BACK}
        id="path44"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroup.BACK)}
        d="M 388.14,174.91
           C 388.01,174.75 387.84,174.60 387.64,174.46
             387.64,174.46 387.63,174.45 387.63,174.45
             387.63,174.45 387.62,174.45 387.62,174.45
             387.37,174.28 387.08,174.12 386.83,173.96
             386.57,173.80 386.20,173.83 385.66,173.73
             381.29,174.00 377.61,176.07 374.87,179.71
             372.85,182.38 371.85,185.60 371.05,188.83
             370.17,192.36 369.48,195.95 368.77,199.53
             368.69,199.95 368.97,200.44 369.08,200.90
             369.54,200.72 370.17,200.67 370.43,200.34
             371.38,199.09 372.35,197.83 373.04,196.44
             373.27,195.98 373.51,195.53 373.74,195.08
             373.74,195.08 377.71,191.11 377.71,191.11
             377.71,191.11 379.97,189.68 379.97,189.68
             380.74,189.23 381.52,188.82 382.35,188.54
             385.44,187.52 388.63,186.80 391.81,186.03
             393.13,185.71 393.48,185.52 393.13,184.24
             392.56,182.18 391.85,180.15 391.09,178.14
             390.88,177.57 390.63,177.19 390.32,176.99
             390.32,176.99 390.35,176.89 390.35,176.89
             390.35,176.89 388.14,174.91 388.14,174.91"
        data-elem={MuscleGroup.BACK}
        id="path36"
        stroke="black"
        strokeWidth="0"
      />
    </g>
  );
}; 