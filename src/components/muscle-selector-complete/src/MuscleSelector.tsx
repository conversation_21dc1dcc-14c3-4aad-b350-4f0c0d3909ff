import React from 'react';
import { MuscleSelectionProps } from './types';
import { MuscleIllustration } from './MuscleIllustration';
import { cn } from './utils';

export const MuscleSelector: React.FC<MuscleSelectionProps> = ({
  selectedMuscles,
  onToggleMuscle,
  className,
  description = "点击选择目标肌肉群",
  showDescription = true,
}) => {
  
  // 使用简洁可靠的样式系统，避免动态颜色值
  const getMuscleClasses = (muscle: any) => {
    const isSelected = selectedMuscles.includes(muscle);
    return cn(
      "cursor-pointer transition-all duration-100 ease-out",
      isSelected ? "fill-blue-500" : "fill-slate-400 group-hover:fill-blue-400"
    );
  };

  return (
    <div className={cn("space-y-6", className)}>
      {showDescription && description && (
        <div className="text-center mb-6">
          <p className="text-slate-600 dark:text-slate-300 text-sm italic">
            {description}
          </p>
        </div>
      )}

      <div className="flex justify-center">
        <MuscleIllustration 
          selectedMuscles={selectedMuscles}
          onToggleMuscle={onToggleMuscle}
          getMuscleClasses={getMuscleClasses}
        />
      </div>
    </div>
  );
}; 