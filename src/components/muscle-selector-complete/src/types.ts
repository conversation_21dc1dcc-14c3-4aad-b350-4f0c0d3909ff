// 肌肉群枚举 - 与原项目完全一致
export enum MuscleGroup {
  BICEPS = "BICEPS",
  CHEST = "CHEST",
  BACK = "BACK",
  SHOULDERS = "SHOULDERS",
  TRICEPS = "TRICEPS",
  QUADRICEPS = "QUADRICEPS",
  HAMSTRINGS = "HAMSTRINGS",
  GLUTES = "GLUTES",
  CALVES = "CALVES",
  ABDOMINALS = "ABDOMINALS",
  OBLIQUES = "OBLIQUES",
  FOREARMS = "FOREARMS",
  TRAPS = "TRAPS"
}

// 肌肉群中文名称映射
export const MUSCLE_NAMES: Record<MuscleGroup, string> = {
  [MuscleGroup.BICEPS]: "二头肌",
  [MuscleGroup.CHEST]: "胸肌",
  [MuscleGroup.BACK]: "背肌",
  [MuscleGroup.SHOULDERS]: "肩部",
  [MuscleGroup.TRICEPS]: "三头肌",
  [MuscleGroup.QUADRICEPS]: "股四头肌",
  [MuscleGroup.HAMSTRINGS]: "腘绳肌",
  [MuscleGroup.GLUTES]: "臀肌",
  [MuscleGroup.CALVES]: "小腿肌",
  [MuscleGroup.ABDOMINALS]: "腹肌",
  [MuscleGroup.OBLIQUES]: "腹斜肌",
  [MuscleGroup.FOREARMS]: "前臂",
  [MuscleGroup.TRAPS]: "斜方肌"
};

// 主要组件Props接口
export interface MuscleSelectionProps {
  selectedMuscles: MuscleGroup[];
  onToggleMuscle: (muscle: MuscleGroup) => void;
  className?: string;
  description?: string;
  showDescription?: boolean;
}

// 肌肉组件的通用Props
export interface MuscleComponentProps {
  onToggleMuscle: (muscle: MuscleGroup) => void;
  getMuscleClasses: (muscle: MuscleGroup) => string;
} 