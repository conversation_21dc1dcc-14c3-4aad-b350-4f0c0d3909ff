import React, { useState } from 'react';
import { MuscleSelector, MuscleGroup, MUSCLE_NAMES } from './src/index';
import './src/styles.css';

/**
 * 🏋️‍♀️ 完整的肌肉选择器使用示例
 * 
 * 这个示例展示了如何在项目中使用完整的肌肉选择器组件
 * 组件完全独立，无任何外部依赖，可直接复制到任何React项目中使用
 */
export const MuscleSelectionExample: React.FC = () => {
  const [selectedMuscles, setSelectedMuscles] = useState<MuscleGroup[]>([]);

  const handleToggleMuscle = (muscle: MuscleGroup) => {
    setSelectedMuscles(prev => 
      prev.includes(muscle) 
        ? prev.filter(m => m !== muscle)
        : [...prev, muscle]
    );
  };

  const handleQuickSelect = (muscles: MuscleGroup[]) => {
    setSelectedMuscles(muscles);
  };

  const clearSelection = () => {
    setSelectedMuscles([]);
  };

  return (
    <div style={{ 
      padding: '40px', 
      maxWidth: '1000px', 
      margin: '0 auto',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* 标题区域 */}
      <div style={{ textAlign: 'center', marginBottom: '40px' }}>
        <h1 style={{ color: '#1e293b', marginBottom: '10px', fontSize: '32px' }}>
          🏋️‍♀️ 人体肌肉选择器
        </h1>
        <p style={{ color: '#64748b', fontSize: '16px', margin: '0' }}>
          完整的SVG交互式人体肌肉图，支持13个精准肌肉群选择
        </p>
      </div>

      {/* 已选择的肌肉群显示 */}
      <div style={{ 
        background: '#ecfdf5', 
        borderRadius: '8px', 
        padding: '20px', 
        marginBottom: '32px',
        border: '1px solid #10b981'
      }}>
        <h3 style={{ margin: '0 0 12px 0', color: '#065f46', fontSize: '18px' }}>
          已选择的肌肉群 ({selectedMuscles.length}/13)
        </h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
          {selectedMuscles.length > 0 ? (
            selectedMuscles.map(muscle => (
              <span
                key={muscle}
                style={{
                  background: '#3b82f6',
                  color: 'white',
                  padding: '6px 12px',
                  borderRadius: '6px',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                {MUSCLE_NAMES[muscle]}
              </span>
            ))
          ) : (
            <span style={{ color: '#6b7280', fontStyle: 'italic' }}>
              请点击下方人体图选择肌肉群
            </span>
          )}
        </div>
      </div>

      {/* 主要的肌肉选择组件 */}
      <div style={{ 
        background: 'white', 
        borderRadius: '12px', 
        padding: '32px',
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        border: '1px solid #e5e7eb',
        marginBottom: '32px'
      }}>
        <MuscleSelector
          selectedMuscles={selectedMuscles}
          onToggleMuscle={handleToggleMuscle}
          description="点击下方人体图的肌肉区域来选择目标肌肉群"
          showDescription={true}
          className="muscle-selector"
        />
      </div>

      {/* 快速选择按钮 */}
      <div style={{ 
        background: '#f8fafc', 
        borderRadius: '8px', 
        padding: '20px',
        marginBottom: '32px'
      }}>
        <h3 style={{ margin: '0 0 16px 0', color: '#374151', fontSize: '16px' }}>
          快速选择组合
        </h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '12px' }}>
          <button 
            onClick={() => handleQuickSelect([MuscleGroup.CHEST, MuscleGroup.SHOULDERS, MuscleGroup.TRICEPS])}
            style={{
              padding: '8px 16px',
              borderRadius: '6px',
              border: '1px solid #d1d5db',
              background: 'white',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            上半身推 (胸肌+肩部+三头)
          </button>
          <button 
            onClick={() => handleQuickSelect([MuscleGroup.BACK, MuscleGroup.BICEPS])}
            style={{
              padding: '8px 16px',
              borderRadius: '6px',
              border: '1px solid #d1d5db',
              background: 'white',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            上半身拉 (背肌+二头)
          </button>
          <button 
            onClick={() => handleQuickSelect([MuscleGroup.QUADRICEPS, MuscleGroup.GLUTES, MuscleGroup.CALVES])}
            style={{
              padding: '8px 16px',
              borderRadius: '6px',
              border: '1px solid #d1d5db',
              background: 'white',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            下半身 (股四头+臀部+小腿)
          </button>
          <button 
            onClick={() => handleQuickSelect([MuscleGroup.ABDOMINALS, MuscleGroup.OBLIQUES])}
            style={{
              padding: '8px 16px',
              borderRadius: '6px',
              border: '1px solid #d1d5db',
              background: 'white',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            核心 (腹肌+腹斜肌)
          </button>
          <button 
            onClick={clearSelection}
            style={{
              padding: '8px 16px',
              borderRadius: '6px',
              border: '1px solid #ef4444',
              background: '#fef2f2',
              color: '#dc2626',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            清除选择
          </button>
        </div>
      </div>

      {/* 技术信息 */}
      <div style={{ 
        background: '#f1f5f9', 
        borderRadius: '8px', 
        padding: '20px',
        fontSize: '14px',
        color: '#475569'
      }}>
        <h3 style={{ margin: '0 0 12px 0', color: '#1e293b' }}>
          📊 技术特性
        </h3>
        <ul style={{ margin: 0, paddingLeft: '20px' }}>
          <li>✅ 完整的13个肌肉群支持</li>
          <li>✅ 基于SVG的高精度人体轮廓</li>
          <li>✅ 零外部依赖，纯React实现</li>
          <li>✅ TypeScript完整类型支持</li>
          <li>✅ 响应式设计，适配所有设备</li>
          <li>✅ 简洁可靠的样式系统</li>
        </ul>
      </div>
    </div>
  );
};

export default MuscleSelectionExample; 