# 🏋️‍♀️ 人体肌肉选择器 (Muscle Selector Complete)

一个完整的、可直接使用的人体肌肉选择器React组件，基于SVG绘制，支持13个精准肌肉群的交互式选择。

## ✨ 功能特性

- 🎯 **交互式人体图**: 基于SVG的高精度人体肌肉图，完整复制自原项目
- 🖱️ **点击选择**: 支持点击选择/取消选择肌肉群，提供即时视觉反馈
- 🎨 **简洁样式**: 使用可靠的Tailwind CSS类名，避免动态样式问题
- 📱 **响应式设计**: 完美适配移动端和桌面端设备
- ⚡ **零外部依赖**: 纯React实现，无任何外部依赖
- 🔧 **TypeScript**: 完整的类型定义支持
- 🌐 **独立组件**: 可直接迁移到任何React项目

## 🎯 支持的肌肉群

| 肌肉群 | 英文名称 | 中文名称 |
|--------|----------|----------|
| `BICEPS` | Biceps | 二头肌 |
| `CHEST` | Chest | 胸肌 |
| `BACK` | Back | 背肌 |
| `SHOULDERS` | Shoulders | 肩部 |
| `TRICEPS` | Triceps | 三头肌 |
| `ABDOMINALS` | Abdominals | 腹肌 |
| `OBLIQUES` | Obliques | 腹斜肌 |
| `QUADRICEPS` | Quadriceps | 股四头肌 |
| `HAMSTRINGS` | Hamstrings | 腘绳肌 |
| `GLUTES` | Glutes | 臀肌 |
| `CALVES` | Calves | 小腿肌 |
| `FOREARMS` | Forearms | 前臂 |
| `TRAPS` | Traps | 斜方肌 |

## 🚀 快速开始

### 1. 复制组件到您的项目

```bash
# 将整个 muscle-selector-complete 文件夹复制到您的项目中
cp -r muscle-selector-complete /path/to/your-project/
```

### 2. 基础使用

```tsx
import React, { useState } from 'react';
import { MuscleSelector, MuscleGroup } from './muscle-selector-complete/src';
import './muscle-selector-complete/src/styles.css';

function App() {
  const [selectedMuscles, setSelectedMuscles] = useState<MuscleGroup[]>([]);

  const handleToggleMuscle = (muscle: MuscleGroup) => {
    setSelectedMuscles(prev => 
      prev.includes(muscle) 
        ? prev.filter(m => m !== muscle)
        : [...prev, muscle]
    );
  };

  return (
    <div>
      <h1>选择目标肌肉群</h1>
      <MuscleSelector
        selectedMuscles={selectedMuscles}
        onToggleMuscle={handleToggleMuscle}
        description="点击下方人体图选择目标肌肉群"
        showDescription={true}
      />
      
      <div>
        <h3>已选择: {selectedMuscles.length} 个肌肉群</h3>
        <ul>
          {selectedMuscles.map(muscle => (
            <li key={muscle}>{muscle}</li>
          ))}
        </ul>
      </div>
    </div>
  );
}

export default App;
```

### 3. 高级使用示例

```tsx
import React, { useState } from 'react';
import { MuscleSelector, MuscleGroup, MUSCLE_NAMES } from './muscle-selector-complete/src';

function AdvancedExample() {
  const [selectedMuscles, setSelectedMuscles] = useState<MuscleGroup[]>([]);

  const handleToggleMuscle = (muscle: MuscleGroup) => {
    setSelectedMuscles(prev => 
      prev.includes(muscle) 
        ? prev.filter(m => m !== muscle)
        : [...prev, muscle]
    );
  };

  // 快速选择预设组合
  const selectUpperBodyPush = () => {
    setSelectedMuscles([MuscleGroup.CHEST, MuscleGroup.SHOULDERS, MuscleGroup.TRICEPS]);
  };

  const selectUpperBodyPull = () => {
    setSelectedMuscles([MuscleGroup.BACK, MuscleGroup.BICEPS]);
  };

  const selectLowerBody = () => {
    setSelectedMuscles([MuscleGroup.QUADRICEPS, MuscleGroup.GLUTES, MuscleGroup.HAMSTRINGS]);
  };

  return (
    <div>
      {/* 快速选择按钮 */}
      <div style={{ marginBottom: '20px' }}>
        <button onClick={selectUpperBodyPush}>上半身推</button>
        <button onClick={selectUpperBodyPull}>上半身拉</button>
        <button onClick={selectLowerBody}>下半身</button>
        <button onClick={() => setSelectedMuscles([])}>清除</button>
      </div>

      {/* 肌肉选择器 */}
      <MuscleSelector
        selectedMuscles={selectedMuscles}
        onToggleMuscle={handleToggleMuscle}
        className="my-custom-selector"
      />

      {/* 选择结果显示 */}
      <div style={{ marginTop: '20px' }}>
        <h3>已选择的肌肉群 ({selectedMuscles.length}/13):</h3>
        {selectedMuscles.map(muscle => (
          <span key={muscle} style={{ 
            display: 'inline-block', 
            margin: '4px', 
            padding: '4px 8px', 
            background: '#3b82f6', 
            color: 'white', 
            borderRadius: '4px' 
          }}>
            {MUSCLE_NAMES[muscle]}
          </span>
        ))}
      </div>
    </div>
  );
}
```

## 📦 组件API

### MuscleSelector Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `selectedMuscles` | `MuscleGroup[]` | - | 已选择的肌肉群数组 |
| `onToggleMuscle` | `(muscle: MuscleGroup) => void` | - | 切换肌肉群选择状态的回调函数 |
| `className` | `string` | - | 自定义CSS类名 |
| `description` | `string` | `"点击选择目标肌肉群"` | 描述文字 |
| `showDescription` | `boolean` | `true` | 是否显示描述文字 |

### MuscleGroup 枚举

```typescript
enum MuscleGroup {
  BICEPS = "BICEPS",
  CHEST = "CHEST",
  BACK = "BACK",
  SHOULDERS = "SHOULDERS",
  TRICEPS = "TRICEPS",
  QUADRICEPS = "QUADRICEPS",
  HAMSTRINGS = "HAMSTRINGS",
  GLUTES = "GLUTES",
  CALVES = "CALVES",
  ABDOMINALS = "ABDOMINALS",
  OBLIQUES = "OBLIQUES",
  FOREARMS = "FOREARMS",
  TRAPS = "TRAPS"
}
```

### MUSCLE_NAMES 映射

```typescript
const MUSCLE_NAMES: Record<MuscleGroup, string> = {
  [MuscleGroup.BICEPS]: "二头肌",
  [MuscleGroup.CHEST]: "胸肌",
  // ... 其他映射
};
```

## 🎨 样式定制

### 使用CSS变量

```css
:root {
  --muscle-default-color: #94a3b8;  /* 默认状态颜色 */
  --muscle-hover-color: #93c5fd;    /* 悬停状态颜色 */
  --muscle-active-color: #3b82f6;   /* 选中状态颜色 */
}
```

### 自定义样式类

```css
.my-custom-selector .muscle-default {
  fill: #6b7280;
}

.my-custom-selector .muscle-selected {
  fill: #ef4444;
}

.my-custom-selector .muscle-hover:hover {
  fill: #f87171;
}
```

## 🔧 技术特点

### SVG结构
- **viewBox**: `0 0 535 462` (精确的原始比例)
- **路径数量**: 15+个详细的人体轮廓路径
- **肌肉组件**: 13个独立的肌肉群组件
- **文件大小**: ~25KB (未压缩)

### 样式系统
- ✅ 使用标准Tailwind CSS类名
- ✅ 避免动态任意值语法问题
- ✅ 支持group-hover效果
- ✅ 完全响应式设计

### 性能特点
- ✅ 纯SVG渲染，性能优异
- ✅ 无外部依赖，加载速度快
- ✅ 支持服务端渲染(SSR)
- ✅ 支持代码分割

## 📱 响应式支持

组件自动适配不同设备屏幕：

```css
/* 平板设备 */
@media (max-width: 768px) {
  #muscle-illustration {
    max-width: 90%;
  }
}

/* 手机设备 */
@media (max-width: 480px) {
  #muscle-illustration {
    max-width: 100%;
  }
}
```

## 🌙 暗色模式支持

```css
@media (prefers-color-scheme: dark) {
  .muscle-selector {
    color: #f1f5f9;
  }
  
  .muscle-default {
    fill: #64748b;
  }
}
```

## 🛠️ 开发构建

```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 构建
npm run build
```

## 📄 文件结构

```
muscle-selector-complete/
├── src/
│   ├── muscles/              # 肌肉组件
│   │   ├── biceps-group.tsx
│   │   ├── chest-group.tsx
│   │   └── ... (其他肌肉组件)
│   ├── types.ts              # 类型定义
│   ├── utils.ts              # 工具函数
│   ├── MuscleIllustration.tsx # SVG容器组件
│   ├── MuscleSelector.tsx     # 主组件
│   ├── styles.css            # 样式文件
│   └── index.ts              # 导出文件
├── example.tsx               # 使用示例
├── package.json              # 包配置
├── tsconfig.json             # TS配置
└── README.md                 # 本文档
```

## 🤝 迁移到其他项目

### 方法1: 直接复制

```bash
# 将组件文件夹复制到目标项目
cp -r muscle-selector-complete /path/to/target-project/src/components/

# 在目标项目中使用
import { MuscleSelector, MuscleGroup } from '@/components/muscle-selector-complete/src';
```

### 方法2: npm包形式

```bash
# 在muscle-selector-complete目录下
npm pack

# 在目标项目中安装
npm install ./muscle-selector-complete-1.0.0.tgz
```

### 方法3: 作为子模块

```bash
# 在目标项目中添加为git子模块
git submodule add ./muscle-selector-complete src/components/muscle-selector
```

## ❗ 重要说明

1. **样式系统**: 组件使用标准Tailwind CSS类名，确保与原项目完全一致的视觉效果
2. **无外部依赖**: 组件完全独立，无需安装额外的npm包
3. **类型安全**: 完整的TypeScript类型定义，提供良好的开发体验
4. **浏览器兼容**: 支持所有现代浏览器，包括移动端

## 📝 许可证

MIT License - 可自由使用于商业和个人项目

---

**💡 提示**: 这个组件是从完整的健身应用项目中提取并独立化的，保持了原项目的所有功能和视觉效果，可以直接用于任何需要肌肉选择功能的项目中。 