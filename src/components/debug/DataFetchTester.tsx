/**
 * 数据获取测试组件
 * 用于测试真实API数据获取和分析
 */

import React, { useState } from 'react';
import { TestDataFetcher } from '../../utils/testDataFetcher';
import { authService } from '../../services/authService';

interface TestResult {
  success: boolean;
  data?: any;
  analysis?: any;
  error?: string;
  timestamp: string;
}

export const DataFetchTester: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [authStatus, setAuthStatus] = useState({
    isAuthenticated: authService.isAuthenticated(),
    user: authService.getCurrentUser()
  });

  // 测试数据获取
  const handleTestDataFetch = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      console.log('【测试组件】开始测试数据获取');
      
      const result = await TestDataFetcher.fetchAndAnalyzeRealFeedData();
      
      const testResult: TestResult = {
        ...result,
        timestamp: new Date().toISOString()
      };

      setTestResult(testResult);

      // 如果获取成功，保存数据样例
      if (result.success && result.data) {
        await TestDataFetcher.saveDataSample(result.data);
        
        // 生成并下载分析报告
        const report = TestDataFetcher.generateDataStructureReport(result.analysis);
        const blob = new Blob([report], { type: 'text/markdown' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'data-structure-analysis.md';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }

      // 更新认证状态
      setAuthStatus({
        isAuthenticated: authService.isAuthenticated(),
        user: authService.getCurrentUser()
      });

    } catch (error) {
      console.error('【测试组件】测试失败:', error);
      
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 测试认证
  const handleTestAuth = async () => {
    setIsLoading(true);
    
    try {
      console.log('【测试组件】开始测试认证');
      
      const user = await authService.loginWithTestUser();
      
      setAuthStatus({
        isAuthenticated: true,
        user
      });

      console.log('【测试组件】认证测试成功:', user);
      
    } catch (error) {
      console.error('【测试组件】认证测试失败:', error);
      
      setAuthStatus({
        isAuthenticated: false,
        user: null
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 清除测试结果
  const handleClearResults = () => {
    setTestResult(null);
  };

  // 登出
  const handleLogout = () => {
    authService.logout();
    setAuthStatus({
      isAuthenticated: false,
      user: null
    });
    setTestResult(null);
  };

  return (
    <div style={{ 
      padding: '20px', 
      maxWidth: '800px', 
      margin: '0 auto',
      fontFamily: 'monospace',
      backgroundColor: '#f5f5f5',
      borderRadius: '8px'
    }}>
      <h2>🧪 FeedPage数据获取测试工具</h2>
      
      {/* 认证状态 */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '15px', 
        backgroundColor: authStatus.isAuthenticated ? '#d4edda' : '#f8d7da',
        border: `1px solid ${authStatus.isAuthenticated ? '#c3e6cb' : '#f5c6cb'}`,
        borderRadius: '4px'
      }}>
        <h3>🔐 认证状态</h3>
        <p><strong>状态:</strong> {authStatus.isAuthenticated ? '✅ 已认证' : '❌ 未认证'}</p>
        {authStatus.user && (
          <div>
            <p><strong>用户ID:</strong> {authStatus.user.id}</p>
            <p><strong>邮箱:</strong> {authStatus.user.email}</p>
            <p><strong>OpenID:</strong> {authStatus.user.openid}</p>
            <p><strong>昵称:</strong> {authStatus.user.nickName}</p>
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={handleTestAuth}
          disabled={isLoading}
          style={{
            marginRight: '10px',
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? '⏳ 处理中...' : '🔑 测试认证'}
        </button>

        <button
          onClick={handleTestDataFetch}
          disabled={isLoading || !authStatus.isAuthenticated}
          style={{
            marginRight: '10px',
            padding: '10px 20px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: (isLoading || !authStatus.isAuthenticated) ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? '⏳ 获取中...' : '📊 获取真实数据'}
        </button>

        <button
          onClick={handleClearResults}
          disabled={isLoading}
          style={{
            marginRight: '10px',
            padding: '10px 20px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          🗑️ 清除结果
        </button>

        <button
          onClick={handleLogout}
          disabled={isLoading}
          style={{
            padding: '10px 20px',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          🚪 登出
        </button>
      </div>

      {/* 测试结果 */}
      {testResult && (
        <div style={{ 
          marginTop: '20px',
          padding: '15px',
          backgroundColor: testResult.success ? '#d1ecf1' : '#f8d7da',
          border: `1px solid ${testResult.success ? '#bee5eb' : '#f5c6cb'}`,
          borderRadius: '4px'
        }}>
          <h3>📋 测试结果</h3>
          <p><strong>状态:</strong> {testResult.success ? '✅ 成功' : '❌ 失败'}</p>
          <p><strong>时间:</strong> {new Date(testResult.timestamp).toLocaleString()}</p>
          
          {testResult.error && (
            <div style={{ marginTop: '10px' }}>
              <strong>错误信息:</strong>
              <pre style={{ 
                backgroundColor: '#fff', 
                padding: '10px', 
                borderRadius: '4px',
                overflow: 'auto',
                maxHeight: '200px'
              }}>
                {testResult.error}
              </pre>
            </div>
          )}

          {testResult.analysis && (
            <div style={{ marginTop: '10px' }}>
              <strong>数据分析:</strong>
              <pre style={{ 
                backgroundColor: '#fff', 
                padding: '10px', 
                borderRadius: '4px',
                overflow: 'auto',
                maxHeight: '300px'
              }}>
                {JSON.stringify(testResult.analysis, null, 2)}
              </pre>
            </div>
          )}

          {testResult.data && (
            <div style={{ marginTop: '10px' }}>
              <strong>原始数据 (前100字符):</strong>
              <pre style={{ 
                backgroundColor: '#fff', 
                padding: '10px', 
                borderRadius: '4px',
                overflow: 'auto',
                maxHeight: '200px'
              }}>
                {JSON.stringify(testResult.data, null, 2).substring(0, 500)}...
              </pre>
            </div>
          )}
        </div>
      )}

      {/* 使用说明 */}
      <div style={{ 
        marginTop: '20px',
        padding: '15px',
        backgroundColor: '#fff3cd',
        border: '1px solid #ffeaa7',
        borderRadius: '4px'
      }}>
        <h3>📖 使用说明</h3>
        <ol>
          <li>首先点击"测试认证"按钮，使用测试用户登录</li>
          <li>认证成功后，点击"获取真实数据"按钮获取后端动态数据</li>
          <li>系统会自动分析数据结构并下载分析报告</li>
          <li>查看控制台日志获取详细的调试信息</li>
        </ol>
      </div>
    </div>
  );
};

export default DataFetchTester;
