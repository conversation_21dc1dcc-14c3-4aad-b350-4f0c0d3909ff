// Loading Spinner Styles
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;

  &.small {
    padding: 10px;
    
    .spinner {
      width: 24px;
      height: 24px;
    }
    
    .loading-text {
      font-size: 12px;
      margin-top: 8px;
    }
  }

  &.medium {
    padding: 20px;
    
    .spinner {
      width: 32px;
      height: 32px;
    }
    
    .loading-text {
      font-size: 14px;
      margin-top: 12px;
    }
  }

  &.large {
    padding: 40px;
    
    .spinner {
      width: 48px;
      height: 48px;
    }
    
    .loading-text {
      font-size: 16px;
      margin-top: 16px;
    }
  }
}

.spinner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner-circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #007AFF;
  margin: 0 2px;
  animation: spinner-bounce 1.4s ease-in-out infinite both;

  &:nth-child(1) {
    animation-delay: -0.32s;
  }

  &:nth-child(2) {
    animation-delay: -0.16s;
  }

  &:nth-child(3) {
    animation-delay: 0s;
  }
}

@keyframes spinner-bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.loading-text {
  color: #666;
  font-weight: 500;
  text-align: center;
  margin: 0;
}

// Load More Indicator Styles
.load-more-indicator {
  padding: 20px;
  text-align: center;
  border-top: 1px solid #f0f0f0;
  margin-top: 20px;

  &.loading {
    background-color: #fafafa;
  }

  &.error {
    background-color: #fff5f5;
    border-color: #fed7d7;
  }

  &.finished {
    background-color: #f0fff4;
    border-color: #c6f6d5;
  }
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.error-icon {
  width: 24px;
  height: 24px;
  color: #e53e3e;
}

.error-message {
  color: #e53e3e;
  font-weight: 500;
  margin: 0;
}

.retry-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #0056b3;
  }

  &:active {
    transform: translateY(1px);
  }
}

.finished-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.finished-icon {
  width: 20px;
  height: 20px;
  color: #38a169;
}

.finished-message {
  color: #38a169;
  font-weight: 500;
  margin: 0;
  font-size: 14px;
}

// Skeleton Card Styles
.skeleton-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

.skeleton-image {
  width: 100%;
  height: 120px;
  background-color: #e2e8f0;
  border-radius: 8px;
  margin-bottom: 12px;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-title {
  height: 20px;
  background-color: #e2e8f0;
  border-radius: 4px;
  width: 70%;
}

.skeleton-meta {
  display: flex;
  gap: 8px;
}

.skeleton-tag {
  height: 16px;
  background-color: #e2e8f0;
  border-radius: 4px;
  width: 60px;
}

@keyframes skeleton-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .loading-text {
    color: #a0a0a0;
  }

  .load-more-indicator {
    border-color: #333;
    
    &.loading {
      background-color: #1a1a1a;
    }

    &.error {
      background-color: #2d1b1b;
      border-color: #4a2c2c;
    }

    &.finished {
      background-color: #1b2d1b;
      border-color: #2c4a2c;
    }
  }

  .skeleton-card {
    background-color: #1a1a1a;
  }

  .skeleton-image,
  .skeleton-title,
  .skeleton-tag {
    background-color: #333;
  }
}