import React from 'react';
import './LoadingSpinner.scss';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  text?: string;
  className?: string;
}

/**
 * 加载指示器组件
 * 用于显示加载状态
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  text,
  className = ''
}) => {
  return (
    <div className={`loading-spinner ${size} ${className}`}>
      <div className="spinner">
        <div className="spinner-circle"></div>
        <div className="spinner-circle"></div>
        <div className="spinner-circle"></div>
      </div>
      {text && <p className="loading-text">{text}</p>}
    </div>
  );
};

interface LoadMoreIndicatorProps {
  loading: boolean;
  hasMore: boolean;
  error?: string | null;
  onRetry?: () => void;
}

/**
 * 加载更多指示器组件
 * 用于无限滚动的底部状态显示
 */
export const LoadMoreIndicator: React.FC<LoadMoreIndicatorProps> = ({
  loading,
  hasMore,
  error,
  onRetry
}) => {
  if (error) {
    return (
      <div className="load-more-indicator error">
        <div className="error-content">
          <svg className="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"/>
            <line x1="12" y1="8" x2="12" y2="12"/>
            <line x1="12" y1="16" x2="12.01" y2="16"/>
          </svg>
          <p className="error-message">加载失败</p>
          {onRetry && (
            <button className="retry-btn" onClick={onRetry}>
              重试
            </button>
          )}
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="load-more-indicator loading">
        <LoadingSpinner size="small" text="加载更多运动..." />
      </div>
    );
  }

  if (!hasMore) {
    return (
      <div className="load-more-indicator finished">
        <div className="finished-content">
          <svg className="finished-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <polyline points="20,6 9,17 4,12"/>
          </svg>
          <p className="finished-message">已加载全部运动</p>
        </div>
      </div>
    );
  }

  return null;
};

interface SkeletonCardProps {
  count?: number;
}

/**
 * 骨架屏组件
 * 用于加载时的占位效果
 */
export const SkeletonCard: React.FC<SkeletonCardProps> = ({ count = 1 }) => {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="skeleton-card">
          <div className="skeleton-image"></div>
          <div className="skeleton-content">
            <div className="skeleton-title"></div>
            <div className="skeleton-meta">
              <div className="skeleton-tag"></div>
              <div className="skeleton-tag"></div>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};