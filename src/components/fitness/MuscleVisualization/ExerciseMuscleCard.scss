// ExerciseMuscleCard 样式文件
@import '../../../styles/design-system.css';

.exercise-muscle-card {
  display: flex;
  background: var(--bg-primary);
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;

  // iOS优化
  @supports (-webkit-touch-callout: none) {
    -webkit-transform: translateZ(0);
    -webkit-backface-visibility: hidden;
  }

  // 主题样式
  &.light {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --text-primary: #1a1a1a;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  &.dark {
    --bg-primary: #1e293b;
    --bg-secondary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --border-color: #475569;
    --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  // 尺寸变体
  &.sm {
    min-height: 180px;
    .muscle-text-section {
      flex: 0 0 120px;
      padding: 12px;
    }
    .muscle-visual-section {
      .muscle-illustration-container {
        max-width: 140px;
      }
    }
    .muscle-category {
      font-size: 12px;
      margin-bottom: 6px;
    }
    .muscle-tag {
      font-size: 10px;
      padding: 2px 6px;
    }
  }

  &.md {
    min-height: 240px;
    .muscle-text-section {
      flex: 0 0 160px;
      padding: 16px;
    }
    .muscle-visual-section {
      .muscle-illustration-container {
        max-width: 200px;
      }
    }
  }

  &.lg {
    min-height: 300px;
    .muscle-text-section {
      flex: 0 0 200px;
      padding: 20px;
    }
    .muscle-visual-section {
      .muscle-illustration-container {
        max-width: 280px;
      }
    }
    .muscle-category {
      font-size: 16px;
      margin-bottom: 12px;
    }
    .muscle-tag {
      font-size: 14px;
      padding: 6px 12px;
    }
  }

  // 左侧文本区域
  .muscle-text-section {
    flex: 0 0 160px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 16px;

    .muscle-text-content {
      width: 100%;
    }

    .muscle-group {
      &:not(:last-child) {
        margin-bottom: 16px;
      }
    }

    .muscle-category {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-primary);
      margin: 0 0 8px 0;
      letter-spacing: 0.025em;
      position: relative;
      padding-bottom: 6px;
      display: inline-block; // 让元素宽度适应文本
      width: auto; // 确保宽度自适应
      
      // 使用background-image作为下划线，与文字重合
      background-image: linear-gradient(transparent, transparent);
      background-repeat: no-repeat;
      background-size: 100% 5px; // 现在100%指的是文本宽度
      background-position: 0 calc(100% - 2px); // 与文字底部重合
    }
    
    // 主要肌肉标题 - 蓝色下划线
    .muscle-group:has(.muscle-tag.primary) .muscle-category {
      background-image: linear-gradient(#3b82f6, #3b82f6);
    }
    
    // 次要肌肉标题 - 浅蓝色下划线  
    .muscle-group:has(.muscle-tag.secondary) .muscle-category {
      background-image: linear-gradient(#93c5fd, #93c5fd);
    }

    .muscle-list {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .muscle-tag {
      display: inline-block;
      font-size: 12px;
      font-weight: 500;
      padding: 6px 12px;
      border-radius: 20px; // 使用半径大的圆弧框
      line-height: 1.2;
      text-align: left;
      max-width: fit-content;
      white-space: nowrap;
      background: #f3f4f6; // 统一使用灰色背景
      color: #374151; // 统一使用黑色字体
      transition: all 0.2s ease;
      
      // 悬停效果
      &:hover {
        background: #e5e7eb;
        transform: translateY(-1px);
      }

      // 不再区分primary和secondary的背景色，统一使用灰色
      &.primary,
      &.secondary {
        background: #f3f4f6;
        color: #374151;
      }
    }

    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 20px;

      .empty-text {
        color: var(--text-secondary);
        font-size: 12px;
        text-align: center;
      }
    }
  }

  // 右侧肌肉视觉区域
  .muscle-visual-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: var(--bg-primary);

    .muscle-illustration-container {
      width: 100%;
      max-width: 200px;
      height: auto;
      
      // 通过CSS控制肌肉颜色 - 主要肌肉
      &[data-primary-muscles*="CHEST"] .muscle-illustration .muscle-selected[data-muscle*="chest"] path,
      &[data-primary-muscles*="BICEPS"] .muscle-illustration .muscle-selected[data-muscle*="biceps"] path,
      &[data-primary-muscles*="SHOULDERS"] .muscle-illustration .muscle-selected[data-muscle*="shoulder"] path,
      &[data-primary-muscles*="BACK"] .muscle-illustration .muscle-selected[data-muscle*="back"] path,
      &[data-primary-muscles*="TRICEPS"] .muscle-illustration .muscle-selected[data-muscle*="triceps"] path,
      &[data-primary-muscles*="QUADRICEPS"] .muscle-illustration .muscle-selected[data-muscle*="quadriceps"] path,
      &[data-primary-muscles*="HAMSTRINGS"] .muscle-illustration .muscle-selected[data-muscle*="hamstrings"] path,
      &[data-primary-muscles*="GLUTES"] .muscle-illustration .muscle-selected[data-muscle*="glutes"] path,
      &[data-primary-muscles*="ABDOMINALS"] .muscle-illustration .muscle-selected[data-muscle*="abs"] path,
      &[data-primary-muscles*="CALVES"] .muscle-illustration .muscle-selected[data-muscle*="calves"] path {
        fill: #3b82f6 !important;
      }

      // 通过CSS控制肌肉颜色 - 次要肌肉
      &[data-secondary-muscles*="CHEST"] .muscle-illustration .muscle-selected[data-muscle*="chest"] path,
      &[data-secondary-muscles*="BICEPS"] .muscle-illustration .muscle-selected[data-muscle*="biceps"] path,
      &[data-secondary-muscles*="SHOULDERS"] .muscle-illustration .muscle-selected[data-muscle*="shoulder"] path,
      &[data-secondary-muscles*="BACK"] .muscle-illustration .muscle-selected[data-muscle*="back"] path,
      &[data-secondary-muscles*="TRICEPS"] .muscle-illustration .muscle-selected[data-muscle*="triceps"] path,
      &[data-secondary-muscles*="QUADRICEPS"] .muscle-illustration .muscle-selected[data-muscle*="quadriceps"] path,
      &[data-secondary-muscles*="HAMSTRINGS"] .muscle-illustration .muscle-selected[data-muscle*="hamstrings"] path,
      &[data-secondary-muscles*="GLUTES"] .muscle-illustration .muscle-selected[data-muscle*="glutes"] path,
      &[data-secondary-muscles*="ABDOMINALS"] .muscle-illustration .muscle-selected[data-muscle*="abs"] path,
      &[data-secondary-muscles*="CALVES"] .muscle-illustration .muscle-selected[data-muscle*="calves"] path {
        fill: #93c5fd !important;
      }

      // 更通用的肌肉颜色覆盖
      .muscle-illustration {
        .muscle-selected {
          path {
            // 默认主要肌肉颜色
            fill: #3b82f6;
          }
        }
      }

      // iOS硬件加速
      .muscle-illustration {
        will-change: transform;
        transform: translateZ(0);
        -webkit-backface-visibility: hidden;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    flex-direction: column;
    min-height: auto;

    .muscle-text-section {
      flex: none;
      border-right: none;
      border-bottom: 1px solid var(--border-color);
      padding: 12px;

      .muscle-list {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 6px;
      }
    }

    .muscle-visual-section {
      padding: 16px;

      .muscle-illustration-container {
        max-width: 160px;
      }
    }

    &.sm .muscle-text-section {
      padding: 8px;
    }

    &.lg .muscle-text-section {
      padding: 16px;
    }
  }

  // 暗色主题下的边框和阴影调整
  &.dark {
    box-shadow: var(--card-shadow);
    
    .muscle-text-section {
      .muscle-tag {
        &.primary {
          background: #2563eb;
          border-color: #2563eb;
        }

        &.secondary {
          background: #7c3aed;
          color: #c4b5fd;
          border-color: #7c3aed;
        }
      }
    }
  }

  // Hover效果（桌面端）
  @media (hover: hover) {
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      &.dark {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
    }
  }

  // 无障碍支持
  &:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
} 