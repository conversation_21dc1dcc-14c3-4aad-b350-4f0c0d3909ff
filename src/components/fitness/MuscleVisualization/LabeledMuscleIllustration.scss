// 带标签的肌肉可视化组件样式 - 基于参考图像优化
.labeled-muscle-illustration {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  background: var(--bg-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  
  // iOS优化
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  
  // 图例样式
  .muscle-legend {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    margin-bottom: var(--space-4);
    
    h3 {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin: 0;
    }
    
    .legend-items {
      display: flex;
      gap: var(--space-6);
      
      .legend-item {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        
        .legend-color {
          width: 20px;
          height: 20px;
          border-radius: var(--radius-sm);
          border: 1px solid var(--border-color);
        }
        
        span {
          font-size: var(--text-base);
          color: var(--text-primary);
          font-weight: var(--font-medium);
        }
      }
    }
  }
  
  // 人体图容器
  .muscle-body-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    width: 100%;
    max-width: 800px;
    
    // SVG包装器
    .muscle-svg-wrapper {
      position: relative;
      width: 100%;
      max-width: 600px;
      
      // 主要SVG样式
      .muscle-illustration-labeled {
        width: 100%;
        height: auto;
        display: block;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        
        // iOS硬件加速
        will-change: transform;
        transform: translateZ(0);
        backfaceVisibility: hidden;
        
        // 优化SVG渲染
        shape-rendering: optimizeSpeed;
        image-rendering: optimizeSpeed;
      }
      
      // 肌肉标签覆盖层
      .muscle-labels-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 10;
        
        .muscle-label {
          position: absolute;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          white-space: nowrap;
          transition: all 0.2s ease;
          
          // 默认样式（未选中）
          background: rgba(255, 255, 255, 0.9);
          color: #374151;
          border: 1px solid #d1d5db;
          backdrop-filter: blur(4px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          
          // 选中状态样式会通过内联样式动态应用
          
          // 响应式字体大小
          @media (max-width: 768px) {
            font-size: 11px;
            padding: 3px 6px;
          }
          
          @media (max-width: 480px) {
            font-size: 10px;
            padding: 2px 5px;
          }
          
          // iOS触摸优化
          @supports (backdrop-filter: blur(4px)) {
            backdrop-filter: blur(4px);
          }
          
          // 标签连接线（可选，与参考图像保持一致）
          &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -6px;
            width: 6px;
            height: 1px;
            background: currentColor;
            opacity: 0.4;
            transform: translateY(-50%);
          }
          
          // 右侧标签（后视图）的连接线
          &[data-view="back"]::before {
            left: auto;
            right: -6px;
          }
        }
      }
    }
  }
}

// Loading状态
.labeled-muscle-illustration-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  
  .animate-spin {
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .labeled-muscle-illustration {
    padding: var(--space-3);
    gap: var(--space-3);
    
    .muscle-body-container {
      .muscle-svg-wrapper {
        max-width: 500px;
        
        .muscle-labels-overlay {
          .muscle-label {
            font-size: 11px;
            padding: 3px 6px;
            border-radius: 3px;
          }
        }
      }
    }
    
    .muscle-legend {
      margin-bottom: var(--space-3);
      
      .legend-items {
        gap: var(--space-4);
        
        .legend-item {
          .legend-color {
            width: 16px;
            height: 16px;
          }
          
          span {
            font-size: var(--text-sm);
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .labeled-muscle-illustration {
    padding: var(--space-2);
    gap: var(--space-2);
    
    .muscle-body-container {
      .muscle-svg-wrapper {
        max-width: 400px;
        
        .muscle-labels-overlay {
          .muscle-label {
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 3px;
            
            &::before {
              width: 4px;
              left: -4px;
            }
            
            &[data-view="back"]::before {
              right: -4px;
            }
          }
        }
      }
    }
    
    .muscle-legend {
      h3 {
        font-size: var(--text-base);
      }
      
      .legend-items {
        gap: var(--space-3);
        
        .legend-item {
          .legend-color {
            width: 14px;
            height: 14px;
          }
          
          span {
            font-size: var(--text-xs);
          }
        }
      }
    }
  }
}

// 暗色主题支持
.theme-dark {
  .labeled-muscle-illustration {
    .muscle-legend {
      h3 {
        color: var(--text-primary);
      }
      
      .legend-items {
        .legend-item {
          span {
            color: var(--text-primary);
          }
        }
      }
    }
    
    .muscle-labels-overlay {
      .muscle-label {
        background: rgba(0, 0, 0, 0.8);
        color: #f9fafb;
        border-color: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(8px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      }
    }
  }
}

// iOS特定优化
@supports (-webkit-touch-callout: none) {
  .labeled-muscle-illustration {
    .muscle-body-container {
      .muscle-svg-wrapper {
        // iOS Safari优化
        -webkit-overflow-scrolling: touch;
        
        .muscle-illustration-labeled {
          // iOS硬件加速
          -webkit-transform: translateZ(0);
          -webkit-backface-visibility: hidden;
          -webkit-perspective: 1000;
        }
        
        .muscle-labels-overlay {
          .muscle-label {
            // iOS文字渲染优化
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }
        }
      }
    }
  }
} 