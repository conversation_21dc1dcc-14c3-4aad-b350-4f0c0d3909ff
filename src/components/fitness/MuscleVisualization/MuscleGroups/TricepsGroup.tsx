import { MuscleGroupEnum } from "../../../../types/muscle.types";
import { MuscleGroupComponentProps } from "./types";

export function TricepsGroup({ onToggleMuscle, getMuscleClasses }: MuscleGroupComponentProps) {
  return (
    <g className="group cursor-pointer" onClick={() => onToggleMuscle(MuscleGroupEnum.TRICEPS)}>
      <path
        className={getMuscleClasses(MuscleGroupEnum.TRICEPS)}
        d="M 478.44,132.70
           C 477.17,132.60 475.86,132.25 474.77,131.75
             472.83,130.86 471.05,129.76 468.96,128.62
             468.99,129.09 469.03,129.35 469.03,129.62
             469.02,131.66 469.21,133.71 468.95,135.73
             468.64,138.15 467.53,140.35 464.61,141.69
             464.57,141.72 464.53,141.77 464.49,141.82
             464.31,141.83 463.98,141.83 463.36,141.77
             462.16,141.67 460.56,140.30 459.67,139.45
             459.46,139.24 459.25,139.02 459.04,138.81
             459.02,138.79 459.01,138.78 459.01,138.78
             459.01,138.78 459.02,138.79 459.02,138.79
             458.42,138.20 457.82,137.61 457.24,137.01
             457.12,137.06 457.00,137.12 456.88,137.18
             456.94,137.39 456.98,137.61 457.09,137.81
             459.43,142.05 461.66,146.33 464.22,150.51
             465.11,151.97 466.74,153.23 468.22,154.47
             468.66,154.84 469.71,154.83 470.47,155.00
             470.47,155.00 474.13,155.27 474.13,155.27
             474.54,155.34 475.01,155.25 475.55,155.06
             477.12,154.50 478.73,153.95 480.39,153.63
             481.50,153.43 482.01,153.04 482.10,152.23
             482.37,149.83 482.84,147.43 482.80,145.04
             482.73,141.31 482.10,137.61 480.56,134.07
             480.20,133.25 479.68,132.80 478.44,132.70"
        data-elem={MuscleGroupEnum.TRICEPS}
        fill="#757575"
        id="path78"
        stroke="black"
        strokeWidth="0"
      />

      {/* Path 82 */}
      <path
        className={getMuscleClasses(MuscleGroupEnum.TRICEPS)}
        d="M 466.43,137.75
           C 466.50,137.55 466.56,137.34 466.58,137.14
             466.79,135.14 466.99,133.14 467.13,131.75
             466.82,129.82 466.57,128.54 466.41,127.25
             465.53,120.08 461.16,114.46 456.77,108.87
             456.41,108.40 455.41,108.13 454.74,108.19
             454.37,108.23 454.05,109.10 453.78,109.64
             453.64,109.93 453.63,110.30 453.63,110.63
             453.65,115.00 453.56,119.37 453.74,123.73
             454.00,129.73 456.89,134.72 461.26,139.01
             463.57,141.28 465.45,140.75 466.43,137.75"
        data-elem={MuscleGroupEnum.TRICEPS}
        fill="#757575"
        id="path82"
        stroke="black"
        strokeWidth="0"
      />

      {/* Path 84 */}
      <path
        className={getMuscleClasses(MuscleGroupEnum.TRICEPS)}
        d="M 465.31,111.50
           C 464.02,111.23 462.74,110.93 461.05,110.56
             461.36,111.45 461.48,112.00 461.74,112.50
             463.17,115.34 464.82,118.12 466.05,121.01
             467.83,125.22 471.46,128.13 475.39,130.86
             477.62,132.42 479.64,131.80 480.31,129.45
             480.47,128.91 480.61,128.33 480.52,127.79
             480.15,125.50 479.92,123.17 479.22,120.94
             478.34,118.21 477.07,115.56 475.92,112.89
             475.60,112.16 475.11,111.53 473.87,111.81
             470.99,112.47 468.13,112.09 465.31,111.50"
        data-elem={MuscleGroupEnum.TRICEPS}
        fill="#757575"
        id="path84"
        stroke="black"
        strokeWidth="0"
      />

      {/* Path 98 */}
      <path
        className={getMuscleClasses(MuscleGroupEnum.TRICEPS)}
        d="M 368.73,110.63
           C 368.73,110.30 368.72,109.93 368.58,109.64
             368.32,109.10 367.99,108.23 367.62,108.19
             366.96,108.13 365.95,108.40 365.59,108.87
             361.20,114.46 356.83,120.08 355.95,127.25
             355.79,128.54 355.54,129.82 355.23,131.75
             355.37,133.14 355.57,135.14 355.78,137.14
             355.80,137.34 355.86,137.55 355.93,137.75
             356.91,140.75 358.79,141.28 361.10,139.01
             365.47,134.72 368.36,129.73 368.62,123.73
             368.80,119.37 368.71,115.00 368.73,110.63"
        data-elem={MuscleGroupEnum.TRICEPS}
        fill="#757575"
        id="path98"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.TRICEPS)}
        d="M 341.97,153.63
           C 343.63,153.95 345.24,154.50 346.81,155.06
             347.35,155.25 347.82,155.34 348.23,155.27
             348.23,155.27 351.89,155.00 351.89,155.00
             352.66,154.83 353.70,154.84 354.14,154.47
             355.62,153.23 357.25,151.97 358.14,150.51
             360.70,146.33 362.93,142.05 365.27,137.81
             365.38,137.61 365.42,137.39 365.49,137.18
             365.36,137.12 365.24,137.06 365.12,137.01
             364.54,137.61 363.94,138.20 363.34,138.79
             363.34,138.79 363.35,138.78 363.35,138.78
             363.35,138.78 363.34,138.79 363.33,138.81
             363.11,139.02 362.90,139.24 362.69,139.45
             361.80,140.30 360.20,141.67 359.00,141.77
             358.38,141.83 358.05,141.83 357.88,141.82
             357.84,141.77 357.79,141.72 357.75,141.69
             354.83,140.35 353.72,138.15 353.41,135.73
             353.15,133.71 353.34,131.66 353.33,129.62
             353.33,129.35 353.37,129.09 353.40,128.62
             351.31,129.76 349.53,130.86 347.60,131.75
             346.50,132.25 345.19,132.60 343.92,132.70
             342.68,132.80 342.16,133.25 341.80,134.07
             340.26,137.61 339.63,141.31 339.56,145.04
             339.52,147.43 340.00,149.83 340.26,152.23
             340.35,153.04 340.86,153.43 341.97,153.63"
        data-elem={MuscleGroupEnum.TRICEPS}
        fill="#757575"
        id="path100"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.TRICEPS)}
        d="M 346.98,130.86
           C 350.90,128.13 354.53,125.22 356.32,121.01
             357.54,118.12 359.19,115.34 360.62,112.50
             360.88,112.00 361.00,111.45 361.31,110.56
             359.62,110.93 358.34,111.23 357.05,111.50
             354.23,112.09 351.37,112.47 348.49,111.81
             347.26,111.53 346.76,112.16 346.44,112.89
             345.29,115.56 344.01,118.21 343.15,120.94
             342.44,123.17 342.21,125.50 341.84,127.79
             341.75,128.33 341.89,128.91 342.05,129.45
             342.72,131.80 344.74,132.42 346.98,130.86"
        data-elem={MuscleGroupEnum.TRICEPS}
        fill="#757575"
        id="path102"
        stroke="black"
        strokeWidth="0"
      />
    </g>
  );
}