import { MuscleGroupEnum } from "../../../../types/muscle.types";
import { MuscleGroupComponentProps } from "./types";

export function HamstringsGroup({ onToggleMuscle, getMuscleClasses }: MuscleGroupComponentProps) {
  return (
    <g className="group cursor-pointer" onClick={() => onToggleMuscle(MuscleGroupEnum.HAMSTRINGS)}>
      <path
        className="fill-transparent"
        d="M 365.50,209.50
           C 365.50,209.50 370.00,210.00 370.00,210.00
             370.00,210.00 375.00,213.50 375.00,213.50
             375.00,213.50 377.50,219.25 377.50,219.25
             377.50,219.25 377.50,229.25 377.50,229.25
             377.50,229.25 377.00,237.25 377.00,237.25
             377.00,237.25 377.00,239.75 377.00,239.75
             377.00,239.75 382.00,239.75 382.00,239.75
             382.00,239.75 389.25,238.75 389.25,238.75
             389.25,238.75 400.50,233.25 400.50,233.25
             400.50,233.25 406.50,230.25 406.50,230.25
             406.50,230.25 408.50,229.25 408.50,229.25
             408.50,229.25 408.75,243.50 408.75,243.50
             408.75,243.50 410.00,258.00 410.00,258.00
             410.00,258.00 409.75,272.25 409.75,272.25
             409.75,272.25 409.75,286.50 409.75,286.50
             409.75,286.50 408.00,300.50 408.00,300.50
             408.00,300.50 403.50,318.50 403.50,318.75
             403.50,319.00 399.00,334.75 399.00,334.75
             399.00,334.75 393.25,341.25 393.25,341.25
             393.25,341.25 392.25,331.25 392.25,331.25
             392.25,331.25 392.25,326.00 392.25,326.00
             392.25,326.00 391.00,322.25 391.00,322.25
             391.00,322.25 384.00,327.25 384.00,327.25
             384.00,327.25 382.00,324.50 382.00,324.50
             382.00,324.50 379.25,318.75 379.25,318.75
             379.25,318.75 376.50,321.25 376.50,321.25
             376.50,321.25 371.00,329.50 371.00,329.50
             371.00,329.50 369.25,324.00 369.25,324.00
             369.25,324.00 368.00,306.25 368.00,306.25
             368.00,306.25 366.75,295.00 366.75,295.00
             366.75,295.00 363.00,268.50 363.00,268.50
             363.00,268.50 362.00,246.25 362.00,246.25
             362.00,246.25 362.25,216.50 362.25,216.50
             362.25,216.50 364.25,211.75 365.00,209.25"
        data-elem={MuscleGroupEnum.HAMSTRINGS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.HAMSTRINGS)}
        d="M 441.45,241.64
           C 440.46,241.28 439.45,240.94 438.44,240.64
             436.85,240.18 436.23,240.55 436.10,242.07
             435.85,244.93 435.62,247.81 435.72,250.66
             435.90,255.66 436.30,260.65 436.66,265.64
             436.81,267.61 437.17,269.56 437.26,271.53
             437.59,278.00 437.84,284.47 438.11,290.93
             438.45,299.17 440.40,307.00 444.28,314.45
             446.28,318.27 447.81,322.30 449.56,326.23
             449.65,326.44 449.77,326.63 449.88,326.83
             450.01,326.85 450.14,326.86 450.28,326.88
             450.60,325.93 451.09,325.00 451.21,324.03
             451.51,321.76 451.84,319.46 451.78,317.19
             451.66,312.62 451.12,308.06 451.05,303.49
             451.05,303.27 451.05,303.05 451.05,302.83
             451.05,302.83 453.07,291.65 453.07,291.65
             453.26,290.74 453.49,289.83 453.62,288.91
             454.93,279.90 456.51,270.91 457.44,261.87
             458.22,254.46 458.36,247.00 458.53,239.56
             458.64,234.72 458.43,229.86 458.12,225.03
             457.86,221.14 457.32,217.25 456.72,213.38
             456.38,211.20 454.98,210.62 452.62,211.34
             450.64,211.95 449.28,213.23 448.21,214.79
             445.94,218.14 445.41,221.86 445.29,225.66
             445.12,230.79 446.16,235.77 447.42,240.71
             447.37,243.31 443.19,242.21 441.45,241.64"
        data-elem={MuscleGroupEnum.HAMSTRINGS}
        id="path74"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.HAMSTRINGS)}
        d="M 429.00,338.21
           C 429.23,338.17 429.46,338.13 429.69,338.09
             429.69,338.09 429.79,327.66 429.79,327.66
             429.83,326.68 429.94,325.69 429.92,324.71
             429.88,323.32 430.47,322.23 431.49,321.13
             434.73,317.60 437.07,313.64 437.94,309.12
             438.19,307.88 438.03,306.68 437.66,305.53
             437.66,305.53 437.66,305.53 437.66,305.53
             437.66,305.53 435.81,287.16 435.81,287.16
             435.81,287.16 435.81,287.13 435.81,287.13
             435.82,286.71 435.84,286.29 435.84,285.87
             435.89,281.30 435.47,276.73 435.16,272.17
             434.79,266.95 434.36,261.73 433.85,256.52
             433.43,252.12 433.01,247.71 432.31,243.35
             431.70,239.54 429.74,236.47 425.50,234.84
             422.94,233.86 420.36,232.82 418.43,230.88
             417.71,230.16 416.55,230.62 416.44,231.60
             416.43,231.77 416.43,231.95 416.45,232.13
             416.45,232.13 416.46,235.53 416.46,235.53
             416.43,236.96 415.11,254.53 415.20,255.95
             415.78,266.09 415.88,279.95 416.44,290.10
             416.76,295.71 418.39,304.42 420.34,311.87
             422.59,320.53 423.36,325.91 427.66,335.88
             428.14,336.65 428.55,337.43 429.00,338.21"
        data-elem={MuscleGroupEnum.HAMSTRINGS}
        id="path70"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.HAMSTRINGS)}
        d="M 434.82,322.36
           C 435.93,323.13 436.89,324.09 437.93,324.95
             438.28,325.24 438.65,325.50 439.19,325.91
             439.48,324.90 439.65,324.07 439.97,323.29
             440.65,321.65 441.34,319.99 442.17,318.41
             442.59,317.62 442.83,316.93 442.40,316.12
             441.33,314.11 440.26,312.11 439.06,309.86
             438.81,310.34 438.69,310.50 438.65,310.67
             437.79,314.07 436.20,317.16 434.26,320.11
             433.54,321.21 433.74,321.62 434.82,322.36"
        data-elem={MuscleGroupEnum.HAMSTRINGS}
        id="path58"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.HAMSTRINGS)}
        d="M 406.39,232.13
           C 406.41,231.95 406.41,231.77 406.40,231.60
             406.29,230.62 405.13,230.16 404.41,230.88
             402.47,232.82 399.90,233.86 397.34,234.84
             393.10,236.47 391.14,239.54 390.53,243.35
             389.83,247.71 389.41,252.12 388.99,256.52
             388.48,261.73 388.05,266.95 387.68,272.17
             387.37,276.73 386.95,281.30 387.00,285.87
             387.00,286.29 387.02,286.71 387.03,287.13
             387.03,287.13 387.03,287.16 387.03,287.16
             387.03,287.16 385.18,305.53 385.18,305.53
             385.18,305.53 385.18,305.53 385.18,305.53
             384.81,306.68 384.65,307.88 384.89,309.12
             385.77,313.64 388.11,317.60 391.35,321.13
             392.37,322.23 392.96,323.32 392.92,324.71
             392.90,325.69 393.01,326.68 393.05,327.66
             393.05,327.66 393.15,338.09 393.15,338.09
             393.38,338.13 393.61,338.17 393.84,338.21
             394.29,337.43 394.70,336.65 395.18,335.88
             399.48,325.91 400.25,320.53 402.50,311.87
             404.45,304.42 406.08,295.71 406.40,290.10
             406.96,279.95 407.06,266.09 407.64,255.95
             407.73,254.53 406.41,236.96 406.38,235.53
             406.38,235.53 406.39,232.13 406.39,232.13"
        data-elem={MuscleGroupEnum.HAMSTRINGS}
        id="path48"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.HAMSTRINGS)}
        d="M 383.65,325.91
           C 384.19,325.50 384.56,325.24 384.91,324.95
             385.95,324.09 386.91,323.13 388.02,322.36
             389.10,321.62 389.30,321.21 388.58,320.11
             386.64,317.16 385.05,314.07 384.19,310.67
             384.15,310.50 384.03,310.34 383.78,309.86
             382.58,312.11 381.51,314.11 380.44,316.12
             380.01,316.93 380.25,317.62 380.67,318.41
             381.50,319.99 382.19,321.65 382.87,323.29
             383.19,324.07 383.36,324.90 383.65,325.91"
        data-elem={MuscleGroupEnum.HAMSTRINGS}
        id="path40"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.HAMSTRINGS)}
        d="M 377.55,225.66
           C 377.43,221.86 376.90,218.14 374.63,214.79
             373.56,213.23 372.20,211.95 370.22,211.34
             367.86,210.62 366.46,211.20 366.12,213.38
             365.52,217.25 364.98,221.14 364.72,225.03
             364.41,229.86 364.20,234.72 364.31,239.56
             364.48,247.00 364.62,254.46 365.39,261.87
             366.33,270.91 367.91,279.90 369.22,288.91
             369.35,289.83 369.58,290.74 369.77,291.65
             369.77,291.65 371.79,302.83 371.79,302.83
             371.79,303.05 371.79,303.27 371.79,303.49
             371.72,308.06 371.18,312.62 371.06,317.19
             371.00,319.46 371.33,321.76 371.63,324.03
             371.75,325.00 372.24,325.93 372.56,326.88
             372.70,326.86 372.83,326.85 372.96,326.83
             373.07,326.63 373.19,326.44 373.28,326.23
             375.03,322.30 376.56,318.27 378.55,314.45
             382.44,307.00 384.39,299.17 384.73,290.93
             385.00,284.47 385.25,278.00 385.58,271.53
             385.67,269.56 386.03,267.61 386.18,265.64
             386.53,260.65 386.94,255.66 387.12,250.66
             387.22,247.81 386.99,244.93 386.74,242.07
             386.61,240.55 385.99,240.18 384.40,240.64
             383.39,240.94 382.38,241.28 381.39,241.64
             379.65,242.21 375.47,243.31 375.42,240.71
             376.68,235.77 377.72,230.79 377.55,225.66"
        data-elem={MuscleGroupEnum.HAMSTRINGS}
        id="path38"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className="fill-transparent"
        d="M 414.25,228.25
           C 414.25,228.25 421.50,232.00 421.50,232.00
             421.50,232.00 437.75,239.75 437.75,239.75
             437.75,239.75 445.50,240.50 445.50,240.50
             445.50,240.50 445.00,227.00 445.00,227.00
             445.00,227.00 446.00,217.00 446.00,217.00
             446.00,217.00 450.25,210.75 450.25,210.75
             450.25,210.75 455.25,210.50 455.25,210.50
             455.25,210.50 457.25,208.75 457.25,208.75
             457.25,208.75 459.75,211.50 459.75,211.50
             459.75,211.50 461.00,228.00 461.00,228.00
             461.00,228.00 461.00,243.75 461.00,243.75
             461.00,243.75 460.50,261.25 460.50,261.25
             460.50,261.25 456.75,282.75 456.75,282.75
             456.75,282.75 453.75,302.50 453.75,302.50
             453.75,302.50 453.75,321.50 453.75,321.50
             453.75,321.50 452.00,329.25 452.00,329.25
             452.00,329.25 447.00,322.25 447.00,322.25
             447.00,322.25 444.50,318.00 444.50,318.00
             444.50,318.00 441.75,322.00 441.75,322.00
             441.75,322.00 439.75,327.75 439.75,327.75
             439.75,327.75 433.00,322.00 433.00,322.00
             433.00,322.00 431.50,323.25 431.50,323.25
             431.50,323.25 430.25,340.00 430.25,340.00
             430.25,340.00 425.75,338.00 425.75,338.00
             425.75,338.00 421.25,326.00 421.25,326.00
             421.25,326.00 418.75,315.00 418.50,315.00
             418.25,315.00 414.50,295.25 414.50,295.25
             414.50,295.25 414.25,274.50 414.25,274.50
             414.25,274.50 414.00,251.50 414.00,251.50
             414.00,251.50 414.25,237.25 414.25,237.25
             414.25,237.25 414.75,232.00 414.75,229.25"
        data-elem={MuscleGroupEnum.HAMSTRINGS}
        stroke="black"
        strokeWidth="0"
      />
    </g>
  );
}