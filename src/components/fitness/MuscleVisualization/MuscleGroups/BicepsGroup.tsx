import { MuscleGroupEnum } from "../../../../types/muscle.types";
import { MuscleGroupComponentProps } from "./types";

export function BicepsGroup({ onToggleMuscle, getMuscleClasses }: MuscleGroupComponentProps) {
  return (
    <g className="muscle-group" onClick={() => onToggleMuscle(MuscleGroupEnum.BICEPS)}>
      <path
        className="fill-transparent"
        d="M 49.25,117.25
           C 49.25,117.25 45.50,120.75 45.50,120.75
             45.50,120.75 42.00,129.00 42.00,129.00
             42.00,129.00 41.25,137.75 41.25,137.75
             41.25,137.75 42.00,145.25 42.00,145.25
             42.00,145.25 46.50,149.50 46.50,149.50
             46.50,149.50 48.25,153.25 48.25,153.25
             48.25,153.25 49.25,166.50 49.25,166.50
             49.25,166.50 60.25,153.00 60.25,153.00
             60.25,153.00 61.25,149.50 61.25,149.50
             61.25,149.50 63.75,151.50 63.75,151.50
             63.75,151.50 68.00,148.75 68.00,148.75
             68.00,148.75 73.00,140.50 73.00,140.50
             73.00,140.50 74.25,129.50 74.25,129.50
             74.25,129.50 74.50,120.25 74.50,120.25
             74.50,120.25 74.50,116.50 74.50,116.50
             74.50,116.50 73.00,113.00 73.00,113.00
             73.00,113.00 71.25,111.50 71.25,111.50
             71.25,111.50 68.00,110.50 68.00,110.50
             68.00,110.50 57.75,114.25 57.75,114.25
             57.75,114.25 49.50,117.50 49.50,117.50"
        data-elem={MuscleGroupEnum.BICEPS}
        stroke="black"
        strokeWidth="0"
      />
      <path
        className={getMuscleClasses(MuscleGroupEnum.BICEPS)}
        d="M 166.82,151.36
           C 166.99,151.37 166.72,150.23 166.86,150.14
             166.46,148.57 166.10,146.98 165.67,145.42
             164.18,140.02 162.71,134.62 161.15,129.24
             160.79,128.01 158.77,117.31 158.25,114.97
             158.05,114.59 157.47,118.80 157.14,121.79
             157.09,122.29 157.16,124.59 157.08,125.04
             156.95,125.74 157.27,130.26 157.40,130.96
             158.57,137.01 159.49,139.00 162.34,144.99
             162.91,146.19 164.64,148.86 165.33,149.99
             165.62,150.47 166.25,151.31 166.82,151.36"
        data-elem={MuscleGroupEnum.BICEPS}
        id="path158"
        stroke="black"
        strokeWidth="0"
      />
      <path
        className={getMuscleClasses(MuscleGroupEnum.BICEPS)}
        d="M 160.10,114.96
           C 160.64,121.59 161.87,128.11 163.78,134.48
             164.74,137.69 165.80,140.87 166.86,144.05
             168.73,149.65 171.29,154.88 174.82,159.66
             176.46,161.88 177.68,164.42 179.09,166.81
             179.16,166.93 179.26,167.02 179.52,167.32
             179.64,166.25 179.80,165.40 179.82,164.55
             179.94,159.48 179.56,151.75 182.68,147.85
             184.94,145.02 185.76,140.37 185.60,137.71
             185.30,132.58 184.87,130.15 182.68,125.43
             177.61,114.52 170.89,114.39 163.98,111.93
             161.46,111.02 159.88,112.29 160.10,114.96"
        data-elem={MuscleGroupEnum.BICEPS}
        id="path150"
        stroke="black"
        strokeWidth="0"
      />
      <path
        className={getMuscleClasses(MuscleGroupEnum.BICEPS)}
        d="M 62.71,151.36
           C 63.28,151.31 63.91,150.47 64.20,149.99
             64.90,148.86 66.62,146.19 67.19,144.99
             70.04,139.00 70.97,137.01 72.13,130.96
             72.27,130.26 72.59,125.74 72.46,125.04
             72.37,124.59 72.45,122.29 72.39,121.79
             72.07,118.80 71.48,114.59 71.29,114.97
             70.77,117.31 68.74,128.01 68.38,129.24
             66.83,134.62 65.35,140.02 63.86,145.42
             63.43,146.98 63.07,148.57 62.68,150.14
             62.81,150.23 62.54,151.37 62.71,151.36"
        data-elem={MuscleGroupEnum.BICEPS}
        id="path148"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.BICEPS)}
        d="M 49.72,164.55
           C 49.74,165.40 49.90,166.25 50.02,167.32
             50.27,167.02 50.37,166.93 50.44,166.81
             51.85,164.42 53.07,161.88 54.71,159.66
             58.24,154.88 60.81,149.65 62.67,144.05
             63.73,140.87 64.79,137.69 65.76,134.48
             67.67,128.11 68.90,121.59 69.44,114.96
             69.66,112.29 68.07,111.02 65.55,111.93
             58.64,114.39 51.92,114.52 46.86,125.43
             44.67,130.15 44.23,132.58 43.93,137.71
             43.78,140.37 44.59,145.02 46.86,147.85
             49.98,151.75 49.59,159.48 49.72,164.55"
        data-elem={MuscleGroupEnum.BICEPS}
        id="path140"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className="fill-transparent"
        d="M 155.50,113.75
           C 155.50,113.75 155.25,124.75 155.25,124.75
             155.25,124.75 156.00,137.75 156.00,137.75
             156.00,137.75 159.50,143.75 159.50,143.75
             159.50,143.75 163.25,151.00 163.25,151.00
             163.25,151.00 168.75,152.50 168.75,152.50
             168.75,152.50 174.50,160.25 174.50,160.25
             174.50,160.25 180.25,168.00 180.25,168.00
             180.25,168.00 181.00,154.50 181.00,154.50
             181.00,154.50 183.50,148.25 183.50,148.25
             183.50,148.25 187.25,144.75 187.25,144.75
             187.25,144.75 188.75,136.25 188.75,136.25
             188.75,136.25 188.50,129.00 188.50,129.00
             188.50,129.00 184.50,121.75 184.50,121.75
             184.50,121.75 181.00,118.25 181.00,118.25
             181.00,118.25 174.50,115.00 174.50,115.00
             174.50,115.00 165.50,111.75 165.50,111.75
             165.50,111.75 158.25,111.00 158.25,111.00
             158.25,111.00 155.25,113.50 155.25,113.50"
        data-elem={MuscleGroupEnum.BICEPS}
        stroke="black"
        strokeWidth="0"
      />
    </g>
  );
}