import { MuscleGroupEnum } from "../../../../types/muscle.types";
import { MuscleGroupComponentProps } from "./types";

export function TrapsGroup({ onToggleMuscle, getMuscleClasses }: MuscleGroupComponentProps) {
  return (
    <g className="group cursor-pointer" onClick={() => onToggleMuscle(MuscleGroupEnum.TRAPS)}>
      <path
        className="fill-transparent"
        d="M 85.67,85.05
           C 85.67,85.05 87.62,84.15 87.62,84.15
             87.62,84.15 89.87,83.25 89.72,83.25
             89.57,83.25 92.27,83.10 92.27,83.10
             92.27,83.10 94.82,83.55 94.82,83.55
             94.82,83.55 97.52,84.60 97.52,84.60
             97.52,84.60 100.82,85.35 100.82,85.35
             100.82,85.35 107.72,87.75 107.72,87.75
             107.72,87.75 108.62,87.15 108.62,87.15
             108.62,87.15 110.72,86.40 110.72,86.40
             110.72,86.40 118.67,86.40 118.67,86.40
             118.67,86.40 121.07,87.90 121.07,87.90
             121.07,87.90 124.37,87.15 124.37,87.15
             124.37,87.15 127.97,85.50 127.97,85.50
             127.97,85.50 132.77,84.15 132.77,84.15
             132.77,84.15 136.38,83.10 136.38,83.10
             136.38,83.10 139.08,82.95 139.08,82.95
             139.08,82.95 141.18,83.55 141.18,83.55
             141.18,83.55 144.48,85.65 144.48,85.65
             144.48,85.65 145.38,83.85 145.38,83.85
             145.38,83.85 148.08,81.45 148.08,81.45
             148.08,81.45 152.73,78.90 152.73,78.90
             152.73,78.90 131.57,69.15 131.57,69.15
             131.57,69.15 128.87,73.80 128.87,73.80
             128.87,73.80 124.37,79.50 124.37,79.50
             124.37,79.50 119.27,85.20 119.27,85.20
             119.27,85.20 115.22,86.10 115.22,86.10
             115.22,86.10 111.62,85.20 111.62,85.20
             111.62,85.20 109.22,83.40 109.22,83.40
             109.22,83.40 106.37,81.15 106.37,81.15
             106.37,81.15 102.32,76.20 102.32,76.20
             102.32,76.20 99.17,71.70 99.17,71.70
             99.17,71.70 96.32,69.60 96.32,69.60
             96.32,69.60 76.21,78.45 76.21,78.45
             76.21,78.45 77.86,79.95 77.86,79.95
             77.86,79.95 80.42,81.30 80.42,81.30
             80.42,81.30 82.52,82.65 82.52,82.65
             82.52,82.65 84.77,85.20 84.77,85.20"
        data-elem={MuscleGroupEnum.TRAPS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.TRAPS)}
        d="M 424.86,75.58
           C 421.55,75.92 418.66,76.93 416.95,80.04
             414.97,83.62 413.60,87.52 413.16,91.51
             412.49,97.59 412.33,103.74 412.23,109.87
             412.08,118.66 412.18,127.46 412.19,136.26
             412.20,137.66 412.33,139.06 412.40,140.46
             413.72,133.20 418.01,127.57 422.22,121.89
             424.83,118.35 427.44,114.82 429.97,111.22
             432.62,107.45 435.01,103.52 436.13,98.99
             436.99,95.51 437.70,91.98 438.61,88.52
             439.53,85.05 441.09,81.93 444.29,79.96
             445.13,79.45 446.07,79.11 446.96,78.68
             446.97,78.57 446.98,78.46 446.98,78.35
             444.21,77.57 441.46,76.69 438.66,76.03
             434.10,74.96 429.48,75.11 424.86,75.58"
        data-elem={MuscleGroupEnum.TRAPS}
        id="path18"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.TRAPS)}
        d="M 406.94,81.88
           C 405.66,78.80 403.68,76.45 400.04,75.90
             395.59,75.23 391.12,74.84 386.69,75.63
             383.08,76.27 379.54,77.36 375.97,78.25
             375.97,78.42 375.97,78.59 375.97,78.76
             381.23,80.32 383.31,84.46 384.56,89.25
             385.03,91.07 385.37,92.93 385.78,94.78
             386.65,98.66 387.50,102.60 389.72,105.96
             393.31,111.39 397.04,116.74 400.84,122.03
             404.25,126.77 407.77,131.41 409.53,137.08
             409.90,138.27 410.14,139.50 410.51,140.99
             410.61,139.02 410.79,137.33 410.77,135.65
             410.67,122.97 410.48,110.28 410.41,97.59
             410.37,92.08 409.03,86.89 406.94,81.88"
        data-elem={MuscleGroupEnum.TRAPS}
        id="path16"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className="fill-transparent"
        d="M 410.93,37.50
           C 410.93,37.50 416.18,38.40 416.18,38.40
             416.18,38.40 418.73,39.60 418.73,39.60
             418.73,39.60 422.18,43.05 422.18,43.05
             422.18,43.05 424.28,48.60 424.28,48.60
             424.28,48.60 425.48,55.35 425.48,55.35
             425.48,55.35 426.53,61.65 426.53,61.65
             426.53,61.65 428.48,65.55 428.48,65.55
             428.48,65.55 434.33,69.60 434.33,69.60
             434.33,69.60 440.18,72.75 440.18,72.75
             440.18,72.75 446.48,75.00 446.48,75.00
             446.48,75.00 449.93,75.75 449.93,75.75
             449.93,75.75 445.28,79.80 445.28,79.80
             445.28,79.80 441.23,84.30 441.23,84.30
             441.23,84.30 439.58,87.75 439.58,87.75
             439.58,87.75 438.23,93.15 438.23,93.15
             438.23,93.15 436.58,100.35 436.58,100.35
             436.58,100.35 434.18,105.75 434.18,105.75
             434.18,105.75 430.28,112.05 430.28,112.05
             430.28,112.05 420.08,126.30 420.08,126.30
             420.08,126.30 414.68,136.80 414.68,136.80
             414.68,136.80 411.23,142.95 411.23,142.95
             411.23,142.95 407.78,135.60 407.78,135.60
             407.78,135.60 405.23,130.50 405.23,130.50
             405.23,130.50 402.68,126.60 402.68,126.60
             402.68,126.60 398.17,118.95 398.17,118.95
             398.17,118.95 394.87,114.45 394.87,114.45
             394.87,114.45 390.37,108.15 390.37,108.15
             390.37,108.15 387.52,102.15 387.52,102.15
             387.52,102.15 385.12,94.05 385.12,94.05
             385.12,94.05 383.02,86.85 383.02,86.85
             383.02,86.85 380.17,82.65 380.17,82.65
             380.17,82.65 376.12,79.35 376.12,79.35
             376.12,79.35 374.32,78.15 374.32,78.15
             374.32,78.15 372.37,76.05 372.37,76.05
             372.37,76.05 380.62,74.55 380.62,74.55
             380.62,74.55 387.22,71.25 387.22,71.25
             387.22,71.25 394.42,65.85 394.42,65.85
             394.42,65.85 396.67,61.50 396.67,61.50
             396.67,61.50 397.57,57.15 397.57,57.15
             397.57,57.15 399.82,48.15 399.82,48.15
             399.82,48.15 400.42,44.40 400.42,44.40
             400.42,44.40 403.28,40.95 403.28,40.95
             403.28,40.95 408.08,38.40 408.08,38.40
             408.08,38.40 410.78,37.80 410.78,37.80"
        data-elem={MuscleGroupEnum.TRAPS}
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.TRAPS)}
        d="M 85.02,84.19
           C 86.82,83.05 90.21,81.65 92.21,82.21
             92.23,82.21 92.25,82.22 92.27,82.22
             93.75,82.66 95.25,83.04 96.74,83.43
             99.90,84.27 103.08,85.07 106.25,85.88
             106.95,86.06 107.66,86.23 108.37,86.34
             108.63,86.37 108.92,86.19 109.19,86.11
             109.11,85.84 109.10,85.49 108.93,85.30
             107.66,83.93 106.33,82.62 105.07,81.24
             102.63,78.57 100.12,75.95 98.64,72.56
             98.25,71.65 97.57,71.78 96.83,72.13
             93.52,73.69 90.21,75.24 86.89,76.78
             86.04,77.18 85.16,77.53 84.06,78.00
             84.06,78.00 79.16,80.15 79.16,80.15
             79.16,80.15 84.02,81.63 85.02,84.19"
        data-elem={MuscleGroupEnum.TRAPS}
        id="path200"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.TRAPS)}
        d="M 130.40,72.56
           C 128.92,75.95 126.41,78.57 123.97,81.24
             122.71,82.62 121.38,83.93 120.11,85.30
             119.94,85.49 119.93,85.84 119.85,86.11
             120.12,86.19 120.41,86.37 120.67,86.34
             121.38,86.23 122.09,86.06 122.79,85.88
             125.96,85.07 129.14,84.27 132.30,83.43
             133.79,83.04 135.29,82.66 136.77,82.22
             136.79,82.22 136.80,82.21 136.83,82.21
             138.83,81.65 142.22,83.05 144.02,84.19
             145.02,81.63 149.88,80.15 149.88,80.15
             149.88,80.15 144.98,78.00 144.98,78.00
             143.88,77.53 143.00,77.18 142.15,76.78
             138.83,75.24 135.52,73.69 132.21,72.13
             131.47,71.78 130.79,71.65 130.40,72.56"
        data-elem={MuscleGroupEnum.TRAPS}
        id="path196"
        stroke="black"
        strokeWidth="0"
      />

      <path
        className={getMuscleClasses(MuscleGroupEnum.TRAPS)}
        d="M 419.29,75.38
           C 419.29,75.38 424.79,74.06 424.79,74.06
             424.79,74.06 424.79,74.06 424.79,74.06
             428.21,74.17 431.63,74.14 435.05,74.19
             435.22,74.20 435.39,74.11 435.85,74.00
             435.36,73.69 435.09,73.53 434.82,73.37
             432.73,72.13 430.62,70.93 428.57,69.64
             425.94,68.01 424.70,65.57 424.56,62.50
             424.44,59.85 424.17,57.19 424.06,54.54
             423.90,50.55 422.75,46.88 420.76,43.45
             420.58,43.15 420.31,42.90 420.09,42.63
             419.96,42.67 419.83,42.71 419.71,42.76
             420.15,47.98 420.60,53.21 421.05,58.46
             420.96,58.13 420.84,57.82 420.80,57.49
             420.11,52.46 419.45,47.42 418.72,42.39
             418.64,41.83 418.26,41.11 417.81,40.84
             413.99,38.61 410.08,38.57 406.09,40.49
             404.91,41.06 404.55,41.84 404.48,43.11
             404.20,48.78 403.43,54.39 401.96,59.89
             401.81,60.47 401.54,61.01 401.33,61.57
             402.70,53.56 403.58,44.00 402.82,42.59
             402.50,43.02 402.16,43.38 401.92,43.80
             400.42,46.37 399.44,49.12 399.18,52.09
             398.84,55.85 398.64,59.62 398.25,63.38
             397.98,65.90 397.07,68.20 394.81,69.65
             392.86,70.89 390.86,72.05 388.87,73.23
             388.53,73.44 388.16,73.62 387.58,73.93
             388.04,74.07 388.25,74.18 388.45,74.17
             392.08,74.12 395.71,74.07 399.34,73.99
             399.34,73.99 399.33,74.00 399.33,74.00
             399.33,74.00 404.10,75.42 404.10,75.42
             408.96,79.25 410.94,84.36 411.51,90.45
             412.37,87.37 412.94,84.65 413.89,82.08
             414.88,79.39 416.88,77.35 419.29,75.38"
        data-elem={MuscleGroupEnum.TRAPS}
        id="path32"
        stroke="black"
        strokeWidth="0"
      />
    </g>
  );
}