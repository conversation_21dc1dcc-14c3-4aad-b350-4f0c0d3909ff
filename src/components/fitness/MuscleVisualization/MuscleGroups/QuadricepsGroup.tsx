import { MuscleGroupEnum } from "../../../../types/muscle.types";
import { MuscleGroupComponentProps } from "./types";

export function QuadricepsGroup({ onToggleMuscle, getMuscleClasses }: MuscleGroupComponentProps) {
  return (
    <>
    <g className="group cursor-pointer" onClick={() => onToggleMuscle(MuscleGroupEnum.QUADRICEPS)}>
      <path
        className="fill-transparent"
        d="M 77.00,190.50
           C 77.00,190.50 83.75,201.75 83.75,201.75
             83.75,201.75 88.25,208.75 88.25,208.75
             88.25,208.75 95.25,214.50 95.25,214.50
             95.25,214.50 106.75,222.50 106.75,222.50
             106.75,222.50 112.25,224.50 112.25,224.50
             112.25,224.50 112.75,239.50 112.75,239.50
             112.75,239.50 107.75,269.00 107.75,269.00
             107.75,269.00 104.00,280.75 104.00,280.75
             104.00,280.75 105.75,298.25 105.75,298.25
             105.75,298.25 104.50,314.50 104.50,314.50
             104.50,314.50 99.25,331.25 99.25,331.25
             99.25,331.25 95.50,336.75 95.50,336.75
             95.50,336.75 94.25,316.00 94.25,316.00
             94.25,316.00 90.75,307.75 90.75,307.75
             90.75,307.75 85.50,315.75 85.50,315.75
             85.50,315.75 78.00,326.25 78.00,326.25
             78.00,326.25 75.25,328.75 75.25,328.75
             75.25,328.75 74.00,303.00 74.00,303.00
             74.00,303.00 64.75,276.75 64.75,276.75
             64.75,276.75 64.25,264.25 64.25,264.25
             64.25,264.25 64.25,248.00 64.25,248.00
             64.25,248.00 65.75,221.25 65.75,221.25
             65.75,221.25 69.50,210.75 69.50,210.75
             69.50,210.75 74.00,198.50 74.00,198.50
             74.00,198.50 77.00,190.75 77.00,190.75M 118.25,225.00
           C 118.25,225.00 128.75,219.00 128.75,219.00
             128.75,219.00 137.25,212.25 137.25,212.25
             137.25,212.25 145.25,202.00 145.25,202.00
             145.25,202.00 152.75,190.75 152.75,190.75
             152.75,190.75 157.25,198.50 157.25,198.50
             157.25,198.50 162.00,212.25 162.00,212.25
             162.00,212.25 165.50,229.00 165.50,229.00
             165.50,229.00 166.50,244.25 166.50,244.25
             166.50,244.25 167.50,256.25 167.50,256.25
             167.50,256.25 164.25,273.00 164.25,273.00
             164.25,273.00 160.50,288.00 160.50,288.00
             160.50,288.00 157.00,298.75 157.00,298.75
             157.00,298.75 155.50,303.75 155.50,303.75
             155.50,303.75 155.50,316.25 155.50,316.25
             155.50,316.25 155.00,331.00 155.00,331.00
             155.00,331.00 147.75,319.75 147.75,319.75
             147.75,319.75 139.50,307.50 139.50,307.50
             139.50,307.50 138.00,308.75 138.00,308.75
             138.00,308.75 136.75,316.00 136.75,316.00
             136.75,316.00 135.75,324.00 135.75,324.00
             135.75,324.00 134.50,337.50 134.50,337.50
             134.50,337.50 128.00,324.25 128.00,324.25
             128.00,324.25 125.50,310.50 125.50,310.50
             125.50,310.50 125.50,298.25 125.50,298.25
             125.50,298.25 125.50,282.75 125.50,282.75
             125.50,282.75 125.75,279.50 125.75,279.50
             125.75,279.50 120.75,258.75 120.75,258.75
             120.75,258.75 118.25,247.00 118.25,247.00
             118.25,247.00 118.25,236.75 118.25,236.75
             118.25,236.75 118.00,228.75 118.00,228.75
             118.00,228.75 118.00,224.50 118.00,224.50"
        data-elem={MuscleGroupEnum.QUADRICEPS}
        stroke="black"
        strokeWidth="0"
      />
      <path
        className={getMuscleClasses(MuscleGroupEnum.QUADRICEPS)}
        d="M 98.39,269.85
           C 98.33,270.21 98.24,270.57 98.21,270.93
             97.87,274.80 97.58,278.68 97.20,282.55
             96.90,285.46 96.66,288.40 96.09,291.27
             95.09,296.32 93.86,301.31 92.76,306.33
             92.65,306.84 92.62,307.42 92.77,307.90
             95.03,314.93 95.42,322.18 95.37,329.48
             95.36,330.86 95.28,332.24 95.24,333.62
             95.34,333.63 95.45,333.64 95.55,333.65
             99.74,323.86 101.95,313.60 102.45,302.95
             102.99,291.71 101.95,280.64 98.86,269.81
             98.71,269.82 98.55,269.84 98.39,269.85"
        data-elem={MuscleGroupEnum.QUADRICEPS}
        id="path170"
        stroke="black"
        strokeWidth="0"
      />
      <path
        className={getMuscleClasses(MuscleGroupEnum.QUADRICEPS)}
        d="M 95.65,269.75
           C 95.73,264.98 95.58,260.18 94.51,255.53
             92.85,248.29 90.84,241.13 89.07,233.91
             86.68,224.21 84.38,214.49 82.04,204.79
             81.75,203.60 81.43,202.43 81.05,201.27
             80.97,201.01 80.60,200.84 80.36,200.63
             80.20,200.90 79.99,201.16 79.88,201.45
             79.79,201.70 79.80,201.98 79.80,202.25
             79.80,205.26 79.86,208.27 79.79,211.28
             79.71,214.97 79.65,218.68 79.36,222.36
             79.04,226.31 78.40,230.23 78.03,234.17
             77.30,241.88 76.57,249.59 76.03,257.31
             75.66,262.48 75.32,267.67 75.48,272.84
             75.74,281.94 76.41,291.03 76.86,300.13
             77.09,304.74 77.26,309.35 77.37,313.96
             77.46,317.76 77.42,321.57 77.44,325.64
             77.79,325.27 78.01,325.08 78.17,324.84
             80.70,321.12 83.31,317.46 85.70,313.66
             87.96,310.08 90.00,306.35 91.28,302.28
             93.64,294.74 94.44,286.92 95.25,279.11
             95.58,276.01 95.60,272.87 95.65,269.75"
        data-elem={MuscleGroupEnum.QUADRICEPS}
        id="path172"
        stroke="black"
        strokeWidth="0"
      />


      <path
        className={getMuscleClasses(MuscleGroupEnum.QUADRICEPS)}
        d="M 152.52,325.64
           C 152.54,321.57 152.50,317.76 152.60,313.96
             152.70,309.35 152.87,304.74 153.10,300.13
             153.55,291.03 154.23,281.94 154.49,272.84
             154.64,267.67 154.30,262.48 153.94,257.31
             153.39,249.59 152.66,241.88 151.93,234.17
             151.56,230.23 150.92,226.31 150.61,222.36
             150.32,218.68 150.25,214.97 150.17,211.28
             150.11,208.27 150.16,205.26 150.16,202.25
             150.16,201.98 150.17,201.70 150.08,201.45
             149.98,201.16 149.77,200.90 149.60,200.63
             149.36,200.84 148.99,201.01 148.91,201.27
             148.54,202.43 148.21,203.60 147.93,204.79
             145.58,214.49 143.28,224.21 140.90,233.91
             139.12,241.13 137.11,248.29 135.45,255.53
             134.38,260.18 134.23,264.98 134.32,269.75
             134.37,272.87 134.39,276.01 134.71,279.11
             135.53,286.92 136.33,294.74 138.69,302.28
             139.96,306.35 142.00,310.08 144.26,313.66
             146.66,317.46 149.27,321.12 151.79,324.84
             151.95,325.08 152.18,325.27 152.52,325.64"
        data-elem={MuscleGroupEnum.QUADRICEPS}
        id="path180"
        stroke="black"
        strokeWidth="0"
      />
      <path
        className={getMuscleClasses(MuscleGroupEnum.QUADRICEPS)}
        d="M 134.73,333.62
           C 134.68,332.24 134.61,330.86 134.60,329.48
             134.55,322.18 134.93,314.93 137.19,307.90
             137.35,307.42 137.32,306.84 137.21,306.33
             136.10,301.31 134.87,296.32 133.88,291.27
             133.31,288.40 133.06,285.46 132.77,282.55
             132.38,278.68 132.10,274.80 131.76,270.93
             131.73,270.57 131.63,270.21 131.57,269.85
             131.41,269.84 131.26,269.82 131.10,269.81
             128.01,280.64 126.98,291.71 127.51,302.95
             128.01,313.60 130.23,323.86 134.41,333.65
             134.52,333.64 134.62,333.63 134.73,333.62"
        data-elem={MuscleGroupEnum.QUADRICEPS}
        id="path182"
        stroke="black"
        strokeWidth="0"
      />
    </g>

    <g className="group cursor-pointer" onClick={() => onToggleMuscle(MuscleGroupEnum.QUADRICEPS)}>
    <path
        className={getMuscleClasses(MuscleGroupEnum.QUADRICEPS)}
        d="M 161.67,275.34
           C 162.84,267.34 163.81,262.99 164.20,255.84
             164.53,249.80 162.84,241.61 162.25,234.79
             161.37,224.48 157.87,212.75 155.00,202.72
             154.17,199.79 153.62,196.78 152.94,193.81
             152.88,193.53 152.79,193.24 152.72,192.96
             152.59,192.96 152.46,192.96 152.34,192.95
             152.21,194.99 152.02,197.03 151.97,199.07
             151.82,204.59 151.55,210.12 151.66,215.64
             151.75,219.94 152.32,224.23 152.71,228.52
             152.88,230.49 153.18,232.45 153.36,234.43
             154.39,245.74 155.80,255.40 156.01,266.76
             156.17,274.89 154.02,302.37 154.26,304.19
             154.33,303.92 161.64,275.60 161.67,275.34"
        data-elem={MuscleGroupEnum.QUADRICEPS}
        id="path186"
        stroke="black"
        strokeWidth="0"
      />
      <path
        className={getMuscleClasses(MuscleGroupEnum.QUADRICEPS)}
        d="M 68.30,275.34
           C 68.32,275.60 75.63,303.92 75.71,304.19
             75.94,302.37 73.80,274.89 73.95,266.76
             74.17,255.40 75.57,245.74 76.61,234.43
             76.79,232.45 77.08,230.49 77.26,228.52
             77.65,224.23 78.22,219.94 78.30,215.64
             78.42,210.12 78.15,204.59 78.00,199.07
             77.94,197.03 77.76,194.99 77.63,192.95
             77.50,192.96 77.37,192.96 77.25,192.96
             77.17,193.24 77.09,193.53 77.02,193.81
             76.34,196.78 75.80,199.79 74.96,202.72
             72.10,212.75 68.60,224.48 67.71,234.79
             67.13,241.61 65.44,249.80 65.76,255.84
             66.15,262.99 67.13,267.34 68.30,275.34"
        data-elem={MuscleGroupEnum.QUADRICEPS}
        id="path176"
        stroke="black"
        strokeWidth="0"
      />
    </g>

    <g className="group cursor-pointer" onClick={() => onToggleMuscle(MuscleGroupEnum.HIP_ADDUCTORS)}>
    <path
        className={getMuscleClasses(MuscleGroupEnum.HIP_ADDUCTORS)}
        d="M 108.84,225.24
           C 106.63,223.43 100.82,220.73 98.71,218.80
             93.08,213.68 90.27,212.08 86.11,205.66
             85.16,204.20 84.27,202.70 83.35,201.22
             83.23,201.24 83.11,201.26 82.99,201.28
             83.06,202.02 83.11,202.76 83.22,203.48
             84.36,210.42 86.19,217.18 88.33,223.88
             93.24,239.25 97.20,254.88 100.81,270.61
             101.42,273.27 101.95,275.95 102.52,278.62
             102.83,278.34 102.95,278.05 103.00,277.74
             103.47,274.88 105.14,269.24 105.53,266.37
             107.68,256.43 107.79,254.80 108.84,246.48
             109.82,238.30 110.01,236.87 110.01,232.26
             110.01,227.97 110.07,226.24 108.84,225.24"
        data-elem={MuscleGroupEnum.HIP_ADDUCTORS}
        id="path174"
        stroke="black"
        strokeWidth="0"
      />
      <path
        className={getMuscleClasses(MuscleGroupEnum.HIP_ADDUCTORS)}
        d="M 129.16,270.61
           C 132.77,254.88 136.72,239.25 141.63,223.88
             143.77,217.18 145.61,210.42 146.74,203.48
             146.86,202.76 146.90,202.02 146.98,201.28
             146.86,201.26 146.74,201.24 146.61,201.22
             145.70,202.70 144.80,204.20 143.85,205.66
             139.69,212.08 136.88,213.68 131.26,218.80
             129.14,220.73 123.33,223.43 121.12,225.24
             119.90,226.24 119.95,227.97 119.95,232.26
             119.95,236.87 120.14,238.30 121.12,246.48
             122.17,254.80 122.29,256.43 124.43,266.37
             124.83,269.24 126.49,274.88 126.96,277.74
             127.02,278.05 127.13,278.34 127.45,278.62
             128.01,275.95 128.55,273.27 129.16,270.61"
        data-elem={MuscleGroupEnum.HIP_ADDUCTORS}
        id="path188"
        stroke="black"
        strokeWidth="0"
      />
    </g>
    </>
  );
} 