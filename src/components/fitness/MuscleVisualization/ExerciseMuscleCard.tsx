import React from 'react';
import { MuscleGroupEnum, MUSCLE_NAMES } from '../../../types/muscle.types';
import { MuscleIllustration } from './MuscleIllustration';
import './ExerciseMuscleCard.scss';

interface ExerciseMuscleCardProps {
  /** 主要训练的肌肉群 */
  primaryMuscles: MuscleGroupEnum[];
  /** 次要训练的肌肉群 */
  secondaryMuscles: MuscleGroupEnum[];
  /** 当前主题 */
  theme?: 'light' | 'dark';
  /** 卡片大小 */
  size?: 'sm' | 'md' | 'lg';
  /** 自定义类名 */
  className?: string;
}

export const ExerciseMuscleCard: React.FC<ExerciseMuscleCardProps> = ({
  primaryMuscles = [],
  secondaryMuscles = [],
  theme = 'light',
  size = 'md',
  className = ''
}) => {
  // 合并所有选中的肌肉群
  const allSelectedMuscles = [...primaryMuscles, ...secondaryMuscles];

  return (
    <div className={`exercise-muscle-card ${size} ${theme} ${className}`}>
      {/* 左侧：肌肉文本信息 */}
      <div className="muscle-text-section">
        <div className="muscle-text-content">
          {/* 主要肌肉 */}
          {primaryMuscles.length > 0 && (
            <div className="muscle-group">
              <h4 className="muscle-category">主要</h4>
              <div className="muscle-list">
                {primaryMuscles.map((muscle) => (
                  <span key={muscle} className="muscle-tag primary">
                    {MUSCLE_NAMES[muscle]}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* 次要肌肉 */}
          {secondaryMuscles.length > 0 && (
            <div className="muscle-group">
              <h4 className="muscle-category">次要</h4>
              <div className="muscle-list">
                {secondaryMuscles.map((muscle) => (
                  <span key={muscle} className="muscle-tag secondary">
                    {MUSCLE_NAMES[muscle]}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* 空状态 */}
          {primaryMuscles.length === 0 && secondaryMuscles.length === 0 && (
            <div className="empty-state">
              <span className="empty-text">暂无训练部位信息</span>
            </div>
          )}
        </div>
      </div>

      {/* 右侧：肌肉视图 */}
      <div className="muscle-visual-section">
        <div 
          className="muscle-illustration-container"
          data-primary-muscles={primaryMuscles.join(',')}
          data-secondary-muscles={secondaryMuscles.join(',')}
        >
          <MuscleIllustration
            selectedMuscles={allSelectedMuscles}
            onToggleMuscle={() => {}} // 不需要点击选中功能
            theme={theme}
            isLoading={false}
          />
        </div>
      </div>
    </div>
  );
};

export default ExerciseMuscleCard; 