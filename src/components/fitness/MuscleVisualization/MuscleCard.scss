.muscle-card {
  // 基础样式
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border);
  padding: var(--space-4);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  // iOS Safe Area支持
  padding-top: max(var(--space-4), env(safe-area-inset-top));
  padding-bottom: max(var(--space-4), env(safe-area-inset-bottom));
  
  // 悬停效果 - 仅非触摸设备
  @media (hover: hover) {
    &:hover {
      box-shadow: var(--shadow-lg);
      transform: translateY(-2px);
    }
  }
  
  // 仅文本模式
  &.text-only {
    .muscle-text-content {
      .primary-muscles,
      .secondary-muscles {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        margin-bottom: var(--space-2);
        
        .label {
          font-size: var(--text-sm);
          font-weight: 600;
          color: var(--text-secondary);
          min-width: 48px;
        }
        
        .muscles {
          font-size: var(--text-sm);
          color: var(--text-primary);
        }
      }
      
      .secondary-muscles {
        margin-bottom: 0;
        
        .label {
          color: var(--text-muted);
        }
        
        .muscles {
          color: var(--text-secondary);
        }
      }
    }
  }
  
  // 可视化模式
  &.with-visualization {
    display: flex;
    gap: var(--space-4);
    
    .muscle-visualization-container {
      flex-shrink: 0;
      width: 80px; // 小尺寸默认
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--bg-secondary);
      border-radius: var(--radius-md);
      padding: var(--space-2);
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      
      // SVG优化
      svg {
        width: 100%;
        height: auto;
        will-change: transform; // iOS硬件加速
        transition: inherit;
      }
    }
    
    // 交互式状态
    &.interactive {
      cursor: pointer;
      user-select: none;
      -webkit-user-select: none;
      -webkit-tap-highlight-color: transparent;
      
      .muscle-visualization-container {
        // iOS触摸反馈
        &:active {
          transform: scale(0.95);
          background: var(--primary-50);
        }
      }
      
      // 悬停效果（非触摸设备）
      @media (hover: hover) {
        &:hover .muscle-visualization-container {
          background: var(--primary-50);
          box-shadow: var(--shadow-md);
        }
      }
    }
    
    .muscle-info {
      flex: 1;
      min-width: 0; // 防止flex溢出
      
      .primary-muscles,
      .secondary-muscles {
        margin-bottom: var(--space-3);
        
        .label {
          display: block;
          font-size: var(--text-xs);
          font-weight: 600;
          color: var(--text-secondary);
          margin-bottom: var(--space-1);
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        
        .muscle-tags {
          display: flex;
          flex-wrap: wrap;
          gap: var(--space-1);
          
          .muscle-tag {
            display: inline-block;
            padding: var(--space-1) var(--space-2);
            font-size: var(--text-xs);
            font-weight: 500;
            border-radius: var(--radius-full);
            white-space: nowrap;
            
            // iOS触摸目标最小尺寸
            min-height: 24px;
            line-height: 1;
            
            &.primary {
              background: var(--primary-100);
              color: var(--primary-700);
            }
            
            &.secondary {
              background: var(--slate-100);
              color: var(--slate-600);
            }
          }
        }
      }
      
      .secondary-muscles {
        margin-bottom: 0;
      }
    }
  }
  
  // 尺寸变体
  &.sm {
    .muscle-visualization-container {
      width: 60px;
      height: 60px;
    }
    
    .muscle-info {
      .muscle-tags .muscle-tag {
        padding: 2px var(--space-1);
        font-size: 10px;
      }
    }
  }
  
  &.md {
    .muscle-visualization-container {
      width: 100px;
      height: 100px;
    }
  }
  
  // 暗色主题
  &.dark {
    background: var(--slate-800);
    border-color: var(--slate-700);
    
    .muscle-visualization-container {
      background: var(--slate-700);
    }
    
    .muscle-info {
      .primary-muscles,
      .secondary-muscles {
        .label {
          color: var(--slate-400);
        }
        
        .muscle-tags .muscle-tag {
          &.primary {
            background: var(--blue-900);
            color: var(--blue-200);
          }
          
          &.secondary {
            background: var(--slate-700);
            color: var(--slate-300);
          }
        }
      }
    }
    
    // 暗色主题交互反馈
    &.interactive {
      .muscle-visualization-container {
        &:active {
          background: var(--blue-800);
        }
      }
      
      @media (hover: hover) {
        &:hover .muscle-visualization-container {
          background: var(--blue-800);
        }
      }
    }
  }
  
  // iOS响应式断点
  @media (max-width: 375px) {
    padding: var(--space-3);
    
    &.with-visualization {
      flex-direction: column;
      gap: var(--space-3);
      
      .muscle-visualization-container {
        width: 60px;
        height: 60px;
        align-self: center;
      }
    }
  }
  
  // iPad横屏优化
  @media (min-width: 768px) and (orientation: landscape) {
    &.with-visualization {
      .muscle-visualization-container {
        width: 120px;
        height: 120px;
      }
    }
  }
  
  // 动画优化 - 减少重排重绘
  * {
    transform-style: preserve-3d;
    backface-visibility: hidden;
  }
}

// iOS专用优化
@supports (-webkit-appearance: none) {
  .muscle-card {
    // WebKit特定优化
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    
    &.with-visualization {
      .muscle-visualization-container {
        // 启用硬件加速
        will-change: transform;
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
      }
    }
    
    // iOS暗色模式适配
    @media (prefers-color-scheme: dark) {
      &:not(.light) {
        background: var(--slate-800);
        border-color: var(--slate-700);
      }
    }
  }
} 