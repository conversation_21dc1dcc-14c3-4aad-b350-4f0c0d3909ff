// 简化的肌肉可视化组件样式
.simple-muscle-illustration {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  // iOS优化
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  
  // 人体图容器
  .muscle-body-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    
    // SVG包装器
    .muscle-svg-wrapper {
      position: relative;
      width: 100%;
      max-width: 400px;
      
      // 主要SVG样式
      .muscle-illustration-simple {
        width: 100%;
        height: auto;
        display: block;
        
        // iOS硬件加速
        will-change: transform;
        transform: translateZ(0);
        backfaceVisibility: hidden;
        
        // 优化SVG渲染
        shape-rendering: optimizeSpeed;
        image-rendering: optimizeSpeed;
        
        // 禁用鼠标交互
        pointer-events: none;
      }
    }
  }
  
  // 主题适配
  &.light {
    .muscle-illustration-simple {
      .body-outline {
        stroke: #64748b;
      }
    }
  }
  
  &.dark {
    .muscle-illustration-simple {
      .body-outline {
        stroke: #94a3b8;
      }
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  .simple-muscle-illustration {
    .muscle-body-container {
      .muscle-svg-wrapper {
        max-width: 300px;
      }
    }
  }
}

@media (max-width: 480px) {
  .simple-muscle-illustration {
    .muscle-body-container {
      .muscle-svg-wrapper {
        max-width: 250px;
      }
    }
  }
} 