import React from 'react';
import { MuscleGroupEnum } from '../../../types/muscle.types';
import { MuscleIllustration } from './MuscleIllustration';
import { convertStringToMuscleEnum } from '../../../utils/muscleUtils';
import { useTheme } from '../../../contexts/ThemeContext';
import './MuscleCard.scss';

interface MuscleCardProps {
  primaryMuscles: string[];
  secondaryMuscles?: string[];
  size?: 'sm' | 'md';
  showVisualization?: boolean;
  interactive?: boolean;
  className?: string;
  theme?: 'light' | 'dark'; // 可选，优先级高于context
}

export const MuscleCard: React.FC<MuscleCardProps> = ({
  primaryMuscles = [],
  secondaryMuscles = [],
  size = 'sm',
  showVisualization = true,
  interactive = false,
  className = '',
  theme: propTheme // 重命名避免冲突
}) => {
  // 使用主题上下文，props中的theme优先
  const { theme: contextTheme } = useTheme();
  const currentTheme = propTheme || contextTheme;

  // 将中文肌肉名称映射到枚举
  const primaryMuscleEnums = convertStringToMuscleEnum(primaryMuscles);
  const secondaryMuscleEnums = convertStringToMuscleEnum(secondaryMuscles || []);
  const allSelectedMuscles = [...primaryMuscleEnums, ...secondaryMuscleEnums];

  const handleMuscleToggle = (muscle: MuscleGroupEnum) => {
    if (interactive) {
      // 在交互模式下的处理逻辑
      console.log('Muscle toggled:', muscle);
      
      // iOS触摸反馈
      if (navigator.vibrate) {
        navigator.vibrate(10);
      }
    }
  };

  // iOS触摸反馈处理
  const handleTouchStart = (e: React.TouchEvent) => {
    if (interactive) {
      // iOS轻微缩放反馈
      const target = e.currentTarget as HTMLElement;
      target.style.transform = 'scale(0.98)';
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (interactive) {
      // 恢复原始大小
      const target = e.currentTarget as HTMLElement;
      target.style.transform = 'scale(1)';
    }
  };

  if (!showVisualization) {
    return (
      <div 
        className={`muscle-card text-only ${currentTheme} ${className}`}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
      >
        <div className="muscle-text-content">
          {primaryMuscles.length > 0 && (
            <div className="primary-muscles">
              <span className="label">主要:</span>
              <span className="muscles">{primaryMuscles.join(', ')}</span>
            </div>
          )}
          {secondaryMuscles && secondaryMuscles.length > 0 && (
            <div className="secondary-muscles">
              <span className="label">辅助:</span>
              <span className="muscles">{secondaryMuscles.join(', ')}</span>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`muscle-card with-visualization ${size} ${currentTheme} ${interactive ? 'interactive' : ''} ${className}`}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
    >
      <div className="muscle-visualization-container">
        <MuscleIllustration
          selectedMuscles={allSelectedMuscles}
          onToggleMuscle={handleMuscleToggle}
          theme={currentTheme}
          isLoading={false}
        />
      </div>
      
      <div className="muscle-info">
        {primaryMuscles.length > 0 && (
          <div className="primary-muscles">
            <span className="label">主要肌群</span>
            <div className="muscle-tags primary">
              {primaryMuscles.map((muscle, index) => (
                <span key={index} className="muscle-tag primary">
                  {muscle}
                </span>
              ))}
            </div>
          </div>
        )}
        
        {secondaryMuscles && secondaryMuscles.length > 0 && (
          <div className="secondary-muscles">
            <span className="label">辅助肌群</span>
            <div className="muscle-tags secondary">
              {secondaryMuscles.map((muscle, index) => (
                <span key={index} className="muscle-tag secondary">
                  {muscle}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}; 