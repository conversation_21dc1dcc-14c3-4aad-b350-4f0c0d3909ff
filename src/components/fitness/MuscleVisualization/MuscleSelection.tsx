import { useState, useCallback, useMemo } from 'react';
import { MuscleGroupEnum, MUSCLE_DISPLAY_NAMES } from '../../../types/muscle.types';
import { MuscleIllustration } from './MuscleIllustration';

interface MuscleSelectionProps {
  initialSelection?: MuscleGroupEnum[];
  onSelectionChange?: (selectedMuscles: MuscleGroupEnum[]) => void;
  maxSelections?: number;
  theme?: 'light' | 'dark';
  className?: string;
}

// 肌肉群分类
const MUSCLE_CATEGORIES = {
  upper: [
    MuscleGroupEnum.CHEST,
    MuscleGroupEnum.BACK,
    MuscleGroupEnum.SHOULDERS,
    MuscleGroupEnum.BICEPS,
    MuscleGroupEnum.TRICEPS,
    MuscleGroupEnum.FOREARMS,
    MuscleGroupEnum.TRAPS,
    MuscleGroupEnum.LATS
  ],
  core: [
    MuscleGroupEnum.ABDOMINALS,
    MuscleGroupEnum.OBLIQUES,
    MuscleGroupEnum.LOWER_BACK
  ],
  lower: [
    MuscleGroupEnum.QUADRICEPS,
    MuscleGroupEnum.HAMSTRINGS,
    MuscleGroupEnum.GLUTES,
    MuscleGroupEnum.CALVES
  ]
} as const;

// 肌肉群显示名称映射已从types文件导入

export const MuscleSelection: React.FC<MuscleSelectionProps> = ({
  initialSelection = [],
  onSelectionChange,
  maxSelections,
  theme = 'light',
  className = ''
}) => {
  const [selectedMuscles, setSelectedMuscles] = useState<MuscleGroupEnum[]>(initialSelection);

  // 处理肌肉群切换
  const handleToggleMuscle = useCallback((muscle: MuscleGroupEnum) => {
    setSelectedMuscles(prev => {
      let newSelection: MuscleGroupEnum[];
      
      if (prev.includes(muscle)) {
        // 取消选择
        newSelection = prev.filter(m => m !== muscle);
      } else {
        // 添加选择（检查最大选择数限制）
        if (maxSelections && prev.length >= maxSelections) {
          // 如果达到最大选择数，替换最后选择的肌肉群
          newSelection = [...prev.slice(0, -1), muscle];
        } else {
          newSelection = [...prev, muscle];
        }
      }
      
      // 通知父组件选择变化
      onSelectionChange?.(newSelection);
      return newSelection;
    });
  }, [maxSelections, onSelectionChange]);

  // 批量选择分类
  const handleSelectCategory = useCallback((category: keyof typeof MUSCLE_CATEGORIES) => {
    const categoryMuscles = MUSCLE_CATEGORIES[category];
    setSelectedMuscles(prev => {
      const newSelection = [...new Set([...prev, ...categoryMuscles])];
      onSelectionChange?.(newSelection);
      return newSelection;
    });
  }, [onSelectionChange]);

  // 清空选择
  const handleClearSelection = useCallback(() => {
    setSelectedMuscles([]);
    onSelectionChange?.([]);
  }, [onSelectionChange]);

  // 选择统计
  const selectionStats = useMemo(() => {
    const stats = {
      total: selectedMuscles.length,
      upper: selectedMuscles.filter(m => (MUSCLE_CATEGORIES.upper as readonly MuscleGroupEnum[]).includes(m)).length,
      core: selectedMuscles.filter(m => (MUSCLE_CATEGORIES.core as readonly MuscleGroupEnum[]).includes(m)).length,
      lower: selectedMuscles.filter(m => (MUSCLE_CATEGORIES.lower as readonly MuscleGroupEnum[]).includes(m)).length
    };
    return stats;
  }, [selectedMuscles]);

  return (
    <div className={`muscle-selection ${className}`}>
      {/* 控制面板 */}
      <div className={`control-panel p-4 rounded-lg mb-6 ${
        theme === 'dark' ? 'bg-slate-800' : 'bg-gray-50'
      }`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className={`text-lg font-semibold ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            肌肉群选择 ({selectionStats.total} 个已选择)
          </h3>
          
          {selectedMuscles.length > 0 && (
            <button
              onClick={handleClearSelection}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                theme === 'dark'
                  ? 'bg-red-600 hover:bg-red-700 text-white'
                  : 'bg-red-500 hover:bg-red-600 text-white'
              }`}
            >
              清空选择
            </button>
          )}
        </div>

        {/* 快速选择按钮 */}
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            onClick={() => handleSelectCategory('upper')}
            className={`px-3 py-1 rounded text-sm transition-colors ${
              theme === 'dark'
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            }`}
          >
            上肢 ({selectionStats.upper})
          </button>
          <button
            onClick={() => handleSelectCategory('core')}
            className={`px-3 py-1 rounded text-sm transition-colors ${
              theme === 'dark'
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-green-500 hover:bg-green-600 text-white'
            }`}
          >
            核心 ({selectionStats.core})
          </button>
          <button
            onClick={() => handleSelectCategory('lower')}
            className={`px-3 py-1 rounded text-sm transition-colors ${
              theme === 'dark'
                ? 'bg-orange-600 hover:bg-orange-700 text-white'
                : 'bg-orange-500 hover:bg-orange-600 text-white'
            }`}
          >
            下肢 ({selectionStats.lower})
          </button>
        </div>

        {/* 已选择的肌肉群标签 */}
        {selectedMuscles.length > 0 && (
          <div className="selected-muscles">
            <div className="flex flex-wrap gap-2">
              {selectedMuscles.map(muscle => (
                <span
                  key={muscle}
                  className={`inline-flex items-center px-2 py-1 rounded text-xs ${
                    theme === 'dark'
                      ? 'bg-blue-600 text-white'
                      : 'bg-blue-100 text-blue-800'
                  }`}
                >
                  {MUSCLE_DISPLAY_NAMES[muscle]}
                  <button
                    onClick={() => handleToggleMuscle(muscle)}
                    className="ml-1 hover:bg-red-500 hover:text-white rounded w-4 h-4 flex items-center justify-center text-xs"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 肌肉图谱 */}
      <div className="muscle-illustration-container">
        <MuscleIllustration
          selectedMuscles={selectedMuscles}
          onToggleMuscle={handleToggleMuscle}
          theme={theme}
        />
      </div>

      {/* 选择提示 */}
      <div className={`text-center text-sm mt-4 ${
        theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
      }`}>
        {maxSelections ? (
          <p>最多可选择 {maxSelections} 个肌肉群 ({selectedMuscles.length}/{maxSelections})</p>
        ) : (
          <p>点击肌肉图谱上的部位来选择或取消选择</p>
        )}
      </div>
    </div>
  );
}; 