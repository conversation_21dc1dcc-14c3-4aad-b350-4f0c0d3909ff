import React, { useMemo } from 'react';
import { MuscleGroupEnum } from '../../../types/muscle.types';

// 增强的肌肉可视化组件接口
interface EnhancedMuscleIllustrationProps {
  selectedMuscles: MuscleGroupEnum[];
  muscleColorConfig?: { [key: string]: 'primary' | 'secondary' };
  isLoading?: boolean;
  theme?: 'light' | 'dark';
}

// 肌肉群颜色配置
const MUSCLE_COLORS = {
  primary: '#3b82f6',   // 主要肌肉 - 蓝色
  secondary: '#93c5fd', // 协同肌肉 - 浅蓝色
  default: '#e5e7eb'    // 未选中 - 灰色
};

export const EnhancedMuscleIllustration: React.FC<EnhancedMuscleIllustrationProps> = ({
  selectedMuscles,
  muscleColorConfig = {},
  isLoading = false,
  theme: _theme = 'light'
}) => {
  
  // 获取肌肉颜色的函数
  const getMuscleColor = useMemo(() => {
    return (muscle: MuscleGroupEnum): string => {
      if (!selectedMuscles.includes(muscle)) {
        return MUSCLE_COLORS.default;
      }
      
      const colorType = muscleColorConfig[muscle];
      return colorType === 'primary' 
        ? MUSCLE_COLORS.primary 
        : colorType === 'secondary' 
        ? MUSCLE_COLORS.secondary 
        : MUSCLE_COLORS.primary; // 默认主要颜色
    };
  }, [selectedMuscles, muscleColorConfig]);

  // Loading状态
  if (isLoading) {
    return (
      <div className="enhanced-muscle-illustration-loading">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="enhanced-muscle-illustration">
      <svg
        className="muscle-svg"
        viewBox="0 0 600 800"
        xmlns="http://www.w3.org/2000/svg"
        style={{ width: '100%', height: 'auto', maxWidth: '400px' }}
      >
        <defs>
          {/* 定义渐变和样式 */}
          <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor={MUSCLE_COLORS.primary} stopOpacity="0.8"/>
            <stop offset="100%" stopColor={MUSCLE_COLORS.primary} stopOpacity="1"/>
          </linearGradient>
          <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor={MUSCLE_COLORS.secondary} stopOpacity="0.8"/>
            <stop offset="100%" stopColor={MUSCLE_COLORS.secondary} stopOpacity="1"/>
          </linearGradient>
        </defs>

        {/* 人体轮廓 */}
        <path
          d="M300 50 C320 50 340 60 350 80 L360 120 L370 180 L365 220 L360 260 L350 300 L340 340 L330 380 L320 420 L310 460 L300 500 L290 460 L280 420 L270 380 L260 340 L250 300 L240 260 L235 220 L240 180 L250 120 L260 80 C270 60 280 50 300 50 Z"
          fill="none"
          stroke="#d1d5db"
          strokeWidth="2"
        />

        {/* 胸大肌 - 前视图 */}
        {(selectedMuscles.includes(MuscleGroupEnum.CHEST)) && (
          <ellipse
            cx="300"
            cy="160"
            rx="40"
            ry="30"
            fill={getMuscleColor(MuscleGroupEnum.CHEST)}
            stroke="#1f2937"
            strokeWidth="1"
            opacity="0.8"
          />
        )}

        {/* 肱二头肌 - 左臂 */}
        {(selectedMuscles.includes(MuscleGroupEnum.BICEPS)) && (
          <>
            <ellipse
              cx="230"
              cy="200"
              rx="15"
              ry="25"
              fill={getMuscleColor(MuscleGroupEnum.BICEPS)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
            {/* 右臂 */}
            <ellipse
              cx="370"
              cy="200"
              rx="15"
              ry="25"
              fill={getMuscleColor(MuscleGroupEnum.BICEPS)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
          </>
        )}

        {/* 肱三头肌 - 后臂 */}
        {(selectedMuscles.includes(MuscleGroupEnum.TRICEPS)) && (
          <>
            <ellipse
              cx="220"
              cy="210"
              rx="12"
              ry="20"
              fill={getMuscleColor(MuscleGroupEnum.TRICEPS)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
            <ellipse
              cx="380"
              cy="210"
              rx="12"
              ry="20"
              fill={getMuscleColor(MuscleGroupEnum.TRICEPS)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
          </>
        )}

        {/* 三角肌 - 肩部 */}
        {(selectedMuscles.includes(MuscleGroupEnum.SHOULDERS) || 
          selectedMuscles.includes(MuscleGroupEnum.SHOULDERS_FRONT) ||
          selectedMuscles.includes(MuscleGroupEnum.SHOULDERS_BACK)) && (
          <>
            <circle
              cx="260"
              cy="140"
              r="20"
              fill={getMuscleColor(MuscleGroupEnum.SHOULDERS)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
            <circle
              cx="340"
              cy="140"
              r="20"
              fill={getMuscleColor(MuscleGroupEnum.SHOULDERS)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
          </>
        )}

        {/* 腹直肌 */}
        {(selectedMuscles.includes(MuscleGroupEnum.ABDOMINALS)) && (
          <rect
            x="280"
            y="200"
            width="40"
            height="80"
            rx="5"
            fill={getMuscleColor(MuscleGroupEnum.ABDOMINALS)}
            stroke="#1f2937"
            strokeWidth="1"
            opacity="0.8"
          />
        )}

        {/* 股四头肌 - 大腿前侧 */}
        {(selectedMuscles.includes(MuscleGroupEnum.QUADRICEPS)) && (
          <>
            <ellipse
              cx="270"
              cy="380"
              rx="20"
              ry="40"
              fill={getMuscleColor(MuscleGroupEnum.QUADRICEPS)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
            <ellipse
              cx="330"
              cy="380"
              rx="20"
              ry="40"
              fill={getMuscleColor(MuscleGroupEnum.QUADRICEPS)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
          </>
        )}

        {/* 腘绳肌 - 大腿后侧 */}
        {(selectedMuscles.includes(MuscleGroupEnum.HAMSTRINGS)) && (
          <>
            <ellipse
              cx="265"
              cy="390"
              rx="15"
              ry="35"
              fill={getMuscleColor(MuscleGroupEnum.HAMSTRINGS)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
            <ellipse
              cx="335"
              cy="390"
              rx="15"
              ry="35"
              fill={getMuscleColor(MuscleGroupEnum.HAMSTRINGS)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
          </>
        )}

        {/* 臀大肌 */}
        {(selectedMuscles.includes(MuscleGroupEnum.GLUTES)) && (
          <ellipse
            cx="300"
            cy="320"
            rx="35"
            ry="25"
            fill={getMuscleColor(MuscleGroupEnum.GLUTES)}
            stroke="#1f2937"
            strokeWidth="1"
            opacity="0.8"
          />
        )}

        {/* 小腿肌 */}
        {(selectedMuscles.includes(MuscleGroupEnum.CALVES) ||
          selectedMuscles.includes(MuscleGroupEnum.CALVES_FRONT) ||
          selectedMuscles.includes(MuscleGroupEnum.CALVES_BACK)) && (
          <>
            <ellipse
              cx="270"
              cy="480"
              rx="12"
              ry="30"
              fill={getMuscleColor(MuscleGroupEnum.CALVES)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
            <ellipse
              cx="330"
              cy="480"
              rx="12"
              ry="30"
              fill={getMuscleColor(MuscleGroupEnum.CALVES)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
          </>
        )}

        {/* 背阔肌 - 背部 */}
        {(selectedMuscles.includes(MuscleGroupEnum.LATS) || 
          selectedMuscles.includes(MuscleGroupEnum.BACK)) && (
          <ellipse
            cx="300"
            cy="200"
            rx="50"
            ry="40"
            fill={getMuscleColor(MuscleGroupEnum.LATS)}
            stroke="#1f2937"
            strokeWidth="1"
            opacity="0.6"
          />
        )}

        {/* 斜方肌 */}
        {(selectedMuscles.includes(MuscleGroupEnum.TRAPS)) && (
          <path
            d="M280 120 L320 120 L340 100 L350 110 L320 130 L280 130 L260 110 L270 100 Z"
            fill={getMuscleColor(MuscleGroupEnum.TRAPS)}
            stroke="#1f2937"
            strokeWidth="1"
            opacity="0.8"
          />
        )}

        {/* 前臂肌群 */}
        {(selectedMuscles.includes(MuscleGroupEnum.FOREARMS) ||
          selectedMuscles.includes(MuscleGroupEnum.FOREARMS_FRONT) ||
          selectedMuscles.includes(MuscleGroupEnum.FOREARMS_BACK)) && (
          <>
            <ellipse
              cx="230"
              cy="250"
              rx="8"
              ry="20"
              fill={getMuscleColor(MuscleGroupEnum.FOREARMS)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
            <ellipse
              cx="370"
              cy="250"
              rx="8"
              ry="20"
              fill={getMuscleColor(MuscleGroupEnum.FOREARMS)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
          </>
        )}

        {/* 腹斜肌 */}
        {(selectedMuscles.includes(MuscleGroupEnum.OBLIQUES)) && (
          <>
            <ellipse
              cx="260"
              cy="230"
              rx="15"
              ry="25"
              fill={getMuscleColor(MuscleGroupEnum.OBLIQUES)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
            <ellipse
              cx="340"
              cy="230"
              rx="15"
              ry="25"
              fill={getMuscleColor(MuscleGroupEnum.OBLIQUES)}
              stroke="#1f2937"
              strokeWidth="1"
              opacity="0.8"
            />
          </>
        )}

        {/* 下背部 */}
        {(selectedMuscles.includes(MuscleGroupEnum.LOWER_BACK)) && (
          <ellipse
            cx="300"
            cy="280"
            rx="30"
            ry="20"
            fill={getMuscleColor(MuscleGroupEnum.LOWER_BACK)}
            stroke="#1f2937"
            strokeWidth="1"
            opacity="0.6"
          />
        )}
      </svg>
      
      {/* 颜色图例 */}
      <div className="muscle-legend">
        <div className="legend-item">
          <div 
            className="legend-color" 
            style={{ backgroundColor: MUSCLE_COLORS.primary }}
          ></div>
          <span>目标肌群</span>
        </div>
        <div className="legend-item">
          <div 
            className="legend-color" 
            style={{ backgroundColor: MUSCLE_COLORS.secondary }}
          ></div>
          <span>协同肌群</span>
        </div>
      </div>
    </div>
  );
}; 