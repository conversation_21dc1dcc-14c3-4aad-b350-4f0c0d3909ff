# 人体肌肉可视化模块 - 使用指南

## 📋 概述

人体肌肉可视化模块是一个高性能、iOS优化的React组件，提供交互式人体肌肉选择功能。采用Apple Watch风格设计，完美适配移动端和桌面端。

## ✨ 核心特性

- 🍎 **iOS优化设计** - 完美适配iPhone、iPad，支持Safe Area
- 📱 **响应式布局** - 自适应不同屏幕尺寸
- 🎨 **主题系统** - 支持明暗主题切换
- ⚡ **高性能渲染** - 硬件加速，懒加载优化
- 🔄 **交互式选择** - 支持多肌肉群选择
- 📊 **多种配置** - 灵活的尺寸和模式配置

## 🚀 快速开始

### 基础使用

```tsx
import React from 'react';
import { MuscleVisualizationModule } from '@/components/fitness/MuscleVisualization';
import { MuscleGroupEnum } from '@/types/muscle.types';

function MyWorkoutPage() {
  const [selectedMuscles, setSelectedMuscles] = useState<MuscleGroupEnum[]>([]);

  return (
    <MuscleVisualizationModule
      selectedMuscles={selectedMuscles}
      onSelectionChange={setSelectedMuscles}
      size="md"
      theme="light"
      showLabels={true}
    />
  );
}
```

### 使用Hook管理状态

```tsx
import { useMuscleSelection } from '@/components/fitness/MuscleVisualization';

function WorkoutPlanPage() {
  const {
    selectedMuscles,
    toggleMuscle,
    clearSelection,
    selectionCount
  } = useMuscleSelection();

  return (
    <div>
      <MuscleVisualizationModule
        selectedMuscles={selectedMuscles}
        onMuscleToggle={toggleMuscle}
        size="lg"
        animated={true}
      />
      
      <div className="mt-4">
        <p>已选择 {selectionCount} 个肌肉群</p>
        <button onClick={clearSelection}>清除选择</button>
      </div>
    </div>
  );
}
```

## 📚 API 参考

### MuscleVisualizationModule Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `selectedMuscles` | `MuscleGroupEnum[]` | `[]` | 当前选中的肌肉群 |
| `onMuscleToggle` | `(muscle: MuscleGroupEnum) => void` | - | 肌肉切换回调 |
| `onSelectionChange` | `(muscles: MuscleGroupEnum[]) => void` | - | 选择变化回调 |
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | 组件尺寸 |
| `theme` | `'light' \| 'dark'` | `'light'` | 主题模式 |
| `mode` | `'interactive' \| 'readonly'` | `'interactive'` | 交互模式 |
| `showLabels` | `boolean` | `false` | 显示肌肉标签 |
| `animated` | `boolean` | `true` | 启用动画效果 |
| `isLoading` | `boolean` | `false` | 加载状态 |
| `className` | `string` | `''` | 自定义CSS类名 |

### useMuscleSelection Hook

```tsx
const {
  selectedMuscles,     // 当前选中的肌肉群
  toggleMuscle,        // 切换肌肉选择状态
  selectMuscles,       // 设置选中的肌肉群
  clearSelection,      // 清除所有选择
  isSelected,          // 检查肌肉是否被选中
  selectionCount       // 选中的肌肉数量
} = useMuscleSelection(initialMuscles);
```

## 🎨 样式定制

### CSS变量

```css
:root {
  /* 肌肉可视化专用变量 */
  --muscle-primary-color: #3b82f6;
  --muscle-secondary-color: #60a5fa;
  --muscle-selected-color: #1d4ed8;
  --muscle-hover-color: #2563eb;
  
  /* iOS触摸目标 */
  --ios-touch-target: 44px;
  
  /* 间距系统 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
}
```

### 自定义主题

```scss
.custom-muscle-theme {
  --muscle-primary-color: #10b981;
  --muscle-selected-color: #059669;
  
  .muscle-path {
    stroke-width: 2px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }
}
```

## 📱 iOS优化特性

### Safe Area适配

组件自动适配iOS设备的Safe Area，确保在刘海屏设备上正常显示：

```scss
.muscle-visualization-module {
  padding-top: max(var(--space-4), env(safe-area-inset-top));
  padding-bottom: max(var(--space-4), env(safe-area-inset-bottom));
}
```

### 触摸优化

- 最小触摸目标44px（符合Apple HIG标准）
- 触摸反馈动画
- 防止意外触摸

### 性能优化

- 硬件加速渲染
- 懒加载组件
- Intersection Observer优化
- 内存管理

## 🔧 高级用法

### 自定义肌肉群配置

```tsx
const customMuscleConfig = {
  [MuscleGroupEnum.CHEST]: {
    color: '#ef4444',
    label: '胸肌',
    priority: 1
  },
  [MuscleGroupEnum.BACK]: {
    color: '#3b82f6',
    label: '背肌',
    priority: 2
  }
};

<MuscleVisualizationModule
  selectedMuscles={selectedMuscles}
  customConfig={customMuscleConfig}
/>
```

### 集成到健身计划

```tsx
function WorkoutPlanBuilder() {
  const { selectedMuscles, toggleMuscle } = useMuscleSelection();
  
  const createWorkoutPlan = () => {
    const exercises = getExercisesForMuscles(selectedMuscles);
    // 创建训练计划逻辑
  };

  return (
    <div className="workout-plan-builder">
      <h2>选择目标肌肉群</h2>
      
      <MuscleVisualizationModule
        selectedMuscles={selectedMuscles}
        onMuscleToggle={toggleMuscle}
        size="lg"
        showLabels={true}
      />
      
      <button 
        onClick={createWorkoutPlan}
        disabled={selectedMuscles.length === 0}
      >
        生成训练计划
      </button>
    </div>
  );
}
```

## 🐛 故障排除

### 常见问题

1. **组件不显示**
   - 检查CSS变量是否正确导入
   - 确认主题系统已初始化

2. **触摸交互无响应**
   - 检查`mode`属性是否设置为`interactive`
   - 确认事件处理函数已正确传入

3. **iOS设备显示异常**
   - 检查Safe Area CSS变量支持
   - 确认viewport meta标签设置正确

### 调试模式

```tsx
<MuscleVisualizationModule
  selectedMuscles={selectedMuscles}
  onMuscleToggle={(muscle) => {
    console.log('肌肉切换:', muscle);
    toggleMuscle(muscle);
  }}
  className="debug-mode"
/>
```

## 📄 许可证

MIT License - 详见LICENSE文件