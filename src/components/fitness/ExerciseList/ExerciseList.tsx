import React, { useMemo } from 'react';
import { ExerciseListProps, WorkoutExercise } from '../../../types/feed.types';
import { MUSCLE_DISPLAY_NAMES } from '../../../types/muscle.types';
import './ExerciseList.scss';

export const ExerciseList: React.FC<ExerciseListProps> = ({
  exercises,
  maxVisible = 3,
  showImages = true,
  className = ''
}) => {
  // 计算显示的动作列表 - 严格按maxVisible限制，不支持展开
  const displayedExercises = useMemo(() => {
    return exercises.slice(0, maxVisible);
  }, [exercises, maxVisible]);

  // 获取动作的主要肌肉群显示名称
  const getPrimaryMuscleNames = (exercise: WorkoutExercise): string => {
    return exercise.primary_muscles
      .map(muscle => MUSCLE_DISPLAY_NAMES[muscle] || muscle)
      .join('、');
  };

  // 渲染动作项
  const renderExerciseItem = (exercise: WorkoutExercise, index: number) => (
    <div key={exercise.id} className="exercise-item">
      {/* 动作缩略图 */}
      {showImages && (
        <div className="exercise-image">
          {exercise.image_url ? (
            <img 
              src={exercise.image_url} 
              alt={exercise.name}
              loading="lazy"
              onError={(e) => {
                // 图片加载失败时显示默认图标
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.classList.remove('hidden');
              }}
            />
          ) : null}
          <div className={`exercise-icon ${exercise.image_url ? 'hidden' : ''}`}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path 
                d="M20.57 14.86L22 13.43 20.57 12 17 15.57 8.43 7 12 3.43 10.57 2 9.14 3.43 7.71 2 5.57 4.14 4.14 2.71 2.71 4.14l1.43 1.43L2 7.71l1.43 1.43L2 10.57 3.43 12 7 8.43 15.57 17 12 20.57 13.43 22l1.43-1.43L16.29 22l2.14-2.14 1.43 1.43 1.43-1.43-1.43-1.43L22 16.29l-1.43-1.43z" 
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
      )}

      {/* 动作信息 */}
      <div className="exercise-info">
        <div className="exercise-name">{exercise.name}</div>
        <div className="exercise-details">
          <span className="sets-info">{exercise.sets} 组</span>
          {exercise.reps > 0 && (
            <span className="reps-info">× {exercise.reps}</span>
          )}
          {exercise.weight > 0 && (
            <span className="weight-info">{exercise.weight}kg</span>
          )}
        </div>
        <div className="muscle-groups">
          {getPrimaryMuscleNames(exercise)}
        </div>
      </div>

      {/* 动作序号 */}
      <div className="exercise-index">
        {index + 1}
      </div>
    </div>
  );

  if (!exercises || exercises.length === 0) {
    return (
      <div className={`exercise-list empty ${className}`}>
        <div className="empty-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
            <path 
              d="M20.57 14.86L22 13.43 20.57 12 17 15.57 8.43 7 12 3.43 10.57 2 9.14 3.43 7.71 2 5.57 4.14 4.14 2.71 2.71 4.14l1.43 1.43L2 7.71l1.43 1.43L2 10.57 3.43 12 7 8.43 15.57 17 12 20.57 13.43 22l1.43-1.43L16.29 22l2.14-2.14 1.43 1.43 1.43-1.43-1.43-1.43L22 16.29l-1.43-1.43z" 
              fill="currentColor"
            />
          </svg>
          <span>暂无训练动作</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`exercise-list ${className}`}>
      {/* 动作列表 */}
      <div className="exercise-items">
        {displayedExercises.map((exercise, index) => 
          renderExerciseItem(exercise, index)
        )}
      </div>
    </div>
  );
};
