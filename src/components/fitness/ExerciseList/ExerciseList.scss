// ExerciseList 组件样式
.exercise-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2); // 减小间隙
  
  // 空状态
  &.empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--space-2);
      color: var(--text-tertiary);
      
      svg {
        opacity: 0.5;
      }
      
      span {
        font-size: var(--text-sm);
      }
    }
  }
}

// 动作列表容器
.exercise-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-1-5); // 减小动作项之间的间隙
}

// 单个动作项
.exercise-item {
  display: flex;
  align-items: center;
  gap: var(--space-2); // 减小内部间隙
  padding: var(--space-2);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal) var(--ease-in-out);
  position: relative;
  
  &:hover {
    background: var(--bg-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
  }
}

// 动作缩略图
.exercise-image {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  overflow: hidden;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .exercise-icon {
    color: var(--text-tertiary);
    
    &.hidden {
      display: none;
    }
    
    svg {
      width: 24px;
      height: 24px;
    }
  }
}

// 动作信息
.exercise-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  min-width: 0; // 防止文字溢出
  
  .exercise-name {
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    color: var(--text-primary);
    line-height: 1.4;
    
    // 文字截断
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .exercise-details {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--text-sm);
    color: var(--text-secondary);
    
    .sets-info {
      font-weight: var(--font-medium);
      color: var(--accent-500);
    }
    
    .reps-info,
    .weight-info {
      color: var(--text-tertiary);
    }
  }
  
  .muscle-groups {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
    line-height: 1.3;
    
    // 文字截断
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// 动作序号
.exercise-index {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--accent-500);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

// 查看更多区域
.view-more-section {
  display: flex;
  justify-content: center;
  padding-top: var(--space-2);
  border-top: 1px solid var(--border-light);
  
  .view-more-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-full);
    color: var(--accent-500);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);
    
    // iOS触摸优化
    -webkit-tap-highlight-color: transparent;
    min-height: var(--ios-touch-target);
    
    &:hover {
      background: var(--accent-500);
      color: white;
      border-color: var(--accent-500);
      transform: translateY(-1px);
      box-shadow: var(--shadow-sm);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    .btn-icon {
      transition: transform var(--transition-normal) var(--ease-in-out);
      
      &.rotated {
        transform: rotate(180deg);
      }
    }
  }
}

// 训练统计摘要
.exercise-summary {
  display: flex;
  justify-content: space-around;
  padding: var(--space-3);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  margin-top: var(--space-2);
  
  .summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-1);
    
    .summary-value {
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      color: var(--accent-500);
      line-height: 1;
    }
    
    .summary-label {
      font-size: var(--text-xs);
      color: var(--text-secondary);
      line-height: 1;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .exercise-item {
    padding: var(--space-2);
    gap: var(--space-2);
  }
  
  .exercise-image {
    width: 40px;
    height: 40px;
    
    .exercise-icon svg {
      width: 20px;
      height: 20px;
    }
  }
  
  .exercise-info {
    .exercise-name {
      font-size: var(--text-sm);
    }
    
    .exercise-details {
      font-size: var(--text-xs);
      gap: var(--space-1);
    }
    
    .muscle-groups {
      font-size: var(--text-xs);
    }
  }
  
  .exercise-index {
    width: 20px;
    height: 20px;
    font-size: var(--text-xs);
  }
  
  .exercise-summary {
    padding: var(--space-2);
    
    .summary-item {
      .summary-value {
        font-size: var(--text-base);
      }
      
      .summary-label {
        font-size: var(--text-xs);
      }
    }
  }
}

// 暗色主题适配
.theme-dark {
  .exercise-item {
    background: var(--bg-secondary);
    
    &:hover {
      background: var(--bg-hover);
    }
  }
  
  .exercise-image {
    background: var(--bg-tertiary);
  }
  
  .view-more-section .view-more-btn {
    border-color: var(--border-color);
    
    &:hover {
      background: var(--accent-500);
      border-color: var(--accent-500);
    }
  }
  
  .exercise-summary {
    background: var(--bg-secondary);
  }
}

// 减少动画支持
@media (prefers-reduced-motion: reduce) {
  .exercise-item,
  .view-more-section .view-more-btn,
  .view-more-section .btn-icon {
    transition: none;
  }
  
  .exercise-item:hover,
  .view-more-section .view-more-btn:hover,
  .view-more-section .view-more-btn:active {
    transform: none;
  }
}

// 高对比度支持
@media (prefers-contrast: high) {
  .exercise-item {
    border: 1px solid var(--border-color);
  }
  
  .view-more-section .view-more-btn {
    border-width: 2px;
  }
  
  .exercise-summary {
    border: 1px solid var(--border-color);
  }
}
