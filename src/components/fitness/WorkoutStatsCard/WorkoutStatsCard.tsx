import React from 'react';
import { Divider } from '@heroui/react';
import { WorkoutData } from '../../../types/feed.types';
import { formatWorkoutDuration } from '../../../utils/timeFormatter';
import './WorkoutStatsCard.scss';

export interface WorkoutStatsCardProps {
  // 训练数据
  workout: WorkoutData;
  // 自定义样式类名
  className?: string;
  // 是否显示分割线
  showDividers?: boolean;
  // 紧凑模式（减少间距）
  compact?: boolean;
  // 统计项目配置
  stats?: Array<'duration' | 'weight' | 'calories' | 'exercises'>;
}

export const WorkoutStatsCard: React.FC<WorkoutStatsCardProps> = ({
  workout,
  className = '',
  showDividers = true,
  compact = false,
  stats = ['duration', 'weight', 'calories']
}) => {
  // 统计数据映射
  const statMapping = {
    duration: {
      label: '持续时间',
      value: formatWorkoutDuration(workout.duration_seconds),
      key: 'duration'
    },
    weight: {
      label: '重量',
      value: `${workout.total_volume}千克`,
      key: 'weight'
    },
    calories: {
      label: '卡路里',
      value: `${workout.calories_burned || 0}千卡`,
      key: 'calories'
    },
    exercises: {
      label: '动作数',
      value: `${workout.exercises?.length || 0}个`,
      key: 'exercises'
    }
  };

  // 获取要显示的统计项目
  const displayStats = stats.map(statKey => statMapping[statKey]).filter(Boolean);

  // 如果没有统计数据，不渲染组件
  if (displayStats.length === 0) {
    return null;
  }

  return (
    <div className={`workout-stats-card ${compact ? 'compact' : ''} ${className}`}>
      <div className="workout-stats-summary">
        {showDividers && (
          <Divider 
            orientation="vertical" 
            className="stats-divider heroui-divider vertical leading-divider" 
          />
        )}
        
        {displayStats.map((stat, index) => (
          <React.Fragment key={stat.key}>
            <div className="workout-stat">
              <span className="stat-label">{stat.label}</span>
              <span className="stat-value">{stat.value}</span>
            </div>
            
            {showDividers && index < displayStats.length - 1 && (
              <Divider 
                orientation="vertical" 
                className="stats-divider heroui-divider vertical" 
              />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default WorkoutStatsCard; 