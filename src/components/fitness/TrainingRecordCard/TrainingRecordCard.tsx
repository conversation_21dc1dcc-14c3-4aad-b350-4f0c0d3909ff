import React, { useState, useCallback } from 'react';
import { Card, CardBody } from '@heroui/react';
import { StaticMuscleIllustration } from '../MuscleVisualization/StaticMuscleIllustration';
import { ExerciseList } from '../ExerciseList/ExerciseList';
import { CarouselItem } from '../../../types/feed.types';
import './TrainingRecordCard.scss';

export interface TrainingRecordCardProps {
  // 轮播项目（包含训练记录和用户图像）
  carouselItems: CarouselItem[];
  // 用户上传的图片
  userImages?: string[];
  // 自定义样式类名
  className?: string;
  // 查看更多回调
  onViewMore?: () => void;
  // 轮播切换回调
  onCarouselChange?: (index: number) => void;
}

export const TrainingRecordCard: React.FC<TrainingRecordCardProps> = ({
  carouselItems = [],
  userImages = [],
  className = '',
  // onViewMore, // 已不再使用
  onCarouselChange
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // 合并训练记录和用户图像数据
  const combinedItems: CarouselItem[] = React.useMemo(() => {
    const items = [...carouselItems];
    
    // 添加用户上传的图像作为轮播项
    userImages.forEach((imageUrl, index) => {
      items.push({
        id: `user-image-${index}`,
        type: 'user_image',
        content: {
          image_data: {
            url: imageUrl,
            alt: `用户上传图片 ${index + 1}`,
            caption: '用户分享'
          }
        }
      });
    });

    return items;
  }, [carouselItems, userImages]);

  // 处理轮播切换
  const handleCarouselChange = useCallback((index: number) => {
    setCurrentIndex(index);
    onCarouselChange?.(index);
  }, [onCarouselChange]);

  // 如果没有任何内容，不渲染组件
  if (combinedItems.length === 0) {
    return null;
  }

  // 分离训练记录和用户图像
  const workoutItems = carouselItems.filter(item => item.type !== 'user_image');
  const userImageItems = userImages.map((imageUrl, index) => ({
    id: `user-image-${index}`,
    type: 'user_image' as const,
    content: {
      image_data: {
        url: imageUrl,
        alt: `用户上传图片 ${index + 1}`,
        caption: '用户分享'
      }
    }
  }));

  // 根据当前索引确定显示内容类型
  const isShowingUserImage = currentIndex >= workoutItems.length;
  const currentWorkoutItem = !isShowingUserImage ? workoutItems[currentIndex] : null;

  return (
    <div className={`training-record-card-wrapper ${className}`}>
      <Card className="training-record-card" shadow="sm" radius="lg">
        <CardBody className="training-record-body p-0">
          {/* 内容显示区域 */}
          <div className="content-display-area">
            {/* 训练记录内容 */}
            {!isShowingUserImage && currentWorkoutItem && (
              <div className="workout-content">
                {/* 训练卡片的两个主要组件 */}
                <div className="exercise-list">
                  {/* 动作简要列表 */}
                  <ExerciseList
                    exercises={currentWorkoutItem.content.workout_data?.exercises || []}
                    maxVisible={3}
                    showImages={true}
                    className="simplified-exercise-list"
                  />
                </div>
                
                <div className="static-muscle-illustration">
                  {/* 肌肉示意图 */}
                  <StaticMuscleIllustration
                    selectedMuscles={currentWorkoutItem.content.workout_data?.exercises?.flatMap(ex => ex.primary_muscles || []) || []}
                    className="workout-muscle-viz"
                  />
                </div>
              </div>
            )}
            
            {/* 用户上传图像内容 */}
            {isShowingUserImage && userImageItems[currentIndex - workoutItems.length] && (
              <div className="user-image-content">
                <img 
                  src={userImageItems[currentIndex - workoutItems.length].content.image_data.url}
                  alt={userImageItems[currentIndex - workoutItems.length].content.image_data.alt}
                  className="user-uploaded-image"
                />
              </div>
            )}
            
            {/* 轮播项目导航 - 仅在多项目时显示 */}
            {combinedItems.length > 1 && (
              <div className="carousel-navigation">
                {combinedItems.map((_, index) => (
                  <button
                    key={index}
                    className={`nav-dot ${index === currentIndex ? 'active' : ''}`}
                    onClick={() => handleCarouselChange(index)}
                    aria-label={`切换到第${index + 1}项`}
                  />
                ))}
              </div>
            )}
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default TrainingRecordCard; 