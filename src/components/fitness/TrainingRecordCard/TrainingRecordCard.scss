// TrainingRecordCard 组件样式
.training-record-card-wrapper {
  margin-top: var(--space-3);
  
  .training-record-card {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--radius-lg); // 大圆角设计
    transition: all var(--transition-normal) var(--ease-in-out);
    overflow: hidden;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }
    
    .training-record-body {
      padding: 0;
      
      // 内容显示区域 - 重构后的清晰结构
      .content-display-area {
        position: relative;
        min-height: 300px; // 固定最小高度
        background: var(--bg-card, #f9fafb);
        border-radius: var(--radius-lg); // 内容区域也采用大圆角
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center; // 垂直居中
        
        // 训练记录内容
        .workout-content {
          display: flex;
          flex-direction: row;
          align-items: flex-start; // 顶部对齐
          height: 100%;
          padding: var(--space-3); // 使用标准padding
          gap: var(--space-2); // 减少间隙
          
          // 动作简要列表 (35% 宽度)
          .exercise-list {
            flex: 0 0 35%;
            display: flex;
            justify-content: center;
            align-items: center;
            
            .simplified-exercise-list {
              width: 100%;
              max-height: none; // 移除高度限制
              
              // 隐藏额外元素，保持简洁
              .exercise-item {
                display: flex;
                align-items: center;
                gap: var(--space-2);
                padding: var(--space-1-5);
                margin-bottom: var(--space-1);
                
                .exercise-image {
                  width: 55px !important;
                  height: 55px !important;
                  border-radius: 50% !important; // 确保圆形
                  background: white !important;
                  flex-shrink: 0;
                  
                  img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                  }
                }
                
                .exercise-info {
                  flex: 1;
                  display: flex;
                  flex-direction: column;
                  gap: var(--space-0-5);
                  
                  .exercise-name {
                    font-size: var(--text-sm);
                    color: var(--text-primary);
                    line-height: 1.2;
                    font-weight: var(--font-medium);
                  }
                  
                  .exercise-details {
                    display: flex;
                    align-items: center;
                    gap: var(--space-1);
                    
                    .sets-info {
                      font-size: var(--text-xs);
                      color: #9ca3af !important;
                      font-weight: var(--font-normal) !important;
                    }
                    
                    // 隐藏重量和次数信息
                    .reps-info,
                    .weight-info {
                      display: none !important;
                    }
                  }
                  
                  // 隐藏肌肉群信息
                  .muscle-groups {
                    display: none !important;
                  }
                }
                
                // 隐藏动作序号
                .exercise-index {
                  display: none !important;
                }
              }
            }
          }
          
          // 肌肉示意图 (65% 宽度)
          .static-muscle-illustration {
            flex: 0 0 80%;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%; // 充分利用可用高度
            
            .workout-muscle-viz {
              width: 100% !important; // 强制撑满宽度
              height: 100% !important; // 强制撑满高度
              max-width: none !important; // 移除最大宽度限制
              max-height: none !important; // 移除最大高度限制
            }
          }
        }
        
        // 用户上传图像内容
        .user-image-content {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 5%; // 保持5%的边距
          
          .user-uploaded-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
            border-radius: var(--radius-md);
          }
        }
        
        // 轮播导航 - 移到底部
        .carousel-navigation {
          position: absolute;
          bottom: var(--space-3);
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          gap: var(--space-1);
          align-items: center;
          z-index: 10;
          
          .nav-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            border: none;
            background: rgba(156, 163, 175, 0.5); // 半透明灰色
            cursor: pointer;
            transition: all var(--transition-normal);
            
            &.active {
              background: var(--accent-500);
              transform: scale(1.2);
            }
            
            &:hover:not(.active) {
              background: rgba(156, 163, 175, 0.8);
            }
          }
        }
      }
    }
  }
}

// 响应式设计 - 移动端优化
@media (max-width: 768px) {
  .training-record-card-wrapper {
    margin-top: var(--space-2);
    
    .training-record-card {
      // 移动端移除悬停效果
      &:hover {
        transform: none;
        box-shadow: var(--shadow-sm);
      }
      
      .training-record-body {
        .content-display-area {
          min-height: 220px; // 移动端减少高度
          
          .workout-content {
            padding: 5% var(--space-2); // 移动端减少左右padding
            gap: var(--space-1); // 移动端更小的间隙
            
            .exercise-list {
              flex: 0 0 40%; // 移动端稍微增加动作列表宽度
              
              .simplified-exercise-list {
                .exercise-item {
                  padding: var(--space-1);
                  gap: var(--space-1);
                  
                  .exercise-image {
                    width: 48px !important;
                    height: 48px !important;
                  }
                  
                  .exercise-info {
                    .exercise-name {
                      font-size: var(--text-xs);
                    }
                    
                    .exercise-details {
                      .sets-info {
                        font-size: var(--text-2xs);
                      }
                    }
                  }
                }
              }
            }
            
            .static-muscle-illustration {
              flex: 0 0 80%; // 移动端相应调整肌肉图宽度
            }
          }
          
          .carousel-navigation {
            bottom: var(--space-2);
            
            .nav-dot {
              width: 6px; // 移动端更小的导航点
              height: 6px;
            }
          }
        }
      }
    }
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .training-record-card-wrapper {
    .training-record-card {
      border-width: 2px;
      
      .training-record-body {
        .content-display-area {
          .carousel-navigation {
            .nav-dot {
              border: 2px solid var(--border-color);
              
              &.active {
                border-color: var(--accent-500);
              }
            }
          }
        }
      }
    }
  }
}

// 暗色主题适配
.theme-dark {
  .training-record-card-wrapper {
    .training-record-card {
      background: var(--card-bg-dark, #1e293b);
      border-color: var(--card-border-dark, #374151);
      
      .training-record-body {
        .content-display-area {
          background: var(--bg-card-dark, #334155);
          
          .carousel-navigation {
            .nav-dot {
              background: rgba(156, 163, 175, 0.3);
              
              &.active {
                background: var(--accent-400);
              }
              
              &:hover:not(.active) {
                background: rgba(156, 163, 175, 0.6);
              }
            }
          }
        }
      }
    }
  }
} 