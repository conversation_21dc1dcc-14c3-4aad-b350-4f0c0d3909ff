// FitnessRings Component - Apple Watch风格健身圆环
// 符合FitMaster开发规则的样式实现

@use '../../../styles/variables' as *;

.fitness-rings {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  
  // 尺寸变体
  &--sm {
    gap: var(--space-2);
    padding: var(--space-2);
  }
  
  &--md {
    gap: var(--space-4);
    padding: var(--space-4);
  }
  
  &--lg {
    gap: var(--space-6);
    padding: var(--space-6);
  }
  
  // 动画启用状态
  &--animated {
    .fitness-ring__progress {
      transition-property: stroke-dasharray, opacity;
      transition-timing-function: var(--ease-out);
    }
  }
}

.fitness-rings__container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fitness-rings__svg {
  display: block;
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
  
  // 确保在主题切换时正确显示
  .theme-dark & {
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
  }
}

// 圆环样式
.fitness-ring {
  &__background {
    opacity: 0.2;
    transition: opacity var(--transition-normal) var(--ease-in-out);
  }
  
  &__progress {
    opacity: 1;
    stroke-linecap: round;
    
    // 确保流畅的动画
    will-change: stroke-dasharray;
    transform-origin: center;
    
    // 减少动画模式支持
    @media (prefers-reduced-motion: reduce) {
      transition: none !important;
    }
  }
}

// 中心数据显示
.fitness-rings__center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.fitness-rings__stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-1);
}

.fitness-stat {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-size: var(--text-xs);
  color: var(--text-secondary);
  
  &__dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    flex-shrink: 0;
  }
  
  &__data {
    display: flex;
    align-items: baseline;
    gap: 1px;
  }
  
  &__value {
    font-weight: var(--font-semibold);
    color: var(--text-primary);
  }
  
  &__goal {
    font-size: var(--text-xs);
    opacity: 0.7;
  }
}

// 圆环说明
.fitness-rings__legend {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  flex-wrap: wrap;
  
  @include mobile {
    gap: var(--space-3);
  }
}

.fitness-legend {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--text-secondary);
  
  &__color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
  }
  
  &__label {
    font-weight: var(--font-medium);
    color: var(--text-primary);
  }
  
  &__percent {
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-left: auto;
    min-width: 32px;
    text-align: right;
  }
}

// 响应式适配 - 移动优先设计
@include mobile {
  .fitness-rings {
    &--sm {
      padding: var(--space-2);
    }
    
    &--md {
      padding: var(--space-3);
    }
    
    &--lg {
      padding: var(--space-4);
    }
  }
  
  .fitness-rings__stats {
    gap: 2px;
  }
  
  .fitness-stat {
    font-size: 10px;
    
    &__dot {
      width: 4px;
      height: 4px;
    }
  }
  
  .fitness-legend {
    font-size: var(--text-xs);
    
    &__color {
      width: 10px;
      height: 10px;
    }
  }
}

// 平板端适配
@include tablet {
  .fitness-rings__legend {
    gap: var(--space-6);
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .fitness-ring__background {
    opacity: 0.4;
    stroke-width: calc(var(--stroke-width) + 1px);
  }
  
  .fitness-ring__progress {
    stroke-width: calc(var(--stroke-width) + 1px);
  }
  
  .fitness-legend__color {
    border: 2px solid var(--text-primary);
  }
}

// 减少动画模式支持
@media (prefers-reduced-motion: reduce) {
  .fitness-rings--animated {
    .fitness-ring__progress {
      transition: none;
    }
  }
  
  .fitness-rings__svg {
    filter: none;
  }
}

// 性能优化 - GPU加速
.fitness-rings__svg {
  will-change: transform;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

// 无障碍支持
.fitness-rings {
  // 确保有足够的触摸目标大小
  min-height: 44px;
  min-width: 44px;
  
  // 为屏幕阅读器提供上下文
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    border-radius: inherit;
    z-index: -1;
  }
}

// 聚焦状态（如果组件可交互）
.fitness-rings:focus-visible {
  outline: 2px solid var(--accent-500);
  outline-offset: var(--focus-ring-offset);
}

// 打印样式优化
@media print {
  .fitness-rings__svg {
    filter: none;
  }
  
  .fitness-rings__legend {
    break-inside: avoid;
  }
} 