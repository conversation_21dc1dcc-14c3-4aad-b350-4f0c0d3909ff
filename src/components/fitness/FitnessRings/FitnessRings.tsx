import React, { useMemo } from 'react';
import './FitnessRings.scss';

// Apple Watch风格圆环配置
const APPLE_WATCH_COLORS = {
  move: '#FF6B35',      // 橙红色
  exercise: '#4CAF50',  // 绿色
  stand: '#2196F3'      // 蓝色
} as const;

// 动画配置（避免过度动画）
const RING_ANIMATION_CONFIG = {
  duration: 1.5,
  ease: 'easeOut',
} as const;

interface AppleWatchRingsProps {
  moveProgress: number;
  moveGoal: number;
  exerciseProgress: number;
  exerciseGoal: number;
  standProgress: number;
  standGoal: number;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  className?: string;
}

/**
 * Apple Watch风格健身圆环组件
 * 符合FitMaster开发规则的数据可视化组件
 */
const FitnessRings: React.FC<AppleWatchRingsProps> = ({
  moveProgress,
  moveGoal,
  exerciseProgress,
  exerciseGoal,
  standProgress,
  standGoal,
  size = 'md',
  animated = true,
  className = ''
}) => {
  // 计算进度百分比
  const movePercent = useMemo(() => 
    Math.min((moveProgress / moveGoal) * 100, 100), 
    [moveProgress, moveGoal]
  );
  
  const exercisePercent = useMemo(() => 
    Math.min((exerciseProgress / exerciseGoal) * 100, 100), 
    [exerciseProgress, exerciseGoal]
  );
  
  const standPercent = useMemo(() => 
    Math.min((standProgress / standGoal) * 100, 100), 
    [standProgress, standGoal]
  );

  // 尺寸配置
  const sizeConfig = {
    sm: { size: 120, strokeWidth: 8, gap: 4 },
    md: { size: 160, strokeWidth: 10, gap: 6 },
    lg: { size: 200, strokeWidth: 12, gap: 8 }
  };

  const config = sizeConfig[size];
  const radius = (config.size - config.strokeWidth) / 2;
  const center = config.size / 2;
  const circumference = 2 * Math.PI * radius;

  // 生成SVG路径
  const generateStrokeDasharray = (percent: number) => {
    const progress = (percent / 100) * circumference;
    return `${progress} ${circumference - progress}`;
  };

  const rings = [
    {
      id: 'move',
      color: APPLE_WATCH_COLORS.move,
      percent: movePercent,
      progress: moveProgress,
      goal: moveGoal,
      label: '活动',
      radius: radius - config.gap * 2,
    },
    {
      id: 'exercise',
      color: APPLE_WATCH_COLORS.exercise,
      percent: exercisePercent,
      progress: exerciseProgress,
      goal: exerciseGoal,
      label: '锻炼',
      radius: radius - config.gap,
    },
    {
      id: 'stand',
      color: APPLE_WATCH_COLORS.stand,
      percent: standPercent,
      progress: standProgress,
      goal: standGoal,
      label: '站立',
      radius: radius,
    },
  ];

  return (
    <div className={`fitness-rings fitness-rings--${size} ${animated ? 'fitness-rings--animated' : ''} ${className}`}>
      <div className="fitness-rings__container">
        <svg 
          width={config.size} 
          height={config.size} 
          className="fitness-rings__svg"
        >
          {rings.map(ring => (
            <g key={ring.id} className={`fitness-ring fitness-ring--${ring.id}`}>
              {/* 背景圆环 */}
              <circle
                cx={center}
                cy={center}
                r={ring.radius}
                className="fitness-ring__background"
                strokeWidth={config.strokeWidth}
                fill="none"
                stroke="var(--progress-bg)"
              />
              
              {/* 进度圆环 */}
              <circle
                cx={center}
                cy={center}
                r={ring.radius}
                className="fitness-ring__progress"
                strokeWidth={config.strokeWidth}
                fill="none"
                stroke={ring.color}
                strokeLinecap="round"
                strokeDasharray={generateStrokeDasharray(ring.percent)}
                strokeDashoffset={0}
                transform={`rotate(-90 ${center} ${center})`}
                style={{
                  transition: animated 
                    ? `stroke-dasharray ${RING_ANIMATION_CONFIG.duration}s ${RING_ANIMATION_CONFIG.ease}` 
                    : 'none'
                }}
              />
            </g>
          ))}
        </svg>

        {/* 中心数据显示 */}
        <div className="fitness-rings__center">
          <div className="fitness-rings__stats">
            {rings.map(ring => (
              <div key={ring.id} className={`fitness-stat fitness-stat--${ring.id}`}>
                <div 
                  className="fitness-stat__dot" 
                  style={{ backgroundColor: ring.color }}
                />
                <div className="fitness-stat__data">
                  <span className="fitness-stat__value">
                    {ring.progress}
                  </span>
                  <span className="fitness-stat__goal">
                    /{ring.goal}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 圆环说明 */}
      <div className="fitness-rings__legend">
        {rings.map(ring => (
          <div key={ring.id} className="fitness-legend">
            <div 
              className="fitness-legend__color" 
              style={{ backgroundColor: ring.color }}
            />
            <span className="fitness-legend__label">{ring.label}</span>
            <span className="fitness-legend__percent">
              {Math.round(ring.percent)}%
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export type { AppleWatchRingsProps };
export default FitnessRings; 