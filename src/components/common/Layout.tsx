import React, { useState, useEffect } from 'react';
import Sidebar from '../navigation/Sidebar';
import BottomNavigation from '../navigation/mobile/BottomNavigation';
import HeaderActions from './HeaderActions';
import { useUnifiedSystem } from '../../hooks';
import './Layout.scss';

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  showSidebar?: boolean;
  showHeader?: boolean;
  headerType?: 'default' | 'dashboard';
  dashboardDate?: Date;
  onNotificationPress?: () => void;
  onSettingsPress?: () => void;
}

const Layout: React.FC<LayoutProps> = ({ 
  children, 
  title = 'FitMaster', 
  showSidebar = true,
  showHeader = true,
  headerType = 'default',
  dashboardDate,
  onNotificationPress,
  onSettingsPress
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);

  // 🚀 使用统一的iOS主题与布局系统
  const system = useUnifiedSystem();

  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // 📱 iOS布局系统集成（使用统一修复方案）
  useEffect(() => {
    if (isMobile && system.layoutReady) {
      // 添加统一布局修复标记
      document.body.classList.add('ios-layout-unified-initialized');
      
      // 开发环境调试模式
      if (process.env.NODE_ENV === 'development') {
        console.log('🎯 Layout组件使用统一iOS修复方案:', system.getSystemState());
        console.log('🔧 z-index层级: 状态栏遮罩(990) < header(1030) < 其他UI元素');
        // document.body.classList.add('debug-ios-unified-layout'); // 取消注释以启用调试
      }
    }
    
    return () => {
      document.body.classList.remove('ios-layout-unified-initialized', 'debug-ios-unified-layout');
    };
  }, [isMobile, system.layoutReady, system]);

  const formatDate = (date: Date): string => {
    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();
    
    if (isToday) {
      return '今天';
    }
    
    return date.toLocaleDateString('zh-CN', { 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const getDetailedDate = (date: Date): string => {
    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();
    
    if (isToday) {
      return date.toLocaleDateString('zh-CN', { 
        year: 'numeric',
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
      });
    }
    
    return date.toLocaleDateString('zh-CN', { 
      year: 'numeric',
      month: 'long', 
      day: 'numeric',
      weekday: 'long'
    });
  };

  const renderHeaderContent = () => {
    if (headerType === 'dashboard' && dashboardDate) {
      return (
        <div className="dashboard-header-content">
          <div className="dashboard-date-info">
            <h1 className="dashboard-title">
              📅 {formatDate(dashboardDate)}
            </h1>
            <p className="dashboard-subtitle">
              {getDetailedDate(dashboardDate)}
            </p>
          </div>
          
          <HeaderActions
            onNotificationPress={onNotificationPress}
            onSettingsPress={onSettingsPress}
          />
        </div>
      );
    }

    // 统一所有页面使用相同的布局结构
    return (
      <div className="dashboard-header-content">
        <div className="dashboard-date-info">
          <h1 className="dashboard-title">{title}</h1>
        </div>
        
        <HeaderActions
          onNotificationPress={onNotificationPress}
          onSettingsPress={onSettingsPress}
        />
      </div>
    );
  };

  return (
    <div className={`layout ${isMobile ? 'mobile-layout' : isTablet ? 'tablet-layout' : 'desktop-layout'} theme-${system.theme}`}>
      {/* 🍎 iOS状态栏现在直接由header处理，无需单独的集成层 */}
      
      {showSidebar && !isMobile && <Sidebar />}
      
      <main className={`main-content ${showSidebar && !isMobile ? 'with-sidebar' : 'full-width'} ${isMobile ? 'with-bottom-nav' : ''}`}>
        {showHeader && (
          <header className={`page-header ${headerType === 'dashboard' ? 'dashboard-header' : ''}`}>
            {renderHeaderContent()}
          </header>
        )}

        {/* 📄 纯滚动内容区域 */}
        <div className={`page-content ${!showHeader ? 'no-header' : ''}`}>
          {children}
        </div>
      </main>
      
      {isMobile && showSidebar && <BottomNavigation />}
    </div>
  );
};

export default Layout; 