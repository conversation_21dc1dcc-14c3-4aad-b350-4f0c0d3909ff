/**
 * 网络诊断组件
 * 为用户提供网络连接状态的可视化诊断界面
 */

import React from 'react';
import { useNetworkDiagnostics } from '../../../hooks/useNetworkDiagnostics';
import './NetworkDiagnostics.scss';

interface NetworkDiagnosticsProps {
  showDetails?: boolean;
  onDiagnosticsComplete?: (success: boolean) => void;
  className?: string;
}

export const NetworkDiagnostics: React.FC<NetworkDiagnosticsProps> = ({
  showDetails = true,
  onDiagnosticsComplete,
  className = ''
}) => {
  const {
    diagnostics,
    isRunningDiagnostics,
    runDiagnostics,
    quickConnectionTest
  } = useNetworkDiagnostics();

  // 获取状态图标
  const getStatusIcon = (success: boolean, loading = false) => {
    if (loading) return '⏳';
    return success ? '✅' : '❌';
  };

  // 获取状态样式类
  const getStatusClass = (success: boolean) => {
    return success ? 'status-success' : 'status-error';
  };

  // 运行快速测试
  const handleQuickTest = async () => {
    const success = await quickConnectionTest();
    onDiagnosticsComplete?.(success);
  };

  // 运行完整诊断
  const handleFullDiagnostics = async () => {
    await runDiagnostics();
    const success = diagnostics.serverReachable && diagnostics.isOnline;
    onDiagnosticsComplete?.(success);
  };

  return (
    <div className={`network-diagnostics ${className}`}>
      {/* 标题栏 */}
      <div className="diagnostics-header">
        <h3>🔍 网络诊断</h3>
        <div className="diagnostics-actions">
          <button 
            className="btn btn-secondary btn-sm"
            onClick={handleQuickTest}
            disabled={isRunningDiagnostics}
          >
            快速测试
          </button>
          <button 
            className="btn btn-primary btn-sm"
            onClick={handleFullDiagnostics}
            disabled={isRunningDiagnostics}
          >
            {isRunningDiagnostics ? '诊断中...' : '完整诊断'}
          </button>
        </div>
      </div>

      {/* 基本状态 */}
      <div className="diagnostics-summary">
        <div className={`status-item ${getStatusClass(diagnostics.isOnline)}`}>
          <span className="status-icon">
            {getStatusIcon(diagnostics.isOnline, isRunningDiagnostics)}
          </span>
          <span className="status-text">
            网络连接: {diagnostics.isOnline ? '正常' : '断开'}
          </span>
        </div>

        <div className={`status-item ${getStatusClass(diagnostics.serverReachable)}`}>
          <span className="status-icon">
            {getStatusIcon(diagnostics.serverReachable, isRunningDiagnostics)}
          </span>
          <span className="status-text">
            服务器连接: {diagnostics.serverReachable ? '正常' : '失败'}
          </span>
        </div>

        {diagnostics.platform === 'ios' && (
          <div className={`status-item ${getStatusClass(diagnostics.httpAllowed)}`}>
            <span className="status-icon">
              {getStatusIcon(diagnostics.httpAllowed, isRunningDiagnostics)}
            </span>
            <span className="status-text">
              iOS HTTP支持: {diagnostics.httpAllowed ? '已配置' : '受限'}
            </span>
          </div>
        )}
      </div>

      {/* 平台信息 */}
      {showDetails && (
        <div className="platform-info">
          <h4>📱 平台信息</h4>
          <div className="info-grid">
            <div className="info-item">
              <span className="info-label">平台:</span>
              <span className="info-value">{diagnostics.platform}</span>
            </div>
            <div className="info-item">
              <span className="info-label">运行环境:</span>
              <span className="info-value">
                {diagnostics.isNative ? '原生应用' : 'Web浏览器'}
              </span>
            </div>
            <div className="info-item">
              <span className="info-label">连接类型:</span>
              <span className="info-value">{diagnostics.connectionType}</span>
            </div>
          </div>
        </div>
      )}

      {/* 错误信息 */}
      {diagnostics.lastError && (
        <div className="error-section">
          <h4>❌ 最后错误</h4>
          <div className="error-message">
            {diagnostics.lastError}
          </div>
        </div>
      )}

      {/* 诊断消息 */}
      {showDetails && diagnostics.diagnosticMessages.length > 0 && (
        <div className="diagnostics-messages">
          <h4>📋 诊断详情</h4>
          <div className="messages-list">
            {diagnostics.diagnosticMessages.map((message, index) => (
              <div key={index} className="message-item">
                {message}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 修复建议 */}
      {diagnostics.suggestions.length > 0 && (
        <div className="suggestions-section">
          <h4>💡 修复建议</h4>
          <div className="suggestions-list">
            {diagnostics.suggestions.map((suggestion, index) => (
              <div key={index} className="suggestion-item">
                <span className="suggestion-icon">💡</span>
                <span className="suggestion-text">{suggestion}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* iOS特定提示 */}
      {diagnostics.platform === 'ios' && !diagnostics.httpAllowed && (
        <div className="ios-specific-help">
          <h4>🍎 iOS配置指导</h4>
          <div className="ios-help-content">
            <p>检测到您正在使用iOS设备访问HTTP服务器。请确保以下配置：</p>
            <div className="config-steps">
              <div className="config-step">
                <span className="step-number">1</span>
                <div className="step-content">
                  <strong>Info.plist配置</strong>
                  <p>在iOS项目的Info.plist中添加App Transport Security配置</p>
                </div>
              </div>
              <div className="config-step">
                <span className="step-number">2</span>
                <div className="step-content">
                  <strong>重新构建应用</strong>
                  <p>运行 npx cap sync ios 后重新构建应用</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 快速修复按钮 */}
      {!diagnostics.serverReachable && !isRunningDiagnostics && (
        <div className="quick-actions">
          <button 
            className="btn btn-warning"
            onClick={handleFullDiagnostics}
          >
            🔄 重新检测
          </button>
        </div>
      )}
    </div>
  );
}; 