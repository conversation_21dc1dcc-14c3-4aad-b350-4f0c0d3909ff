/**
 * 网络诊断组件样式
 * iOS风格的诊断界面设计
 */

.network-diagnostics {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  padding: var(--space-4);
  margin: var(--space-4) 0;
  
  // 诊断标题栏
  .diagnostics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 1px solid var(--border-color);
    
    h3 {
      margin: 0;
      color: var(--text-primary);
      font-size: var(--font-size-lg);
      font-weight: 600;
    }
    
    .diagnostics-actions {
      display: flex;
      gap: var(--space-2);
      
      .btn {
        font-size: var(--font-size-sm);
        padding: var(--space-2) var(--space-3);
        border-radius: var(--border-radius-md);
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        
        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
        
        &.btn-primary {
          background: var(--primary-color);
          color: white;
          
          &:hover:not(:disabled) {
            background: var(--primary-hover);
          }
        }
        
        &.btn-secondary {
          background: var(--bg-secondary);
          color: var(--text-primary);
          border: 1px solid var(--border-color);
          
          &:hover:not(:disabled) {
            background: var(--bg-tertiary);
          }
        }
        
        &.btn-warning {
          background: var(--warning-color);
          color: white;
          
          &:hover:not(:disabled) {
            background: var(--warning-hover);
          }
        }
      }
    }
  }
  
  // 状态摘要
  .diagnostics-summary {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
    
    .status-item {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      padding: var(--space-3);
      border-radius: var(--border-radius-md);
      border: 1px solid transparent;
      
      .status-icon {
        font-size: var(--font-size-lg);
        flex-shrink: 0;
      }
      
      .status-text {
        font-weight: 500;
        color: var(--text-primary);
      }
      
      &.status-success {
        background: rgba(34, 197, 94, 0.1);
        border-color: rgba(34, 197, 94, 0.3);
        
        .status-text {
          color: var(--success-color);
        }
      }
      
      &.status-error {
        background: rgba(239, 68, 68, 0.1);
        border-color: rgba(239, 68, 68, 0.3);
        
        .status-text {
          color: var(--error-color);
        }
      }
    }
  }
  
  // 平台信息
  .platform-info {
    margin-bottom: var(--space-4);
    
    h4 {
      margin: 0 0 var(--space-3) 0;
      color: var(--text-primary);
      font-size: var(--font-size-md);
      font-weight: 600;
    }
    
    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--space-3);
      
      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--space-2) var(--space-3);
        background: var(--bg-secondary);
        border-radius: var(--border-radius-sm);
        
        .info-label {
          color: var(--text-secondary);
          font-size: var(--font-size-sm);
        }
        
        .info-value {
          color: var(--text-primary);
          font-weight: 500;
          font-size: var(--font-size-sm);
        }
      }
    }
  }
  
  // 错误信息
  .error-section {
    margin-bottom: var(--space-4);
    
    h4 {
      margin: 0 0 var(--space-3) 0;
      color: var(--error-color);
      font-size: var(--font-size-md);
      font-weight: 600;
    }
    
    .error-message {
      padding: var(--space-3);
      background: rgba(239, 68, 68, 0.1);
      border: 1px solid rgba(239, 68, 68, 0.3);
      border-radius: var(--border-radius-md);
      color: var(--error-color);
      font-family: var(--font-mono);
      font-size: var(--font-size-sm);
      line-height: 1.5;
    }
  }
  
  // 诊断消息
  .diagnostics-messages {
    margin-bottom: var(--space-4);
    
    h4 {
      margin: 0 0 var(--space-3) 0;
      color: var(--text-primary);
      font-size: var(--font-size-md);
      font-weight: 600;
    }
    
    .messages-list {
      display: flex;
      flex-direction: column;
      gap: var(--space-2);
      
      .message-item {
        padding: var(--space-2) var(--space-3);
        background: var(--bg-secondary);
        border-radius: var(--border-radius-sm);
        color: var(--text-primary);
        font-size: var(--font-size-sm);
        line-height: 1.4;
      }
    }
  }
  
  // 修复建议
  .suggestions-section {
    margin-bottom: var(--space-4);
    
    h4 {
      margin: 0 0 var(--space-3) 0;
      color: var(--warning-color);
      font-size: var(--font-size-md);
      font-weight: 600;
    }
    
    .suggestions-list {
      display: flex;
      flex-direction: column;
      gap: var(--space-2);
      
      .suggestion-item {
        display: flex;
        align-items: flex-start;
        gap: var(--space-2);
        padding: var(--space-3);
        background: rgba(245, 158, 11, 0.1);
        border: 1px solid rgba(245, 158, 11, 0.3);
        border-radius: var(--border-radius-md);
        
        .suggestion-icon {
          flex-shrink: 0;
          font-size: var(--font-size-sm);
        }
        
        .suggestion-text {
          color: var(--text-primary);
          font-size: var(--font-size-sm);
          line-height: 1.5;
        }
      }
    }
  }
  
  // iOS特定帮助
  .ios-specific-help {
    margin-bottom: var(--space-4);
    padding: var(--space-4);
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: var(--border-radius-lg);
    
    h4 {
      margin: 0 0 var(--space-3) 0;
      color: var(--primary-color);
      font-size: var(--font-size-md);
      font-weight: 600;
    }
    
    .ios-help-content {
      p {
        margin: 0 0 var(--space-3) 0;
        color: var(--text-primary);
        font-size: var(--font-size-sm);
        line-height: 1.5;
      }
      
      .config-steps {
        display: flex;
        flex-direction: column;
        gap: var(--space-3);
        
        .config-step {
          display: flex;
          gap: var(--space-3);
          
          .step-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            font-size: var(--font-size-xs);
            font-weight: 600;
            flex-shrink: 0;
          }
          
          .step-content {
            flex: 1;
            
            strong {
              display: block;
              color: var(--text-primary);
              font-size: var(--font-size-sm);
              margin-bottom: var(--space-1);
            }
            
            p {
              margin: 0;
              color: var(--text-secondary);
              font-size: var(--font-size-xs);
              line-height: 1.4;
            }
          }
        }
      }
    }
  }
  
  // 快速操作
  .quick-actions {
    display: flex;
    justify-content: center;
    gap: var(--space-3);
    padding-top: var(--space-3);
    border-top: 1px solid var(--border-color);
    
    .btn {
      padding: var(--space-3) var(--space-4);
      border-radius: var(--border-radius-md);
      border: none;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.2s ease;
      
      &:hover {
        transform: translateY(-1px);
      }
    }
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    padding: var(--space-3);
    
    .diagnostics-header {
      flex-direction: column;
      align-items: stretch;
      gap: var(--space-3);
      
      .diagnostics-actions {
        justify-content: center;
      }
    }
    
    .platform-info .info-grid {
      grid-template-columns: 1fr;
    }
    
    .ios-specific-help {
      padding: var(--space-3);
      
      .config-steps .config-step {
        flex-direction: column;
        align-items: flex-start;
        
        .step-number {
          align-self: flex-start;
        }
      }
    }
  }
  
  // 暗色主题适配
  .theme-dark & {
    background: var(--bg-primary);
    border-color: var(--border-dark);
    
    .status-item {
      &.status-success {
        background: rgba(34, 197, 94, 0.15);
        border-color: rgba(34, 197, 94, 0.4);
      }
      
      &.status-error {
        background: rgba(239, 68, 68, 0.15);
        border-color: rgba(239, 68, 68, 0.4);
      }
    }
    
    .error-message {
      background: rgba(239, 68, 68, 0.15);
      border-color: rgba(239, 68, 68, 0.4);
    }
    
    .suggestion-item {
      background: rgba(245, 158, 11, 0.15);
      border-color: rgba(245, 158, 11, 0.4);
    }
    
    .ios-specific-help {
      background: rgba(59, 130, 246, 0.15);
      border-color: rgba(59, 130, 246, 0.4);
    }
  }
} 