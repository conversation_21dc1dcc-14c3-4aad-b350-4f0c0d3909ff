import React from 'react';
import './CustomHeroUI.scss';

// 自定义Avatar组件
interface CustomAvatarProps {
  src?: string;
  alt?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const CustomAvatar: React.FC<CustomAvatarProps> = ({
  src,
  alt = '',
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'custom-avatar-sm',
    md: 'custom-avatar-md',
    lg: 'custom-avatar-lg'
  };

  return (
    <div className={`custom-avatar ${sizeClasses[size]} ${className}`}>
      {src ? (
        <img src={src} alt={alt} />
      ) : (
        <div className="custom-avatar-placeholder">
          {alt.charAt(0).toUpperCase()}
        </div>
      )}
    </div>
  );
};

// 自定义Button组件
interface CustomButtonProps {
  children: React.ReactNode;
  variant?: 'solid' | 'bordered' | 'light';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
}

export const CustomButton: React.FC<CustomButtonProps> = ({
  children,
  variant = 'solid',
  size = 'md',
  className = '',
  onClick,
  disabled = false
}) => {
  const variantClasses = {
    solid: 'custom-button-solid',
    bordered: 'custom-button-bordered',
    light: 'custom-button-light'
  };

  const sizeClasses = {
    sm: 'custom-button-sm',
    md: 'custom-button-md',
    lg: 'custom-button-lg'
  };

  return (
    <button
      className={`custom-button ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

// 自定义Divider组件
interface CustomDividerProps {
  orientation?: 'horizontal' | 'vertical';
  className?: string;
  style?: React.CSSProperties;
}

export const CustomDivider: React.FC<CustomDividerProps> = ({
  orientation = 'horizontal',
  className = '',
  style
}) => {
  return (
    <div
      className={`custom-divider custom-divider-${orientation} ${className}`}
      style={style}
    />
  );
}; 