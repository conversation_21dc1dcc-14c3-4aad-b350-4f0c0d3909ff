@use '../../../styles/variables' as *;

.btn {
  // 基础样式
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  
  font-family: inherit;
  font-weight: 600;
  text-decoration: none;
  white-space: nowrap;
  
  border: none;
  border-radius: 8px;
  cursor: pointer;
  user-select: none;
  
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.3);
  }

  &:active {
    transform: translateY(1px);
  }

  // 尺寸变体
  &--sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    min-height: 32px;
    
    .btn__icon {
      width: 16px;
      height: 16px;
    }
  }

  &--md {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    min-height: 40px;
    
    .btn__icon {
      width: 18px;
      height: 18px;
    }
  }

  &--lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    min-height: 48px;
    
    .btn__icon {
      width: 20px;
      height: 20px;
    }
  }

  // 形状变体
  &--round {
    border-radius: 9999px;
  }

  &--circle {
    aspect-ratio: 1;
    border-radius: 50%;
    padding: 0;
    
    &.btn--sm {
      width: 32px;
      height: 32px;
    }
    
    &.btn--md {
      width: 40px;
      height: 40px;
    }
    
    &.btn--lg {
      width: 48px;
      height: 48px;
    }
  }

  // 全宽
  &--full-width {
    width: 100%;
  }

  // 颜色变体
  &--primary {
    background: linear-gradient(135deg, #4fc3f7, #29b6f6);
    color: white;
    box-shadow: 0 2px 8px rgba(79, 195, 247, 0.3);
    
    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #29b6f6, #0288d1);
      box-shadow: 0 4px 16px rgba(79, 195, 247, 0.4);
      transform: translateY(-2px);
    }
    
    &:active {
      background: linear-gradient(135deg, #0288d1, #0277bd);
      transform: translateY(0);
    }
  }

  &--secondary {
    background: var(--color-background-secondary);
    color: var(--color-text-primary);
    border: 1px solid rgba(79, 195, 247, 0.3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    &:hover:not(:disabled) {
      background: rgba(79, 195, 247, 0.1);
      border-color: var(--color-primary);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }
    
    &:active {
      background: rgba(79, 195, 247, 0.2);
      transform: translateY(0);
    }
  }

  &--ghost {
    background: transparent;
    color: var(--color-primary);
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background: rgba(79, 195, 247, 0.1);
      border-color: rgba(79, 195, 247, 0.3);
    }
    
    &:active {
      background: rgba(79, 195, 247, 0.2);
    }
  }

  &--danger {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    color: white;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
    
    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #d32f2f, #c62828);
      box-shadow: 0 4px 16px rgba(244, 67, 54, 0.4);
      transform: translateY(-2px);
    }
    
    &:active {
      background: linear-gradient(135deg, #c62828, #b71c1c);
      transform: translateY(0);
    }
  }

  &--success {
    background: linear-gradient(135deg, #66bb6a, #4caf50);
    color: white;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
    
    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #4caf50, #388e3c);
      box-shadow: 0 4px 16px rgba(76, 175, 80, 0.4);
      transform: translateY(-2px);
    }
    
    &:active {
      background: linear-gradient(135deg, #388e3c, #2e7d32);
      transform: translateY(0);
    }
  }

  // 禁用状态
  &--disabled,
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
    
    &:hover {
      transform: none !important;
      box-shadow: none !important;
    }
  }

  // 加载状态
  &--loading {
    cursor: wait;
    
    .btn__text {
      opacity: 0.7;
    }
  }

  // 图标样式
  .btn__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    
    svg {
      width: 100%;
      height: 100%;
      stroke-width: 2;
    }
    
    &--left {
      margin-right: -0.25rem;
    }
    
    &--right {
      margin-left: -0.25rem;
    }
  }

  // 加载动画
  .btn__spinner {
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
    
    path:last-child {
      opacity: 0.3;
    }
  }

  // 文本样式
  .btn__text {
    flex: 1;
    text-align: center;
  }
}

// 加载动画关键帧
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式适配
@include mobile {
  .btn {
    &--sm {
      padding: 0.5rem 0.75rem;
      font-size: 0.8rem;
      min-height: 28px;
    }
    
    &--md {
      padding: 0.75rem 1.25rem;
      font-size: 0.9rem;
      min-height: 36px;
    }
    
    &--lg {
      padding: 1rem 1.75rem;
      font-size: 1rem;
      min-height: 44px;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .btn {
    border-width: 2px;
    
    &--primary {
      border: 2px solid #0288d1;
    }
    
    &--secondary {
      border-width: 2px;
    }
    
    &--ghost {
      border: 2px solid var(--color-primary);
    }
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
    
    &:hover {
      transform: none;
    }
    
    &:active {
      transform: none;
    }
  }
  
  .btn__spinner {
    animation: none;
  }
} 