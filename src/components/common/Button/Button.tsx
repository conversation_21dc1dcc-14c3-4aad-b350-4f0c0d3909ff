import React, { forwardRef, ButtonHTMLAttributes } from 'react';
import './Button.scss';

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  /** 按钮变体 */
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger' | 'success';
  /** 按钮尺寸 */
  size?: 'sm' | 'md' | 'lg';
  /** 是否全宽 */
  fullWidth?: boolean;
  /** 是否正在加载 */
  loading?: boolean;
  /** 图标位置 */
  iconPosition?: 'left' | 'right';
  /** 左侧图标 */
  leftIcon?: React.ReactNode;
  /** 右侧图标 */
  rightIcon?: React.ReactNode;
  /** 按钮形状 */
  shape?: 'default' | 'round' | 'circle';
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      variant = 'primary',
      size = 'md',
      fullWidth = false,
      loading = false,
      disabled = false,
      iconPosition = 'left',
      leftIcon,
      rightIcon,
      shape = 'default',
      className = '',
      ...props
    },
    ref
  ) => {
    const classes = [
      'btn',
      `btn--${variant}`,
      `btn--${size}`,
      `btn--${shape}`,
      fullWidth && 'btn--full-width',
      loading && 'btn--loading',
      disabled && 'btn--disabled',
      className
    ]
      .filter(Boolean)
      .join(' ');

    const LoadingSpinner = () => (
      <svg
        className="btn__spinner"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
      >
        <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" />
        <path d="M3 12C3 7.02944 7.02944 3 12 3" strokeLinecap="round" />
      </svg>
    );

    const renderContent = () => {
      if (loading) {
        return (
          <>
            <LoadingSpinner />
            {children && <span className="btn__text">{children}</span>}
          </>
        );
      }

      if (shape === 'circle') {
        return leftIcon || rightIcon || children;
      }

      return (
        <>
          {leftIcon && !loading && (
            <span className="btn__icon btn__icon--left">{leftIcon}</span>
          )}
          {children && <span className="btn__text">{children}</span>}
          {rightIcon && !loading && (
            <span className="btn__icon btn__icon--right">{rightIcon}</span>
          )}
        </>
      );
    };

    return (
      <button
        ref={ref}
        className={classes}
        disabled={disabled || loading}
        {...props}
      >
        {renderContent()}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button; 