@use '../../../styles/variables' as *;

.card {
  // 基础样式
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  // 变体样式
  &--default {
    background: var(--color-background-secondary);
    border: 1px solid rgba(79, 195, 247, 0.1);
  }

  &--outlined {
    background: transparent;
    border: 1px solid rgba(79, 195, 247, 0.3);
  }

  &--elevated {
    background: var(--color-background-secondary);
    border: 1px solid rgba(79, 195, 247, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  &--ghost {
    background: rgba(79, 195, 247, 0.05);
    border: 1px solid transparent;
  }

  // 尺寸变体
  &--sm {
    font-size: 0.875rem;
  }

  &--md {
    font-size: 1rem;
  }

  &--lg {
    font-size: 1.125rem;
  }

  // 边框半径
  &--radius-sm {
    border-radius: 4px;
  }

  &--radius-md {
    border-radius: 8px;
  }

  &--radius-lg {
    border-radius: 12px;
  }

  &--radius-xl {
    border-radius: 16px;
  }

  // 内边距
  &--padding-none {
    padding: 0;
  }

  &--padding-sm {
    padding: 0.75rem;
  }

  &--padding-md {
    padding: 1rem;
  }

  &--padding-lg {
    padding: 1.5rem;
  }

  // 全宽
  &--full-width {
    width: 100%;
  }

  // 可点击
  &--clickable {
    cursor: pointer;
    user-select: none;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    &:active {
      transform: translateY(0);
    }
  }

  // 可悬浮
  &--hoverable {
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      border-color: var(--color-primary);
    }
  }

  // 头部样式
  &__header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 1rem;

    &:last-child {
      margin-bottom: 0;
    }

    &-content {
      flex: 1;
      min-width: 0;
    }
  }

  &__title {
    color: var(--color-text-primary);
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    line-height: 1.4;
  }

  &__subtitle {
    color: var(--color-text-secondary);
    font-size: 0.875rem;
    margin: 0;
    line-height: 1.4;
    opacity: 0.8;
  }

  &__action {
    margin-left: 1rem;
    flex-shrink: 0;
  }

  // 内容样式
  &__content {
    flex: 1;
    color: var(--color-text-primary);
    line-height: 1.6;

    &:not(:last-child) {
      margin-bottom: 1rem;
    }

    // 内容中的段落
    p {
      margin: 0 0 0.75rem 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    // 内容中的列表
    ul, ol {
      margin: 0 0 0.75rem 0;
      padding-left: 1.5rem;

      &:last-child {
        margin-bottom: 0;
      }
    }

    li {
      margin-bottom: 0.25rem;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 底部样式
  &__footer {
    margin-top: auto;
    color: var(--color-text-secondary);
    font-size: 0.875rem;

    &--divider {
      padding-top: 1rem;
      border-top: 1px solid rgba(79, 195, 247, 0.1);
    }

    // 底部按钮组
    .btn + .btn {
      margin-left: 0.5rem;
    }
  }

  // 组合使用时的样式调整
  &--padding-none {
    .card__header,
    .card__content,
    .card__footer {
      padding: 1rem;
    }

    .card__footer--divider {
      padding-top: 1rem;
      margin-top: 1rem;
    }
  }
}

// 响应式适配
@include tablet {
  .card {
    &--padding-lg {
      padding: 1.25rem;
    }

    &__title {
      font-size: 1.125rem;
    }
  }
}

@include mobile {
  .card {
    &--padding-sm {
      padding: 0.5rem;
    }

    &--padding-md {
      padding: 0.75rem;
    }

    &--padding-lg {
      padding: 1rem;
    }

    &__title {
      font-size: 1rem;
    }

    &__subtitle {
      font-size: 0.8rem;
    }

    &__action {
      margin-left: 0.5rem;
    }

    &__header {
      margin-bottom: 0.75rem;
    }

    &__content:not(:last-child) {
      margin-bottom: 0.75rem;
    }

    &__footer--divider {
      padding-top: 0.75rem;
      margin-top: 0.75rem;
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .card {
    &--elevated {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }

    &--clickable:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    }

    &--hoverable:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .card {
    border-width: 2px;

    &--outlined {
      border-width: 2px;
    }

    &__footer--divider {
      border-top-width: 2px;
    }
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  .card {
    transition: none;

    &--clickable:hover,
    &--hoverable:hover {
      transform: none;
    }

    &--clickable:active {
      transform: none;
    }
  }
} 