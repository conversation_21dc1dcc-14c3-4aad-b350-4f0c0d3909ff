import React, { HTMLAttributes } from 'react';
import './Card.scss';

export interface CardProps extends HTMLAttributes<HTMLDivElement> {
  /** 卡片变体 */
  variant?: 'default' | 'outlined' | 'elevated' | 'ghost';
  /** 卡片尺寸 */
  size?: 'sm' | 'md' | 'lg';
  /** 是否可点击 */
  clickable?: boolean;
  /** 是否有悬浮效果 */
  hoverable?: boolean;
  /** 边框半径 */
  radius?: 'sm' | 'md' | 'lg' | 'xl';
  /** 内边距 */
  padding?: 'none' | 'sm' | 'md' | 'lg';
  /** 是否全宽 */
  fullWidth?: boolean;
}

interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
  /** 标题 */
  title?: string;
  /** 副标题 */
  subtitle?: string;
  /** 右侧动作区域 */
  action?: React.ReactNode;
}

interface CardContentProps extends HTMLAttributes<HTMLDivElement> {}

interface CardFooterProps extends HTMLAttributes<HTMLDivElement> {
  /** 是否有分割线 */
  divider?: boolean;
}

const Card: React.FC<CardProps> & {
  Header: React.FC<CardHeaderProps>;
  Content: React.FC<CardContentProps>;
  Footer: React.FC<CardFooterProps>;
} = ({
  children,
  variant = 'default',
  size = 'md',
  clickable = false,
  hoverable = false,
  radius = 'md',
  padding = 'md',
  fullWidth = false,
  className = '',
  ...props
}) => {
  const classes = [
    'card',
    `card--${variant}`,
    `card--${size}`,
    `card--radius-${radius}`,
    `card--padding-${padding}`,
    clickable && 'card--clickable',
    hoverable && 'card--hoverable',
    fullWidth && 'card--full-width',
    className
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
};

const CardHeader: React.FC<CardHeaderProps> = ({
  title,
  subtitle,
  action,
  children,
  className = '',
  ...props
}) => {
  return (
    <div className={`card__header ${className}`} {...props}>
      <div className="card__header-content">
        {title && <h3 className="card__title">{title}</h3>}
        {subtitle && <p className="card__subtitle">{subtitle}</p>}
        {children}
      </div>
      {action && <div className="card__action">{action}</div>}
    </div>
  );
};

const CardContent: React.FC<CardContentProps> = ({
  children,
  className = '',
  ...props
}) => {
  return (
    <div className={`card__content ${className}`} {...props}>
      {children}
    </div>
  );
};

const CardFooter: React.FC<CardFooterProps> = ({
  children,
  divider = false,
  className = '',
  ...props
}) => {
  const classes = [
    'card__footer',
    divider && 'card__footer--divider',
    className
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
};

Card.Header = CardHeader;
Card.Content = CardContent;
Card.Footer = CardFooter;

export default Card; 