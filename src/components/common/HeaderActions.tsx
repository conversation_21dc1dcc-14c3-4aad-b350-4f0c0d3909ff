import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import Icon from './Icon';
import './HeaderActions.scss';

interface HeaderActionsProps {
  onNotificationPress?: () => void;
  onSettingsPress?: () => void;
}

const HeaderActions: React.FC<HeaderActionsProps> = ({
  onNotificationPress,
  onSettingsPress
}) => {
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  const getThemeIcon = () => {
    return theme === 'light' ? 'sun' : 'moon';
  };

  return (
    <div className="header-actions">
      <button 
        className="header-action-btn theme-btn"
        onClick={toggleTheme}
        aria-label={`当前: ${theme === 'light' ? '明亮模式' : '暗黑模式'}, 点击切换主题`}
        title={theme === 'light' ? '切换到暗黑模式' : '切换到明亮模式'}
      >
        <Icon name={getThemeIcon()} size="small" />
      </button>
      
      <button 
        className="header-action-btn notification-btn"
        onClick={onNotificationPress}
        aria-label="查看通知"
        title="通知"
      >
        <Icon name="bell" size="small" />
      </button>
      
      <button 
        className="header-action-btn settings-btn"
        onClick={onSettingsPress}
        aria-label="打开设置"
        title="设置"
      >
        <Icon name="settings" size="small" />
      </button>
    </div>
  );
};

export default HeaderActions; 