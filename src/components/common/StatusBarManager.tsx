/**
 * 🍎 iOS状态栏文字颜色管理器 - 精简版
 * 只处理iOS支持的setStyle()，背景由CSS完全接管
 */

import { useEffect } from 'react'
import { StatusBar, Style } from '@capacitor/status-bar'
import { Capacitor } from '@capacitor/core'
import { useTheme } from '../../contexts/ThemeContext'

const StatusBarManager: React.FC = () => {
  const { theme } = useTheme()
  
  useEffect(() => {
    const updateStatusBarStyle = async () => {
      // 只在iOS原生平台执行
      if (!Capacitor.isNativePlatform()) {
        console.log('📱 Web环境，跳过状态栏设置')
        return
      }

      console.log(`🍎 设置iOS状态栏文字颜色: ${theme}主题`)

      try {
        // 📝 根据Capacitor官方文档：
        // Style.Light = 深色文字适配浅色背景
        // Style.Dark = 浅色文字适配暗色背景
        
        if (theme === 'dark') {
          await StatusBar.setStyle({ style: Style.Dark })
          console.log('🌙 设置浅色文字（适配暗色背景）')
        } else {
          await StatusBar.setStyle({ style: Style.Light })
          console.log('🌞 设置深色文字（适配浅色背景）')
        }
        
        // 确保状态栏显示
        await StatusBar.show()
        
        console.log('✅ iOS状态栏文字颜色设置完成')
        console.log('📱 背景色由CSS .ios-statusbar-integration 层处理')
        
      } catch (error) {
        console.error('❌ 状态栏设置失败:', error)
      }
    }

    // 立即执行设置
    updateStatusBarStyle()
    
  }, [theme]) // 响应主题变化

  return null // 不渲染任何UI
}

export default StatusBarManager 