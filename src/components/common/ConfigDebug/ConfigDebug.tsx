/**
 * 配置调试组件
 * 显示当前API配置和环境信息，用于调试
 */

import React from 'react';
import { getApiConfig, getCurrentEnvironment, getPlatformNetworkConfig } from '../../../config/api.config';

interface ConfigDebugProps {
  className?: string;
}

export const ConfigDebug: React.FC<ConfigDebugProps> = ({ className = '' }) => {
  const config = getApiConfig();
  const environment = getCurrentEnvironment();
  const platformConfig = getPlatformNetworkConfig();

  const debugInfo = {
    environment,
    platform: platformConfig.platform,
    isNative: platformConfig.isNative,
    baseURL: config.baseURL,
    imageBaseURL: config.imageBaseURL,
    timeout: config.timeout,
    enableLogging: config.enableLogging,
    // 环境变量
    envVars: {
      VITE_APP_ENV: import.meta.env.VITE_APP_ENV || 'undefined',
      VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'undefined',
      VITE_API_IMAGE_BASE_URL: import.meta.env.VITE_API_IMAGE_BASE_URL || 'undefined',
      MODE: import.meta.env.MODE,
      DEV: import.meta.env.DEV,
      PROD: import.meta.env.PROD
    }
  };

  return (
    <div className={`config-debug ${className}`} style={{ 
      background: '#f8f9fa', 
      border: '1px solid #dee2e6', 
      borderRadius: '8px', 
      padding: '16px', 
      margin: '16px 0',
      fontFamily: 'monospace',
      fontSize: '12px'
    }}>
      <h4 style={{ margin: '0 0 12px 0', color: '#495057' }}>🔧 配置调试信息</h4>
      
      <div style={{ marginBottom: '12px' }}>
        <strong>当前环境:</strong> {debugInfo.environment}
      </div>
      
      <div style={{ marginBottom: '12px' }}>
        <strong>平台信息:</strong>
        <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
          <li>平台: {debugInfo.platform}</li>
          <li>原生应用: {debugInfo.isNative ? '是' : '否'}</li>
        </ul>
      </div>
      
      <div style={{ marginBottom: '12px' }}>
        <strong>API配置:</strong>
        <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
          <li>Base URL: <code>{debugInfo.baseURL}</code></li>
          <li>Image URL: <code>{debugInfo.imageBaseURL}</code></li>
          <li>超时时间: {debugInfo.timeout}ms</li>
          <li>日志记录: {debugInfo.enableLogging ? '开启' : '关闭'}</li>
        </ul>
      </div>
      
      <div>
        <strong>环境变量:</strong>
        <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
          {Object.entries(debugInfo.envVars).map(([key, value]) => (
            <li key={key}>
              {key}: <code>{String(value)}</code>
            </li>
          ))}
        </ul>
      </div>
      
      <div style={{ 
        marginTop: '12px', 
        padding: '8px', 
        background: debugInfo.baseURL.includes('124.222.91.101') ? '#d4edda' : '#f8d7da',
        borderRadius: '4px',
        border: `1px solid ${debugInfo.baseURL.includes('124.222.91.101') ? '#c3e6cb' : '#f5c6cb'}`
      }}>
        <strong>状态:</strong> {debugInfo.baseURL.includes('124.222.91.101') 
          ? '✅ 正在使用HTTP测试服务器' 
          : '❌ 未使用HTTP测试服务器！检查配置。'}
      </div>
    </div>
  );
}; 