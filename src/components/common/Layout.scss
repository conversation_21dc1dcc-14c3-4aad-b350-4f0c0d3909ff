// Main Layout Styles
// Based on FitMaster Design System

.layout {
  display: flex;
  min-height: 100vh;
  background: var(--bg-primary);
  color: var(--text-primary, #f8fafc);
  font-family: var(--font-primary, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif);
}

// Main Content Area
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all var(--transition-normal);
  min-height: 100vh;
  
  &.with-sidebar {
    margin-left: var(--sidebar-width);
  }
  
  &.full-width {
    margin-left: 0;
  }
}

// Page Header
.page-header {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--primary-500);
  padding: 0 var(--space-6);
  height: var(--header-height);
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  
  // 统一所有页面使用dashboard-header样式
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: 16px 20px;
  
  .dashboard-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  
  .dashboard-date-info {
    flex: 1;
    min-width: 0;
  }
  
  .dashboard-title {
    font-size: 18px;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    font-weight: bold;
    line-height: 1.2;
  }
  
  .dashboard-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.2;
  }
  
  .header-actions {
    flex-shrink: 0;
  }
}

.page-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: var(--container-7xl);
  margin: 0 auto;
}

.page-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

// Action Buttons - 页面级别的按钮样式
.page-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: var(--error-500);
  color: var(--text-on-error);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-full);
  line-height: 1;
  min-width: 1.125rem;
  text-align: center;
}

// Page Content
.page-content {
  flex: 1;
  padding: var(--space-6);
  overflow-y: auto;
  
  // 当没有页面头部时，移除顶部padding
  &.no-header {
    padding-top: 0;
  }
  
  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: var(--primary-600, #1e293b);
  }
  
  &::-webkit-scrollbar-thumb {
    background: var(--primary-500, #334155);
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: var(--accent-500, #3b82f6);
  }
}

// Loading State
.page-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  flex-direction: column;
  gap: var(--space-4, 1rem);
  
  .loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--primary-500, #334155);
    border-top-color: var(--accent-500, #3b82f6);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .loading-text {
    color: var(--text-secondary, #cbd5e1);
    font-size: var(--text-base, 1rem);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Error State
.page-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  flex-direction: column;
  gap: var(--space-4, 1rem);
  text-align: center;
  
  .error-icon {
    width: 3rem;
    height: 3rem;
    color: var(--error-500, #ef4444);
  }
  
  .error-title {
    font-size: var(--text-xl, 1.25rem);
    font-weight: var(--font-semibold, 600);
    color: var(--text-primary, #f8fafc);
    margin: 0;
  }
  
  .error-message {
    color: var(--text-secondary, #cbd5e1);
    font-size: var(--text-base, 1rem);
    margin: 0;
  }
  
  .error-actions {
    display: flex;
    gap: var(--space-3, 0.75rem);
    margin-top: var(--space-4, 1rem);
  }
}

// Responsive Design
@media (max-width: 768px) {
  .layout {
    &.mobile-layout {
      .main-content {
        /* 移动端样式由mobile-optimizations.scss统一管理 */
        /* 避免与iOS优化产生冲突 */
        &.with-bottom-nav {
          /* padding-bottom由mobile-optimizations.scss处理 */
        }
      }
    }
  }
  
  .page-header {
    padding: 0 var(--space-4);
    height: auto; // 改为auto以适应内容
    min-height: var(--header-height-mobile);
    
    // 统一移动端header样式
    padding: 16px; // 增加内边距
    
    .dashboard-header-content {
      flex-direction: row; // 改回row布局
      gap: 12px;
      align-items: center; // 保持居中对齐
      justify-content: space-between;
      width: 100%;
      min-height: 48px; // 确保最小高度
    }
    
    .dashboard-date-info {
      flex: 1;
      min-width: 0;
      display: block; // 确保显示
      
      .dashboard-title {
        font-size: 16px;
        margin: 0 0 2px 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      .dashboard-subtitle {
        font-size: 12px;
        margin: 0;
        color: var(--text-secondary);
      }
    }
    
    .header-actions {
      flex-shrink: 0;
    }
  }
  

  
  .page-content {
    padding: 12px;
  }
}

// 更小屏幕的进一步调整
@media (max-width: 480px) {
  .page-header {
    padding: 0 12px;
    
    // 统一小屏幕header样式
    padding: 8px 12px;
  }
  
  .page-content {
    padding: 12px;
  }
}

// Tablet Responsive
@media (min-width: 769px) and (max-width: 1024px) {
  .main-content {
    &.with-sidebar {
      margin-left: 14rem; // Matches tablet sidebar width
    }
  }
  
  .page-header {
    padding: 0 var(--space-5, 1.25rem);
  }
  
  .page-content {
    padding: var(--space-5, 1.25rem);
  }
}

// Large Desktop
@media (min-width: 1440px) {
  .page-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-8, 2rem);
  }
}

// Dark mode (already optimized)
@media (prefers-color-scheme: dark) {
  .layout {
    // Already optimized for dark theme
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .page-header {
    border-bottom-width: 2px;
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }
}

// Print styles
@media print {
  .page-header,
  .page-actions {
    display: none;
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .page-content {
    padding: 0;
  }
} 
 