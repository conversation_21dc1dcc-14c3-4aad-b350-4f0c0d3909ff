import React from 'react';
import './PixelHeader.scss';

export interface PixelHeaderProps {
  variant?: 'profile' | 'dashboard' | 'workout' | 'default';
  title: string;
  subtitle?: string;
  showNotification?: boolean;
  showSettings?: boolean;
  showBackButton?: boolean;
  onBack?: () => void;
  onNotificationClick?: () => void;
  onSettingsClick?: () => void;
}

const PixelHeader: React.FC<PixelHeaderProps> = ({
  variant = 'default',
  title,
  subtitle,
  showNotification = false,
  showSettings = false,
  showBackButton = false,
  onBack,
  onNotificationClick,
  onSettingsClick,
}) => {
  return (
    <header className={`pixel-header pixel-header--${variant}`}>
      <div className="pixel-header__left">
        {showBackButton && (
          <button
            className="pixel-header__back-btn"
            onClick={onBack}
            aria-label="返回"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
          </button>
        )}
        <div className="pixel-header__title-section">
          <h1 className="pixel-header__title">{title}</h1>
          {subtitle && <p className="pixel-header__subtitle">{subtitle}</p>}
        </div>
      </div>
      
      <div className="pixel-header__right">
        {showNotification && (
          <button
            className="pixel-header__icon-btn"
            onClick={onNotificationClick}
            aria-label="通知"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" />
              <path d="M13.73 21a2 2 0 0 1-3.46 0" />
            </svg>
          </button>
        )}
        
        {showSettings && (
          <button
            className="pixel-header__icon-btn"
            onClick={onSettingsClick}
            aria-label="设置"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <circle cx="12" cy="12" r="3" />
              <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24" />
            </svg>
          </button>
        )}
      </div>
    </header>
  );
};

export default PixelHeader; 