.pixel-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #1a1a1a;
  border-bottom: 2px solid #333;
  min-height: 64px;
  
  // 像素风格边框
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid #444;
    border-radius: 0;
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    z-index: -1;
  }

  &__left {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  &__right {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &__back-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #333;
    border: 2px solid #555;
    border-radius: 0;
    color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: #444;
      border-color: #666;
      transform: translateY(-1px);
    }
    
    &:active {
      transform: translateY(0);
    }
  }

  &__title-section {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  &__title {
    font-family: 'Fusion Pixel', monospace;
    font-size: 18px;
    font-weight: 700;
    color: #fff;
    margin: 0;
    line-height: 1.2;
    text-shadow: 1px 1px 0px #000;
  }

  &__subtitle {
    font-family: 'Fusion Pixel', monospace;
    font-size: 12px;
    color: #aaa;
    margin: 0;
    line-height: 1.2;
  }

  &__icon-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #333;
    border: 2px solid #555;
    border-radius: 0;
    color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background: #444;
      border-color: #666;
      transform: translateY(-1px);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    svg {
      width: 20px;
      height: 20px;
    }
  }

  // 变体样式
  &--profile {
    background: linear-gradient(135deg, #2d1b69 0%, #1a1035 100%);
    border-bottom-color: #4a3c8e;
    
    &::before {
      background: linear-gradient(135deg, #2d1b69 0%, #1a1035 100%);
    }
  }

  &--dashboard {
    background: linear-gradient(135deg, #1a4c96 0%, #0f2645 100%);
    border-bottom-color: #2d5aa0;
    
    &::before {
      background: linear-gradient(135deg, #1a4c96 0%, #0f2645 100%);
    }
  }

  &--workout {
    background: linear-gradient(135deg, #c44569 0%, #8b1538 100%);
    border-bottom-color: #e74c3c;
    
    &::before {
      background: linear-gradient(135deg, #c44569 0%, #8b1538 100%);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .pixel-header {
    padding: 12px 16px;
    min-height: 56px;
    
    &__title {
      font-size: 16px;
    }
    
    &__subtitle {
      font-size: 11px;
    }
    
    &__back-btn,
    &__icon-btn {
      width: 36px;
      height: 36px;
      
      svg {
        width: 18px;
        height: 18px;
      }
    }
  }
} 