@use '../../../styles/variables' as *;

.input-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  &--full-width {
    width: 100%;
  }
}

.input-label {
  color: var(--color-text-primary);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.4;
}

.input-required {
  color: var(--error-500);
  margin-left: 0.25rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input {
  // 基础样式
  width: 100%;
  border: 1px solid rgba(79, 195, 247, 0.3);
  border-radius: 8px;
  background: var(--color-background-secondary);
  color: var(--color-text-primary);
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.5;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  
  &::placeholder {
    color: var(--color-text-secondary);
    opacity: 0.6;
  }
  
  &:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.2);
  }

  // 尺寸变体
  &--sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    min-height: 32px;
  }

  &--md {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    min-height: 40px;
  }

  &--lg {
    padding: 1rem 1.25rem;
    font-size: 1.125rem;
    min-height: 48px;
  }

  // 变体样式
  &--default {
    background: var(--color-background-secondary);
  }

  &--filled {
    background: rgba(79, 195, 247, 0.1);
    border-color: transparent;
    
    &:focus {
      background: var(--color-background-secondary);
      border-color: var(--color-primary);
    }
  }

  &--outlined {
    background: transparent;
    border-width: 2px;
  }

  // 状态样式
  &--error {
    border-color: var(--error-500);
    
    &:focus {
      border-color: var(--error-500);
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
    }
  }

  &--success {
    border-color: var(--success-500);
    
    &:focus {
      border-color: var(--success-500);
      box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
    }
  }

  &--warning {
    border-color: var(--warning-500);
    
    &:focus {
      border-color: var(--warning-500);
      box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2);
    }
  }

  // 禁用状态
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(79, 195, 247, 0.05);
  }

  // 图标和前后缀调整
  &--has-left-icon {
    padding-left: 2.5rem;
  }

  &--has-right-icon {
    padding-right: 2.5rem;
  }

  &--has-prefix {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: none;
  }

  &--has-suffix {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
  }

  &--clearable {
    padding-right: 2.5rem;
  }
}

// 图标样式
.input-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-secondary);
  pointer-events: none;
  z-index: 1;

  &--left {
    left: 0.75rem;
  }

  &--right {
    right: 0.75rem;
  }

  svg {
    width: 18px;
    height: 18px;
    stroke-width: 2;
  }
}

// 前缀和后缀
.input-prefix,
.input-suffix {
  padding: 0.75rem 1rem;
  background: rgba(79, 195, 247, 0.1);
  border: 1px solid rgba(79, 195, 247, 0.3);
  color: var(--color-text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.input-prefix {
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  border-right: none;
}

.input-suffix {
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  border-left: none;
}

// 清除按钮
.input-clear {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 2;

  &:hover {
    background: rgba(79, 195, 247, 0.1);
    color: var(--color-text-primary);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  svg {
    width: 14px;
    height: 14px;
    stroke-width: 2;
  }
}

// 底部信息
.input-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 0.5rem;
  min-height: 1.25rem;
}

.input-message {
  color: var(--color-text-secondary);
  font-size: 0.75rem;
  line-height: 1.4;
  flex: 1;

  &--error {
    color: var(--error-500);
  }
}

.input-count {
  color: var(--color-text-secondary);
  font-size: 0.75rem;
  font-family: monospace;
  line-height: 1.4;
  white-space: nowrap;

  &--error {
    color: var(--error-500);
  }
}

// 响应式适配
@include mobile {
  .input {
    &--sm {
      padding: 0.5rem;
      font-size: 0.8rem;
      min-height: 28px;
    }
    
    &--md {
      padding: 0.625rem 0.875rem;
      font-size: 0.9rem;
      min-height: 36px;
    }
    
    &--lg {
      padding: 0.875rem 1rem;
      font-size: 1rem;
      min-height: 44px;
    }

    &--has-left-icon {
      padding-left: 2.25rem;
    }

    &--has-right-icon {
      padding-right: 2.25rem;
    }
  }

  .input-icon {
    &--left {
      left: 0.5rem;
    }

    &--right {
      right: 0.5rem;
    }

    svg {
      width: 16px;
      height: 16px;
    }
  }

  .input-clear {
    right: 0.5rem;
    width: 18px;
    height: 18px;

    svg {
      width: 12px;
      height: 12px;
    }
  }

  .input-prefix,
  .input-suffix {
    padding: 0.625rem 0.875rem;
    font-size: 0.8rem;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .input {
    border-width: 2px;

    &--outlined {
      border-width: 3px;
    }
  }

  .input-prefix,
  .input-suffix {
    border-width: 2px;
  }
}

// 减少动画模式
@media (prefers-reduced-motion: reduce) {
  .input {
    transition: none;
  }

  .input-clear {
    transition: none;
  }
} 