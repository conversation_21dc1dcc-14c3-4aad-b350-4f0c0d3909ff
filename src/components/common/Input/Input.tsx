import { forwardRef, InputHTMLAttributes, ReactNode } from 'react';
import './Input.scss';

export interface InputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /** 输入框变体 */
  variant?: 'default' | 'filled' | 'outlined';
  /** 输入框尺寸 */
  size?: 'sm' | 'md' | 'lg';
  /** 输入框状态 */
  state?: 'default' | 'error' | 'success' | 'warning';
  /** 标签文本 */
  label?: string;
  /** 帮助文本 */
  helperText?: string;
  /** 错误文本 */
  errorText?: string;
  /** 是否必填 */
  required?: boolean;
  /** 是否全宽 */
  fullWidth?: boolean;
  /** 左侧图标 */
  leftIcon?: ReactNode;
  /** 右侧图标 */
  rightIcon?: ReactNode;
  /** 前缀文本 */
  prefix?: string;
  /** 后缀文本 */
  suffix?: string;
  /** 是否显示清除按钮 */
  clearable?: boolean;
  /** 清除按钮点击事件 */
  onClear?: () => void;
  /** 是否显示字数统计 */
  showCount?: boolean;
  /** 最大字符数 */
  maxLength?: number;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      variant = 'default',
      size = 'md',
      state = 'default',
      label,
      helperText,
      errorText,
      required = false,
      fullWidth = false,
      leftIcon,
      rightIcon,
      prefix,
      suffix,
      clearable = false,
      onClear,
      showCount = false,
      maxLength,
      className = '',
      disabled = false,
      value,
      onChange,
      ...props
    },
    ref
  ) => {
    const inputClasses = [
      'input',
      `input--${variant}`,
      `input--${size}`,
      `input--${state}`,
      fullWidth && 'input--full-width',
      disabled && 'input--disabled',
      leftIcon && 'input--has-left-icon',
      rightIcon && 'input--has-right-icon',
      prefix && 'input--has-prefix',
      suffix && 'input--has-suffix',
      clearable && value && 'input--clearable',
      className
    ]
      .filter(Boolean)
      .join(' ');

    const fieldClasses = [
      'input-field',
      fullWidth && 'input-field--full-width'
    ]
      .filter(Boolean)
      .join(' ');

    const handleClear = () => {
      if (onClear) {
        onClear();
      }
    };

    const currentLength = typeof value === 'string' ? value.length : 0;
    const displayError = state === 'error' && errorText;
    const displayHelper = !displayError && helperText;

    return (
      <div className={fieldClasses}>
        {label && (
          <label className="input-label">
            {label}
            {required && <span className="input-required">*</span>}
          </label>
        )}
        
        <div className="input-wrapper">
          {leftIcon && (
            <div className="input-icon input-icon--left">
              {leftIcon}
            </div>
          )}
          
          {prefix && (
            <div className="input-prefix">
              {prefix}
            </div>
          )}
          
          <input
            ref={ref}
            className={inputClasses}
            disabled={disabled}
            value={value}
            onChange={onChange}
            maxLength={maxLength}
            {...props}
          />
          
          {suffix && (
            <div className="input-suffix">
              {suffix}
            </div>
          )}
          
          {clearable && value && (
            <button
              type="button"
              className="input-clear"
              onClick={handleClear}
              disabled={disabled}
            >
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </button>
          )}
          
          {rightIcon && !clearable && (
            <div className="input-icon input-icon--right">
              {rightIcon}
            </div>
          )}
        </div>
        
        <div className="input-footer">
          {(displayError || displayHelper) && (
            <div className={`input-message ${displayError ? 'input-message--error' : ''}`}>
              {displayError ? errorText : helperText}
            </div>
          )}
          
          {showCount && maxLength && (
            <div className="input-count">
              <span className={currentLength > maxLength ? 'input-count--error' : ''}>
                {currentLength}
              </span>
              /{maxLength}
            </div>
          )}
        </div>
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input; 