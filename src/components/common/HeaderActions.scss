.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.header-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 12px;
  background: var(--bg-surface);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  
  .hn {
    width: 20px;
    height: 20px;
    font-size: 20px;
    color: inherit;
  }
  
  &:hover {
    background: var(--bg-hover);
    border-color: var(--primary-500);
    color: var(--text-primary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: none;
  }
  
  &:focus {
    outline: 2px solid var(--accent-500);
    outline-offset: 2px;
  }
}

// 主题按钮特定样式
.theme-btn {
  &:hover {
    background: var(--primary-600);
    border-color: var(--accent-500);
    color: var(--accent-400);
    
    .hn {
      color: var(--accent-400);
    }
  }
}

// 通知按钮特定样式
.notification-btn {
  &:hover {
    background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
    border-color: #f59e0b;
    color: #f59e0b;
    
    .hn {
      color: #f59e0b;
    }
  }
}

// 设置按钮特定样式
.settings-btn {
  &:hover {
    background: linear-gradient(135deg, #e0e7ff 0%, #8b5cf6 100%);
    border-color: #8b5cf6;
    color: #8b5cf6;
    
    .hn {
      color: #8b5cf6;
    }
  }
}

// 移动端响应式
@media (max-width: 768px) {
  .header-actions {
    gap: 8px;
  }
  
  .header-action-btn {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    
    .hn {
      width: 18px;
      height: 18px;
      font-size: 18px;
    }
  }
}

// 更小屏幕的调整
@media (max-width: 480px) {
  .header-actions {
    gap: 6px;
  }
  
  .header-action-btn {
    width: 36px;
    height: 36px;
    
    .hn {
      width: 16px;
      height: 16px;
      font-size: 16px;
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .header-action-btn {
    border-width: 2px;
    
    &:hover,
    &:focus {
      border-width: 2px;
    }
  }
}

// 减少动画
@media (prefers-reduced-motion: reduce) {
  .header-action-btn {
    transition: none;
    
    &:hover {
      transform: none;
    }
  }
} 