// Icon component styles - 与Pixel Icon Library兼容
.hn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  // 尺寸定义
  &.icon-small {
    font-size: 14px;
    width: 16px;
    height: 16px;
    line-height: 16px;
  }
  
  &.icon-medium {
    font-size: 18px;
    width: 20px;
    height: 20px;
    line-height: 20px;
  }
  
  &.icon-large {
    font-size: 24px;
    width: 28px;
    height: 28px;
    line-height: 28px;
  }
  
  &.icon-xlarge {
    font-size: 32px;
    width: 36px;
    height: 36px;
    line-height: 36px;
  }
} 