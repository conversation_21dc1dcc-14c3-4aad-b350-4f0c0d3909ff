import React from 'react';
import { IconProps, getIconClass } from '../../../utils/iconMapping';
import './Icon.scss';

/**
 * Icon组件
 * 使用Pixel Icon Library实现统一的图标组件
 * 
 * @param {IconProps} props 组件属性
 * @returns {JSX.Element} 图标组件
 * 
 * @example
 * // 基础用法
 * <Icon name="workout" />
 * 
 * // 指定大小
 * <Icon name="add" size="large" />
 * 
 * // 添加额外样式
 * <Icon name="settings" className="settings-icon" />
 */
const Icon: React.FC<IconProps> = ({
  name,
  size = 'medium',
  className = '',
}) => {
  // 获取图标类名
  const iconClass = getIconClass(name);
  
  // 生成正确的Pixel Icon Library格式的类名
  const classes = [
    'hn',
    iconClass,
    `icon-${size}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <i className={classes} aria-hidden="true"></i>
  );
};

export default Icon; 