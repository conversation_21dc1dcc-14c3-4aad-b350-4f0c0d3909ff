// Theme Toggle Component Styles
.theme-toggle {
  position: relative;
  display: inline-block;

  .theme-toggle-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    border: 1px solid var(--primary-500);
    border-radius: var(--radius-lg);
    background: var(--bg-surface);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    
    &:hover {
      background: var(--primary-600);
      color: var(--text-primary);
      border-color: var(--accent-500);
      transform: translateY(-1px);
    }
    
    &:focus {
      outline: 2px solid var(--accent-500);
      outline-offset: 2px;
    }
    
    .theme-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 1.125rem;
      height: 1.125rem;
      
      svg {
        width: 100%;
        height: 100%;
        stroke-width: 2;
        transition: transform var(--transition-normal) var(--ease-in-out);
      }
    }
    
    &:hover .theme-icon svg {
      transform: rotate(15deg);
    }
    
    .theme-label {
      white-space: nowrap;
      
      @media (max-width: 768px) {
        display: none;
      }
    }
  }

  // 下拉菜单样式
  .theme-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--space-2);
    background: var(--bg-surface);
    border: 1px solid var(--primary-500);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--space-2);
    min-width: 12rem;
    z-index: var(--z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-0.5rem);
    transition: all var(--transition-normal) var(--ease-in-out);
    backdrop-filter: blur(10px);
    
    .theme-option {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      width: 100%;
      padding: var(--space-3);
      border: none;
      border-radius: var(--radius-md);
      background: transparent;
      color: var(--text-secondary);
      cursor: pointer;
      font-size: var(--text-sm);
      transition: all var(--transition-normal) var(--ease-in-out);
      
      svg {
        width: 1rem;
        height: 1rem;
        stroke-width: 2;
        flex-shrink: 0;
      }
      
      &:hover {
        background: var(--primary-600);
        color: var(--text-primary);
      }
      
      &.active {
        background: var(--accent-500);
        color: var(--text-on-accent);
      }
      
      .system-indicator {
        margin-left: auto;
        font-size: var(--text-xs);
        color: var(--text-tertiary);
        
        .active & {
          color: var(--text-on-accent);
          opacity: 0.8;
        }
      }
    }
  }

  // 悬停时显示下拉菜单
  &:hover .theme-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  // 移动端适配
  @media (max-width: 768px) {
    .theme-toggle-btn {
      padding: var(--space-2);
      min-width: 2.5rem;
      height: 2.5rem;
      justify-content: center;
    }
    
    .theme-menu {
      right: -2rem;
      min-width: 10rem;
    }
  }
}

// 主题特定样式
.theme-light {
  .theme-toggle-btn {
    .theme-icon svg {
      color: var(--warning-500);
    }
  }
}

.theme-dark {
  .theme-toggle-btn {
    .theme-icon svg {
      color: var(--accent-400);
    }
  }
}

// 动画效果
@keyframes themeSwitch {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

.theme-toggle-btn:active .theme-icon svg {
  animation: themeSwitch 0.3s ease-in-out;
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  .theme-toggle-btn,
  .theme-menu,
  .theme-option {
    transition: none;
  }
  
  .theme-toggle-btn:hover .theme-icon svg {
    transform: none;
  }
  
  .theme-toggle-btn:active .theme-icon svg {
    animation: none;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .theme-toggle-btn {
    border-width: 2px;
    
    &:hover,
    &:focus {
      border-width: 2px;
    }
  }
  
  .theme-menu {
    border-width: 2px;
  }
} 