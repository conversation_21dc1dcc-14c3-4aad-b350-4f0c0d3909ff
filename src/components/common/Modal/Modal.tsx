import React, { useEffect, useRef, ReactNode } from 'react';
import { createPortal } from 'react-dom';
import './Modal.scss';

export interface ModalProps {
  /** 是否显示模态框 */
  open: boolean;
  /** 关闭模态框回调 */
  onClose: () => void;
  /** 模态框标题 */
  title?: string;
  /** 模态框内容 */
  children: ReactNode;
  /** 模态框尺寸 */
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  /** 是否显示关闭按钮 */
  closable?: boolean;
  /** 是否点击遮罩层关闭 */
  maskClosable?: boolean;
  /** 是否显示遮罩层 */
  mask?: boolean;
  /** 底部操作区域 */
  footer?: ReactNode;
  /** 是否居中显示 */
  centered?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 是否禁用body滚动 */
  destroyOnClose?: boolean;
  /** z-index值 */
  zIndex?: number;
}

const Modal: React.FC<ModalProps> = ({
  open,
  onClose,
  title,
  children,
  size = 'md',
  closable = true,
  maskClosable = true,
  mask = true,
  footer,
  centered = true,
  className = '',
  destroyOnClose = false,
  zIndex = 1050
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // 处理ESC键关闭
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && open) {
        onClose();
      }
    };

    if (open) {
      document.addEventListener('keydown', handleEsc);
      // 禁用body滚动
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEsc);
      // 恢复body滚动
      document.body.style.overflow = 'unset';
    };
  }, [open, onClose]);

  // 处理焦点管理
  useEffect(() => {
    if (open && contentRef.current) {
      // 聚焦到模态框内容
      const focusableElement = contentRef.current.querySelector(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      ) as HTMLElement;
      
      if (focusableElement) {
        focusableElement.focus();
      } else {
        contentRef.current.focus();
      }
    }
  }, [open]);

  // 处理遮罩层点击
  const handleMaskClick = (event: React.MouseEvent) => {
    if (maskClosable && event.target === event.currentTarget) {
      onClose();
    }
  };

  // 处理模态框内容点击（阻止冒泡）
  const handleContentClick = (event: React.MouseEvent) => {
    event.stopPropagation();
  };

  const modalClasses = [
    'modal',
    `modal--${size}`,
    centered && 'modal--centered',
    className
  ]
    .filter(Boolean)
    .join(' ');

  if (!open && destroyOnClose) {
    return null;
  }

  const modalContent = (
    <div
      className={`modal-wrapper ${open ? 'modal-wrapper--open' : ''}`}
      style={{ zIndex }}
      onClick={handleMaskClick}
      ref={modalRef}
    >
      {mask && <div className="modal-mask" />}
      
      <div
        className={modalClasses}
        onClick={handleContentClick}
        ref={contentRef}
        tabIndex={-1}
        role="dialog"
        aria-modal="true"
        aria-labelledby={title ? 'modal-title' : undefined}
      >
        {/* 模态框头部 */}
        {(title || closable) && (
          <div className="modal-header">
            {title && (
              <h3 id="modal-title" className="modal-title">
                {title}
              </h3>
            )}
            {closable && (
              <button
                type="button"
                className="modal-close"
                onClick={onClose}
                aria-label="关闭"
              >
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <line x1="18" y1="6" x2="6" y2="18"/>
                  <line x1="6" y1="6" x2="18" y2="18"/>
                </svg>
              </button>
            )}
          </div>
        )}

        {/* 模态框内容 */}
        <div className="modal-body">
          {children}
        </div>

        {/* 模态框底部 */}
        {footer && (
          <div className="modal-footer">
            {footer}
          </div>
        )}
      </div>
    </div>
  );

  // 使用Portal渲染到body
  return createPortal(modalContent, document.body);
};

export default Modal; 