@use '../../../styles/variables' as *;

.modal-wrapper {
  position: fixed;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
  
  &--open {
    visibility: visible;
    opacity: 1;
  }
}

.modal-mask {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.modal {
  position: relative;
  background: var(--color-background-secondary);
  border-radius: 16px;
  border: 1px solid rgba(79, 195, 247, 0.2);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  max-height: 90vh;
  display: flex;
  flex-direction: column;

  &--sm { max-width: 400px; }
  &--md { max-width: 500px; }
  &--lg { max-width: 700px; }
  &--xl { max-width: 900px; }
  &--full { width: 95vw; height: 95vh; max-width: none; max-height: none; }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(79, 195, 247, 0.1);
}

.modal-title {
  color: var(--color-text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(79, 195, 247, 0.1);
    color: var(--color-text-primary);
  }

  svg {
    width: 18px;
    height: 18px;
  }
}

.modal-body {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  color: var(--color-text-primary);
}

.modal-footer {
  padding: 1rem 1.5rem 1.5rem;
  border-top: 1px solid rgba(79, 195, 247, 0.1);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
} 