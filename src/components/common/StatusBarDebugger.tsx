/**
 * 🔍 iOS状态栏调试工具
 * 用于测试和可视化状态栏修复效果
 */

import React, { useState, useEffect } from 'react'
import { StatusBar } from '@capacitor/status-bar'
import { Capacitor } from '@capacitor/core'
import { useTheme } from '../../contexts/ThemeContext'

const StatusBarDebugger: React.FC = () => {
  const { theme, setTheme } = useTheme()
  const [isDebugMode, setIsDebugMode] = useState(false)
  const [statusBarInfo, setStatusBarInfo] = useState<any>(null)
  const [isNative, setIsNative] = useState(false)

  useEffect(() => {
    setIsNative(Capacitor.isNativePlatform())
    
    // 获取状态栏信息
    if (Capacitor.isNativePlatform()) {
      StatusBar.getInfo().then(info => {
        setStatusBarInfo(info)
      }).catch(console.error)
    }
  }, [])

  const toggleDebugMode = () => {
    const newDebugMode = !isDebugMode
    setIsDebugMode(newDebugMode)
    
    // 添加/移除调试CSS类
    if (newDebugMode) {
      document.body.classList.add('debug-statusbar')
    } else {
      document.body.classList.remove('debug-statusbar')
    }
  }

  const testThemeSwitch = async () => {
    const newTheme = theme === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
    
    // 强制更新状态栏信息
    if (Capacitor.isNativePlatform()) {
      setTimeout(async () => {
        try {
          const info = await StatusBar.getInfo()
          setStatusBarInfo(info)
        } catch (error) {
          console.error('获取状态栏信息失败:', error)
        }
      }, 200)
    }
  }

  const forceStatusBarUpdate = async () => {
    if (!Capacitor.isNativePlatform()) return
    
    try {
      // 只设置文字颜色样式（iOS唯一支持的API）
      if (theme === 'dark') {
        await StatusBar.setStyle({ style: 'DARK' as any })
        console.log('🌙 强制设置: 暗色主题浅色文字')
      } else {
        await StatusBar.setStyle({ style: 'LIGHT' as any })
        console.log('🌞 强制设置: 浅色主题深色文字')
      }
      
      const info = await StatusBar.getInfo()
      setStatusBarInfo(info)
      console.log('✅ 强制更新状态栏成功（仅文字颜色）:', info)
      console.log('📝 背景色由CSS safe-area自动处理')
    } catch (error) {
      console.error('❌ 强制更新状态栏失败:', error)
    }
  }

  if (!isNative) {
    return (
      <div style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        background: 'rgba(0,0,0,0.8)',
        color: 'white',
        padding: '10px',
        borderRadius: '8px',
        fontSize: '12px',
        zIndex: 9999
      }}>
        <p>🌐 Web环境</p>
        <p>状态栏功能仅在iOS原生环境下可用</p>
      </div>
    )
  }

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: theme === 'dark' ? '#1e293b' : '#ffffff',
      color: theme === 'dark' ? '#ffffff' : '#000000',
      border: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}`,
      padding: '12px',
      borderRadius: '8px',
      fontSize: '11px',
      maxWidth: '280px',
      zIndex: 9999,
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
    }}>
      <h4 style={{ margin: '0 0 8px 0', fontSize: '12px', fontWeight: 'bold' }}>
        🔍 状态栏调试器
      </h4>
      
      {/* 当前状态信息 */}
      <div style={{ marginBottom: '10px' }}>
        <p style={{ margin: '2px 0' }}>📱 平台: {Capacitor.getPlatform()}</p>
        <p style={{ margin: '2px 0' }}>🎨 当前主题: {theme}</p>
        <p style={{ margin: '2px 0' }}>🔍 调试模式: {isDebugMode ? '启用' : '关闭'}</p>
      </div>

      {/* 状态栏信息 */}
      {statusBarInfo && (
        <div style={{ marginBottom: '10px', padding: '6px', background: theme === 'dark' ? '#374151' : '#f3f4f6', borderRadius: '4px' }}>
          <p style={{ margin: '1px 0', fontWeight: 'bold' }}>状态栏信息:</p>
          <p style={{ margin: '1px 0' }}>可见: {statusBarInfo.visible ? '是' : '否'}</p>
          <p style={{ margin: '1px 0' }}>样式: {statusBarInfo.style || 'unknown'}</p>
          <p style={{ margin: '1px 0' }}>覆盖: {statusBarInfo.overlays ? '是' : '否'}</p>
        </div>
      )}

      {/* 控制按钮 */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '6px' }}>
        <button
          onClick={toggleDebugMode}
          style={{
            padding: '6px 8px',
            fontSize: '10px',
            border: '1px solid #6b7280',
            borderRadius: '4px',
            background: isDebugMode ? '#ef4444' : '#6b7280',
            color: 'white',
            cursor: 'pointer'
          }}
        >
          {isDebugMode ? '🔴 关闭调试模式' : '🟢 开启调试模式'}
        </button>
        
        <button
          onClick={testThemeSwitch}
          style={{
            padding: '6px 8px',
            fontSize: '10px',
            border: '1px solid #3b82f6',
            borderRadius: '4px',
            background: '#3b82f6',
            color: 'white',
            cursor: 'pointer'
          }}
        >
          🎨 切换主题测试
        </button>
        
        <button
          onClick={forceStatusBarUpdate}
          style={{
            padding: '6px 8px',
            fontSize: '10px',
            border: '1px solid #059669',
            borderRadius: '4px',
            background: '#059669',
            color: 'white',
            cursor: 'pointer'
          }}
        >
          🔄 强制更新状态栏
        </button>
      </div>

      {/* 调试说明 */}
      {isDebugMode && (
        <div style={{ marginTop: '10px', padding: '6px', background: 'rgba(255,0,0,0.1)', borderRadius: '4px' }}>
          <p style={{ margin: '1px 0', fontSize: '10px', color: '#dc2626' }}>
            调试模式已启用:
          </p>
          <p style={{ margin: '1px 0', fontSize: '9px' }}>🔴 红色边框 = html::before遮罩</p>
          <p style={{ margin: '1px 0', fontSize: '9px' }}>🟢 绿色边框 = body::before遮罩</p>
          <p style={{ margin: '1px 0', fontSize: '9px' }}>🔵 蓝色边框 = page-header</p>
          <p style={{ margin: '1px 0', fontSize: '9px' }}>🟡 黄色边框 = header::before</p>
        </div>
      )}
    </div>
  )
}

export default StatusBarDebugger 