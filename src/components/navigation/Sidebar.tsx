import React from 'react';
import { useLocation, Link } from 'react-router-dom';
import Icon from '../common/Icon';
import { IconName } from '../../utils/iconMapping';
import './Sidebar.scss';

interface NavigationItem {
  id: string;
  label: string;
  iconName: IconName;
  path: string;
  badge?: number;
}

const Sidebar: React.FC = () => {
  const location = useLocation();

  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: '仪表板',
      iconName: 'dashboard',
      path: '/'
    },
    {
      id: 'workout',
      label: '训练',
      iconName: 'workout',
      path: '/workout'
    },
    {
      id: 'feed',
      label: '动态',
      iconName: 'feed',
      path: '/feed'
    },
    {
      id: 'routines',
      label: '计划',
      iconName: 'routines',
      path: '/routines'
    },
    {
      id: 'exercises',
      label: '动作库',
      iconName: 'exercises',
      path: '/exercises'
    },
    {
      id: 'profile',
      label: '个人',
      iconName: 'profile',
      path: '/profile'
    },
    {
      id: 'settings',
      label: '设置',
      iconName: 'settings',
      path: '/settings'
    },
  ];

  const isActivePath = (path: string): boolean => {
    return location.pathname === path;
  };

  return (
    <aside className="sidebar">
      {/* Logo/Brand Section */}
      <div className="sidebar-header">
        <div className="sidebar-brand">
          <div className="brand-icon">
            <Icon name="workout" size="medium" />
          </div>
          <span className="brand-text">FitMaster</span>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="sidebar-nav">
        <ul className="nav-list">
          {navigationItems.map((item) => (
            <li key={item.id} className="nav-list-item">
              <Link
                key={item.id}
                to={item.path}
                className={`nav-item ${isActivePath(item.path) ? 'active' : ''}`}
              >
                <Icon name={item.iconName} size="small" />
                <span className="nav-label">{item.label}</span>
                {item.badge && (
                  <span className="nav-badge">{item.badge}</span>
                )}
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      {/* User Section */}
      <div className="sidebar-user">
        <div className="user-profile">
          <div className="user-avatar">
            <img 
              src="/api/placeholder/40/40" 
              alt="User Avatar" 
              className="avatar-image"
            />
          </div>
          <div className="user-info">
            <div className="user-name">Alex Chen</div>
            <div className="user-status">Online</div>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar; 