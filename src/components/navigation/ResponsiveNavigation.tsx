import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import './ResponsiveNavigation.scss';

// 导航项类型定义
interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  badge?: number;
}

// 设备类型枚举
type DeviceType = 'mobile' | 'tablet' | 'desktop';

// 导航项配置
const navigationItems: NavigationItem[] = [
  {
    id: 'workout',
    label: '训练',
    icon: (
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M6 9L12 15L18 9"/>
        <circle cx="12" cy="12" r="9"/>
      </svg>
    ),
    path: '/'
  },
  {
    id: 'feed',
    label: '动态',
    icon: (
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M3 7V5C3 3.89543 3.89543 3 5 3H19C20.1046 3 21 3.89543 21 5V7"/>
        <path d="M3 7H21V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V7Z"/>
      </svg>
    ),
    path: '/feed'
  },
  {
    id: 'routines',
    label: '计划',
    icon: (
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M9 12L11 14L15 10"/>
        <circle cx="12" cy="12" r="9"/>
      </svg>
    ),
    path: '/routines'
  },
  {
    id: 'exercises',
    label: '动作库',
    icon: (
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M6.5 6.5H17.5"/>
        <circle cx="12" cy="12" r="9"/>
      </svg>
    ),
    path: '/exercises'
  },
  {
    id: 'profile',
    label: '个人',
    icon: (
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M20 21V19"/>
        <circle cx="12" cy="7" r="4"/>
      </svg>
    ),
    path: '/profile'
  },
  {
    id: 'settings',
    label: '设置',
    icon: (
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <circle cx="12" cy="12" r="3"/>
      </svg>
    ),
    path: '/settings'
  }
];

// 自定义Hook：设备类型检测
const useDeviceType = (): DeviceType => {
  const [deviceType, setDeviceType] = useState<DeviceType>('desktop');

  useEffect(() => {
    const checkDeviceType = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setDeviceType('mobile');
      } else if (width < 1024) {
        setDeviceType('tablet');
      } else {
        setDeviceType('desktop');
      }
    };

    checkDeviceType();
    window.addEventListener('resize', checkDeviceType);
    
    return () => window.removeEventListener('resize', checkDeviceType);
  }, []);

  return deviceType;
};

// 导航组件接口
interface ResponsiveNavigationProps {
  className?: string;
}

const ResponsiveNavigation: React.FC<ResponsiveNavigationProps> = ({ className = '' }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const deviceType = useDeviceType();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  // 判断路径是否激活
  const isActivePath = (path: string): boolean => {
    return location.pathname === path;
  };

  // 处理导航点击
  const handleNavigation = (path: string) => {
    navigate(path);
    if (deviceType === 'mobile' || deviceType === 'tablet') {
      setIsSidebarOpen(false);
    }
  };

  // 切换侧边栏状态
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // 渲染导航项
  const renderNavItems = () => (
    <ul className="nav-list">
      {navigationItems.map((item) => (
        <li key={item.id} className="nav-list-item">
          <button 
            className={`nav-item ${isActivePath(item.path) ? 'active' : ''}`}
            onClick={() => handleNavigation(item.path)}
            aria-label={item.label}
          >
            <span className="nav-item-icon">{item.icon}</span>
            <span className="nav-item-label">{item.label}</span>
            {item.badge && (
              <span className="nav-item-badge">{item.badge}</span>
            )}
          </button>
        </li>
      ))}
    </ul>
  );

  // 桌面端侧边栏
  if (deviceType === 'desktop') {
    return (
      <nav className={`responsive-nav desktop-nav ${className}`}>
        <div className="nav-header">
          <div className="nav-brand">
            <div className="brand-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M6 9L12 15L18 9"/>
                <circle cx="12" cy="12" r="9"/>
              </svg>
            </div>
            <span className="brand-text">FitMaster</span>
          </div>
        </div>
        <div className="nav-content">
          {renderNavItems()}
        </div>
      </nav>
    );
  }

  // 平板端可折叠侧边栏
  if (deviceType === 'tablet') {
    return (
      <>
        <button 
          className="hamburger-btn tablet-hamburger"
          onClick={toggleSidebar}
          aria-label="切换导航菜单"
        >
          <span className={`hamburger-line ${isSidebarOpen ? 'open' : ''}`}></span>
          <span className={`hamburger-line ${isSidebarOpen ? 'open' : ''}`}></span>
          <span className={`hamburger-line ${isSidebarOpen ? 'open' : ''}`}></span>
        </button>

        {isSidebarOpen && (
          <div 
            className="nav-overlay"
            onClick={() => setIsSidebarOpen(false)}
          />
        )}

        <nav className={`responsive-nav tablet-nav ${isSidebarOpen ? 'open' : ''} ${className}`}>
          <div className="nav-header">
            <div className="nav-brand">
              <span className="brand-text">FitMaster</span>
            </div>
            <button 
              className="close-btn"
              onClick={() => setIsSidebarOpen(false)}
              aria-label="关闭导航菜单"
            >
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </button>
          </div>
          <div className="nav-content">
            {renderNavItems()}
          </div>
        </nav>
      </>
    );
  }

  // 移动端底部导航栏
  return (
    <nav className={`responsive-nav mobile-nav ${className}`}>
      <div className="nav-content">
        {renderNavItems()}
      </div>
    </nav>
  );
};

export default ResponsiveNavigation; 