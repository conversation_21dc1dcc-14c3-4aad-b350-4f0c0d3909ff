
.responsive-nav {
  position: fixed;
  z-index: 1000;
  background: var(--color-background-primary);
  border: 1px solid rgba(79, 195, 247, 0.1);
  transition: all 0.3s ease;
}

.desktop-nav {
  top: 0;
  left: 0;
  width: 280px;
  height: 100vh;
  border-right: 1px solid rgba(79, 195, 247, 0.1);
}

.tablet-nav {
  top: 0;
  left: 0;
  width: 280px;
  height: 100vh;
  transform: translateX(-100%);
  
  &.open {
    transform: translateX(0);
  }
}

.mobile-nav {
  bottom: 0;
  left: 0;
  right: 0;
  height: 70px;
  border-top: 1px solid rgba(79, 195, 247, 0.1);
}

.nav-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid rgba(79, 195, 247, 0.1);
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-text {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-text-primary);
}

.nav-content {
  flex: 1;
  padding: 1rem 0;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  margin: 0 0.5rem;
  background: none;
  color: var(--color-text-secondary);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(79, 195, 247, 0.1);
    color: var(--color-text-primary);
  }
  
  &.active {
    background: rgba(79, 195, 247, 0.15);
    color: var(--color-primary);
  }
}

@include mobile {
  .mobile-nav .nav-list {
    flex-direction: row;
    justify-content: space-around;
  }
  
  .mobile-nav .nav-item {
    flex-direction: column;
    padding: 0.5rem;
    margin: 0;
    font-size: 0.75rem;
  }
} 
@use '../../styles/variables' as *;
 