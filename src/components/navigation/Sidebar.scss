// Sidebar Navigation Styles
// Based on FitMaster Design System

/* Sidebar Component Styles */
.sidebar {
  width: var(--sidebar-width);
  min-height: 100vh;
  background: var(--primary-700);
  border-right: 1px solid var(--primary-500);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: var(--z-fixed);
  transition: all var(--transition-normal);
}

/* Sidebar Header - Brand/Logo */
.sidebar-header {
  padding: var(--space-6) var(--space-4);
  border-bottom: 1px solid var(--primary-500);
}

.brand {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  text-decoration: none;
  color: var(--text-primary);
}

.brand-icon {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--accent-500);
  border-radius: var(--radius-lg);
  color: var(--text-on-accent);
  
  svg {
    width: 1.25rem;
    height: 1.25rem;
    stroke-width: 2;
  }
}

.brand-text {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  font-family: var(--font-display);
}

/* Navigation */
.sidebar-nav {
  flex: 1;
  padding: var(--space-4) 0;
  overflow-y: auto;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-list-item {
  margin-bottom: var(--space-1);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  margin: 0 var(--space-2);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  position: relative;
  font-weight: var(--font-medium);
  
  .hn {
    width: 20px;
    height: 20px;
    font-size: 20px;
    color: inherit;
    transition: all var(--transition-normal);
  }
  
  .nav-label {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: inherit;
  }
  
  .nav-badge {
    margin-left: auto;
    background: var(--error-500);
    color: var(--text-on-error);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    padding: 0.125rem 0.375rem;
    border-radius: var(--radius-full);
    line-height: 1;
    min-width: 1.125rem;
    text-align: center;
  }
  
  &:hover {
    background: var(--primary-600);
    color: var(--text-primary);
    transform: translateX(4px);
  }
  
  &.active {
    background: var(--accent-500);
    color: var(--text-on-accent);
    
    .hn {
      color: var(--text-on-accent);
    }
    
    &::before {
      content: '';
      position: absolute;
      left: -var(--space-2);
      top: 0;
      bottom: 0;
      width: 3px;
      background: var(--text-on-accent);
      border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
    }
  }
}

.nav-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  
  .nav-icon {
    width: 100%;
    height: 100%;
    stroke-width: 2;
  }
}

.nav-item-label {
  flex: 1;
  font-size: var(--text-sm);
}

.nav-item-badge {
  background: var(--error-500);
  color: var(--text-on-error);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-full);
  min-width: 1.125rem;
  height: 1.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* User Section */
.sidebar-user {
  padding: var(--space-4);
  border-top: 1px solid var(--primary-500);
  margin-top: auto;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  background: var(--primary-600);
  transition: all var(--transition-normal);
  cursor: pointer;
  
  &:hover {
    background: var(--primary-500);
    transform: translateY(-1px);
  }
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-full);
  overflow: hidden;
  border: 2px solid var(--accent-500);
  
  .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: 0.125rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-status {
  font-size: var(--text-xs);
  color: var(--success-400);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    
    &.mobile-open {
      transform: translateX(0);
    }
  }
}

/* Scrollbar Styling for Navigation */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: var(--primary-700);
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: var(--primary-500);
  border-radius: var(--radius-full);
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: var(--primary-400);
}

// Tablet Responsive
@media (min-width: 769px) and (max-width: 1024px) {
  .sidebar {
    width: 14rem; // Slightly narrower on tablets
  }

  .brand-text {
    font-size: var(--text-lg, 1.125rem);
  }

  .nav-item {
    padding: var(--space-2, 0.5rem) var(--space-3, 0.75rem);
    font-size: var(--text-sm, 0.875rem);
  }
}

// Animation for page transitions
.sidebar-transition-enter {
  transform: translateX(-100%);
}

.sidebar-transition-enter-active {
  transform: translateX(0);
  transition: transform 300ms ease-in-out;
}

.sidebar-transition-exit {
  transform: translateX(0);
}

.sidebar-transition-exit-active {
  transform: translateX(-100%);
  transition: transform 300ms ease-in-out;
}

// Focus states for accessibility
.nav-item:focus {
  outline: 2px solid var(--accent-500, #3b82f6);
  outline-offset: 2px;
}

.user-profile:focus {
  outline: 2px solid var(--accent-500, #3b82f6);
  outline-offset: 2px;
}

// Dark mode compatibility (already built for dark theme)
@media (prefers-color-scheme: dark) {
  .sidebar {
    // Already optimized for dark theme
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .sidebar {
    border-right-width: 2px;
  }

  .nav-item {
    border: 1px solid transparent;
    
    &:hover,
    &.active {
      border-color: currentColor;
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .nav-item,
  .user-profile {
    transition: none;
  }

  .nav-item:hover {
    transform: none;
  }
} 