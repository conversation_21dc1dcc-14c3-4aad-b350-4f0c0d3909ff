// Exercise Card Styles - iOS优化版本
.exercise-card {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  
  // iOS优化 - 确保合适的宽高比
  aspect-ratio: 0.8;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }
  
  // iOS小屏幕优化
  @media (max-width: 480px) {
    border-radius: 10px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    aspect-ratio: 0.85;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    }
  }
  
  // 极小屏幕优化
  @media (max-width: 360px) {
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    aspect-ratio: 0.9;
    
    &:hover {
      transform: none;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

.exercise-image {
  position: relative;
  width: 100%;
  flex: 1;
  overflow: hidden;
  background-color: #f5f5f5;
  min-height: 100px;
  
  // iOS优化
  @media (max-width: 480px) {
    min-height: 80px;
  }
  
  // 极小屏幕优化
  @media (max-width: 360px) {
    min-height: 70px;
  }
}

.image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.exercise-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease;
  
  &.loaded {
    opacity: 1;
  }
}

.favorite-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  
  // 未收藏状态：毛玻璃背景效果（贴合icon尺寸）
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 50%;
  transition: all 0.2s ease;

  svg {
    width: 18px;
    height: 18px;
    color: #ffffff; // 未收藏时使用白色图标
    stroke-width: 2;
    transition: color 0.2s ease;
  }
  
  .like-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transform: scale(1.05);
    
    svg {
      color: #ffffff;
    }
  }

  &:active {
    transform: scale(0.95);
  }

  &:focus {
    outline: none;
  }

  // 已收藏状态：完全透明
  &.favorited {
    background: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    border-radius: 0;

    &:hover {
      background: none !important;
      backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important;
      transform: scale(1.05);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
}

.exercise-info {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 6px; // 减少间距
  flex-shrink: 0;
  
  // 小屏幕优化
  @media (max-width: 480px) {
    padding: 10px;
    gap: 5px;
  }
  
  // 极小屏幕优化
  @media (max-width: 360px) {
    padding: 8px;
    gap: 4px;
  }
}

// 动作名称和器械标签同行布局
.exercise-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 2px; // 减少下边距
  
  // 小屏幕优化
  @media (max-width: 480px) {
    gap: 6px;
    margin-bottom: 1px;
  }
  
  // 极小屏幕优化
  @media (max-width: 360px) {
    gap: 4px;
    margin-bottom: 0px;
  }
}

.exercise-name {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.2; // 减少行高
  flex: 1;
  min-width: 0; // 允许文字截断
  
  // 文字截断
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  
  // 小屏幕优化
  @media (max-width: 480px) {
    font-size: 13px;
    -webkit-line-clamp: 2;
    line-height: 1.1;
  }
  
  // 极小屏幕优化
  @media (max-width: 360px) {
    font-size: 12px;
    -webkit-line-clamp: 1;
    line-height: 1.1;
  }
}

// 器械标签样式（位于右侧）
.equipment-tags {
  flex-shrink: 0;
  display: flex;
  align-items: flex-start;
}

.equipment-tag {
  background-color: #f0f0f0;
  color: #666;
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
  
  // 小屏幕优化
  @media (max-width: 480px) {
    font-size: 10px;
    padding: 2px 4px;
  }
  
  // 极小屏幕优化
  @media (max-width: 360px) {
    font-size: 9px;
    padding: 1px 3px;
  }
}

.exercise-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 6px;
  margin-top: -4px; // 进一步上移避免溢出
  
  .difficulty {
    display: flex;
    align-items: center;
    font-size: 11px;
    color: #666;
    font-weight: 500;
    margin-top: -2px; // 额外上移难度图标
    
    // 小屏幕优化
    @media (max-width: 480px) {
      font-size: 10px;
      margin-top: -1px;
    }
    
    // 极小屏幕优化
    @media (max-width: 360px) {
      font-size: 9px;
      margin-top: -1px;
    }
    
    .difficulty-plate {
      width: 14px;
      height: 14px;
      object-fit: contain;
      vertical-align: middle;
      margin-right: 3px;
      
      // 小屏幕优化
      @media (max-width: 480px) {
        width: 12px;
        height: 12px;
        margin-right: 2px;
      }
      
      // 极小屏幕优化
      @media (max-width: 360px) {
        width: 10px;
        height: 10px;
        margin-right: 1px;
      }
    }
  }
  
  // 新增：身体部位显示
  .body-parts {
    display: flex;
    align-items: center;
    font-size: 11px; // 统一为器械标签的字体大小
    color: #888;
    margin-top: -2px; // 额外上移身体部位标签
    
    @media (max-width: 480px) {
      margin-top: -1px;
    }
    
    @media (max-width: 360px) {
      margin-top: -1px;
    }
    
    .body-part-tag {
      background-color: #f0f8ff;
      color: #007AFF;
      padding: 1px 4px;
      border-radius: 3px;
      font-weight: 500;
      white-space: nowrap;
      font-size: 11px; // 与器械标签保持一致
      
      @media (max-width: 480px) {
        font-size: 10px; // 与器械标签保持一致
        padding: 1px 3px;
      }
      
      @media (max-width: 360px) {
        font-size: 9px; // 与器械标签保持一致
        padding: 0px 2px;
      }
    }
  }
}

// 移除不需要的样式
.category,
.exercise-details,
.muscle-groups,
.muscle-tag {
  display: none;
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .exercise-card {
    background-color: #1a1a1a;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    
    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    }
  }

  .exercise-image {
    background-color: #2a2a2a;
  }

  .image-placeholder {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  }

  .favorite-btn {
    // 暗色模式下的毛玻璃效果
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    
    svg {
      color: #ffffff; // 暗色模式下也使用白色图标
    }

    &:hover {
      background: rgba(255, 255, 255, 0.25);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      
      svg {
        color: #ffffff;
      }
    }
    
    // 已收藏状态在暗色模式下保持透明
    &.favorited {
      background: none !important;
      backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important;
      
      &:hover {
        background: none !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
      }
    }
  }

  .exercise-name {
    color: #ffffff;
  }

  .category {
    color: #a0a0a0;
    background-color: #2a2a2a;
  }

  .equipment-tag {
    &:not(.more) {
      color: #64D2FF;
      background-color: rgba(100, 210, 255, 0.1);
    }
    
    &.more {
      color: #a0a0a0;
      background-color: #2a2a2a;
    }
  }

  .muscle-tag.primary {
    color: #64D2FF;
    background-color: rgba(100, 210, 255, 0.1);
  }
}

// Performance optimizations
.exercise-card {
  contain: layout style paint;
  will-change: transform;
}

.exercise-img {
  will-change: opacity;
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .image-placeholder {
    animation: none;
    background: #f0f0f0;
  }
}