---
type: "manual"
---


# FitMaster 多端开发规则指导

## 🚀 项目概述

**FitMaster** 是一个现代化的健身管理应用，采用React 18 + TypeScript技术栈构建。这个项目旨在提供完整的健身追踪、数据可视化和社交功能，**特别优化了iOS原生体验**。

## 📋 技术栈规范

### 核心技术栈（强制要求）
- **前端框架**: React 18+ (必须使用函数组件和Hooks)
- **类型系统**: TypeScript 5.x (严格模式，100%类型覆盖)
- **构建工具**: Vite 5.x (不允许使用Webpack)
- **样式系统**: Sass (SCSS) + CSS变量
- **路由管理**: React Router DOM v6
- **状态管理**: React Context API + useReducer (避免使用Redux)
- **动画库**: Framer Motion (用于复杂动画)

### 多端技术栈（iOS优先）
- **跨平台框架**: Capacitor 5.6+ (iOS原生功能集成)
- **iOS原生插件**: 
  - @capacitor/status-bar (状态栏控制)
  - @capacitor/preferences (安全存储)
  - @capacitor/network (网络监听)
  - @capacitor/device (设备信息)
  - @capacitor/keyboard (键盘管理)
  - @capacitor/app (应用生命周期)
- **设计语言**: Apple Watch风格组件优先
- **Safe Area支持**: iOS刘海屏完美适配

### 开发工具栈
- **代码规范**: ESLint + TypeScript ESLint
- **包管理**: npm (不使用yarn或pnpm)
- **开发服务器**: Vite DevServer
- **iOS开发**: Xcode 14+ (iOS模拟器和真机测试)
- **版本控制**: Git + GitHub

## 🏗️ 多端架构规范

### 目录结构（严格遵循）
```
src/
├── components/           # 可复用组件库
│   ├── common/          # 通用组件 (Button, Card, Modal, etc.)
│   ├── navigation/      # 导航组件
│   ├── fitness/         # 健身专用组件 (Apple Watch风格)
│   ├── charts/          # 图表组件
│   └── achievements/    # 成就系统组件
├── pages/               # 页面组件
├── contexts/            # React Context定义
├── hooks/              # 自定义Hook
│   ├── useUnifiedSystem.ts  # iOS统一系统Hook
│   ├── useiOSStatusBar.ts   # iOS状态栏Hook
│   ├── useLayout.ts         # 布局管理Hook
│   └── useCapacitorFeatures.ts # 原生功能Hook
├── utils/              # 工具函数
├── types/              # TypeScript类型定义
├── styles/             # 全局样式
│   ├── design-system.css    # 统一CSS变量系统
│   ├── ios-unified-system.scss # iOS专用样式
│   └── mobile-optimizations.scss # 移动端优化
├── assets/             # 静态资源
└── constants/          # 常量定义
```

### Capacitor配置规范
```typescript
// capacitor.config.ts - iOS优先配置
const config: CapacitorConfig = {
  appId: 'com.fitmaster.app',
  appName: 'FitMaster',
  webDir: 'dist',
  
  // iOS特定配置（重点）
  ios: {
    contentInset: 'automatic',
    backgroundColor: '#ffffff',
    appendUserAgent: 'FitMaster/1.0',
  },
  
  plugins: {
    StatusBar: {
      overlaysWebView: false // 关键：让CSS处理状态栏背景
    },
    
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: "#ffffff",
      splashFullScreen: true,
      splashImmersive: true,
    }
  }
};
```

## 🍎 iOS原生功能集成规范

### Capacitor Hook开发标准
```typescript
// ✅ 正确：iOS原生功能Hook模式
export function useCapacitorFeatures(): CapacitorFeatures {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    connected: true,
    connectionType: 'unknown'
  });

  const isNative = Capacitor.isNativePlatform();
  const platform = Capacitor.getPlatform();

  // iOS状态栏样式设置（关键实现）
  const setStatusBarStyle = useCallback(async (style: 'light' | 'dark'): Promise<void> => {
    try {
      if (isNative && platform === 'ios') {
        // Capacitor StatusBar API正确用法：
        // Style.Light = 深色文字（适配浅色背景）
        // Style.Dark = 浅色文字（适配深色背景）
        const statusBarStyle = style === 'light' ? Style.Light : Style.Dark;
        await StatusBar.setStyle({ style: statusBarStyle });
      }
    } catch (error) {
      console.warn('设置状态栏样式失败:', error);
    }
  }, [isNative, platform]);

  return {
    deviceInfo,
    isNative,
    platform,
    networkStatus,
    setStatusBarStyle,
    // 其他原生功能...
  };
}
```

### iOS状态栏管理规范
```typescript
// iOS状态栏Hook标准实现
export function useiOSStatusBar(theme: Theme): iOSStatusBarHook {
  const platform = Capacitor.getPlatform();
  const isSupported = Capacitor.isNativePlatform() && platform === 'ios';

  useEffect(() => {
    if (!isSupported) return;

    const updateStatusBar = async () => {
      try {
        // 根据主题设置状态栏文字颜色
        const style = theme === 'dark' ? Style.Dark : Style.Light;
        await StatusBar.setStyle({ style });
        await StatusBar.show();
        
        console.log('✅ iOS状态栏文字颜色设置完成');
      } catch (error) {
        console.warn('⚠️ StatusBar API调用失败（不影响应用功能）:', error);
      }
    };

    updateStatusBar();
  }, [theme, isSupported]);

  return { platform, isSupported };
}
```

## 🎨 iOS设计系统规范

### CSS变量系统（iOS优化）
```scss
// iOS主题变量系统
:root {
  // === 基础色彩 ===
  --bg-primary: #ffffff;        // iOS浅色主背景
  --bg-secondary: #f8fafc;      // iOS次要背景
  --text-primary: #1a1a1a;      // iOS深色文字
  --text-secondary: #6b7280;    // iOS次要文字
  
  // === Apple Watch风格进度色彩 ===
  --progress-move: #FF6B35;     // 橙红色（活动）
  --progress-exercise: #4CAF50; // 绿色（锻炼）
  --progress-stand: #2196F3;    // 蓝色（站立）
  
  // === iOS Safe Area支持 ===
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);
  --safe-area-inset-right: env(safe-area-inset-right);
  
  // === iOS专用间距 ===
  --ios-touch-target: 44px;    // Apple HIG最小触摸目标
  --ios-header-height: calc(56px + var(--safe-area-inset-top));
  --ios-bottom-nav-height: calc(70px + var(--safe-area-inset-bottom));
}

// 暗色主题（iOS适配）
.theme-dark {
  --bg-primary: #0f172a;        // iOS暗色主背景
  --bg-secondary: #1e293b;      // iOS暗色次要背景
  --text-primary: #f8fafc;      // iOS浅色文字
  --text-secondary: #cbd5e1;    // iOS次要浅色文字
}
```

### iOS响应式设计规范
```scss
// ✅ iOS优先的响应式设计
.fitness-component {
  // iOS移动端默认样式（优先）
  padding: var(--space-4);
  min-height: var(--ios-touch-target);
  
  // iPad平板端
  @media (min-width: 768px) and (-webkit-touch-callout: none) {
    padding: var(--space-6);
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
  
  // iPhone横屏模式
  @media (max-width: 768px) and (orientation: landscape) {
    padding: var(--space-2);
    height: calc(100vh - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
  }
  
  // 桌面端（非触摸设备）
  @media (min-width: 1024px) and (hover: hover) {
    padding: var(--space-8);
    grid-template-columns: repeat(3, 1fr);
  }
}
```

## 🎯 Apple Watch风格组件规范

### 健身圆环组件标准
```typescript
// Apple Watch风格健身圆环组件规范（强制遵循）
interface AppleWatchRingsProps {
  moveProgress: number;
  moveGoal: number;
  exerciseProgress: number;
  exerciseGoal: number;
  standProgress: number;
  standGoal: number;
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  className?: string;
}

// 颜色规范（严格使用Apple Watch标准）
const APPLE_WATCH_COLORS = {
  move: '#FF6B35',      // 橙红色（官方色值）
  exercise: '#4CAF50',  // 绿色（官方色值）
  stand: '#2196F3'      // 蓝色（官方色值）
} as const;

// 动画规范（避免过度动画，注重性能）
const RING_ANIMATION_CONFIG = {
  duration: 1.5,        // 适中的动画时长
  ease: 'easeOut',      // 自然的缓动函数
  // 禁止使用：光晕、呼吸、弹跳等过度效果
} as const;
```

### 健身组件开发规范
```typescript
// 健身数据可视化组件必须遵循以下规范：

// 1. Apple Watch风格数据可视化
interface FitnessVisualizationProps {
  data: VisualizationData;
  type: 'rings' | 'chart' | 'progress';
  theme?: 'light' | 'dark';
  animated?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

// 2. iOS原生体验交互组件
interface FitnessInteractionProps {
  onWorkoutStart: (workout: Workout) => void;
  onWorkoutComplete: (result: WorkoutResult) => void;
  disabled?: boolean;
  touchFeedback?: boolean; // iOS触摸反馈
}

// 3. 响应式健身状态管理
const useFitnessState = () => {
  const [workouts, setWorkouts] = useState<Workout[]>([]);
  const [currentWorkout, setCurrentWorkout] = useState<Workout | null>(null);
  
  // 性能优化：使用useCallback包装所有函数
  const startWorkout = useCallback((workout: Workout) => {
    setCurrentWorkout(workout);
  }, []);
  
  const completeWorkout = useCallback((result: WorkoutResult) => {
    setWorkouts(prev => [...prev, result]);
    setCurrentWorkout(null);
  }, []);
  
  return {
    workouts,
    currentWorkout,
    startWorkout,
    completeWorkout
  };
};
```

## 📚 Context7 使用指导（iOS优化）

### iOS开发相关文档查询
```typescript
// iOS开发中必须使用Context7获取最新技术文档
// 优先查询以下技术栈文档：

// React + iOS相关文档
const reactDocs = '/context7/react_dev';
const reactNativeDocs = '/facebook/react-native'; // 参考iOS实践

// Capacitor相关文档
const capacitorDocs = '/ionic-team/capacitor';
const capacitorIOSDocs = '/ionic-team/capacitor-ios';

// 苹果设计规范
const appleHIGDocs = '/apple/human-interface-guidelines';
const appleWatchDocs = '/apple/watchkit';

// 使用示例：
// 1. 开发Apple Watch风格组件时查询官方设计规范
// 2. 集成Capacitor插件时查询最新API文档
// 3. 处理iOS Safe Area时查询最佳实践
```

### iOS开发文档查询策略
```typescript
// iOS开发流程中的文档查询顺序：
// 1. 首先查询Apple官方设计规范
// 2. 然后查询Capacitor iOS集成最佳实践
// 3. 最后查询React性能优化和iOS调试技巧

// 示例：开发iOS状态栏同步功能时
// Step 1: 查询Apple状态栏设计规范
// Step 2: 查询Capacitor StatusBar API最新用法
// Step 3: 查询React主题系统集成方式
```

## 🔧 MCP调用规范（iOS优化）

### iOS开发流程中的MCP使用
```typescript
// 1. 交互式反馈（必须）
// 在每个iOS开发阶段都必须调用mcp-feedback-enhanced获取用户反馈

// 2. iOS技术文档检索（推荐）
// 使用Context7 MCP获取Apple和Capacitor最新文档

// 3. iOS代码搜索（必要时）
// 使用codebase_search查找iOS特定实现
// 使用grep_search进行iOS相关API匹配

// 4. 文件操作（iOS组件开发）
// 优先使用edit_file创建iOS组件
// 大文件(>2500行)使用search_replace
```

### iOS开发MCP最佳实践
```typescript
// ✅ 正确：并行调用多个iOS相关MCP
// 同时查询iOS开发相关信息
await Promise.all([
  codebase_search('iOS StatusBar integration'),
  context7_get_library_docs('/ionic-team/capacitor'),
  grep_search('useCapacitorFeatures')
]);

// ❌ 错误：串行调用导致iOS开发效率低下
// 避免逐个调用MCP工具
```

## 📊 iOS性能优化规范

### React + iOS性能优化
```typescript
// 1. iOS特定的React.memo优化
const FitnessRings = React.memo<AppleWatchRingsProps>(({ data, onUpdate }) => {
  // 组件实现 - 针对iOS GPU加速优化
});

// 2. iOS触摸交互优化
const handleTouchStart = useCallback((event: TouchEvent) => {
  // iOS触摸反馈处理
  event.currentTarget.style.transform = 'scale(0.98)';
}, []);

// 3. iOS Safe Area计算优化
const safeAreaInsets = useMemo(() => {
  const top = getComputedStyle(document.documentElement)
    .getPropertyValue('--safe-area-inset-top') || '0px';
  const bottom = getComputedStyle(document.documentElement)
    .getPropertyValue('--safe-area-inset-bottom') || '0px';
  
  return {
    top: parseInt(top),
    bottom: parseInt(bottom)
  };
}, []);

// 4. iOS动画性能优化
const useIOSAnimation = (enabled: boolean) => {
  return useSpring({
    transform: enabled ? 'scale(1)' : 'scale(0.95)',
    config: {
      tension: 300,
      friction: 30,
      // iOS原生感的动画配置
    }
  });
};
```

### iOS代码分割和懒加载
```typescript
// iOS页面级代码分割
const WorkoutPage = lazy(() => import('./pages/WorkoutPage'));
const DashboardPage = lazy(() => import('./pages/DashboardPage'));

// iOS组件级懒加载（重要组件优先加载）
const FitnessRings = lazy(() => import('./components/fitness/FitnessRings'));
const AppleWatchChart = lazy(() => import('./components/charts/AppleWatchChart'));

// iOS资源预加载
const preloadIOSAssets = () => {
  import('./components/fitness/FitnessRings/FitnessRings');
  import('./hooks/useCapacitorFeatures');
};
```

## 🔍 iOS调试和测试指导

### iOS调试最佳实践
```typescript
// 1. iOS设备调试Hook
function useIOSDebug() {
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  
  useEffect(() => {
    if (Capacitor.isNativePlatform()) {
      Device.getInfo().then(info => {
        setDeviceInfo(info);
        console.log('🍎 iOS设备信息:', info);
      });
    }
  }, []);
  
  useDebugValue(`iOS: ${deviceInfo?.model || 'Unknown'}`);
  
  return deviceInfo;
}

// 2. iOS状态栏调试
function useStatusBarDebug(theme: Theme) {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🎨 状态栏主题: ${theme}`);
      console.log(`📱 安全区域: ${getComputedStyle(document.documentElement).getPropertyValue('--safe-area-inset-top')}`);
    }
  }, [theme]);
}

// 3. iOS性能监控
function useIOSPerformance() {
  useEffect(() => {
    if (Capacitor.getPlatform() === 'ios') {
      console.time('iOS组件渲染时间');
      return () => {
        console.timeEnd('iOS组件渲染时间');
      };
    }
  }, []);
}
```

### iOS错误处理规范
```typescript
// 1. iOS原生功能错误边界
const IOSErrorBoundary = ({ children }: { children: React.ReactNode }) => {
  return (
    <ErrorBoundary
      fallback={<IOSErrorFallback />}
      onError={(error, errorInfo) => {
        console.error('🍎 iOS组件错误:', error);
        // 可选：发送到错误监控服务
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

// 2. Capacitor API错误处理
const useCapacitorSafe = () => {
  const callCapacitorAPI = useCallback(async (apiCall: () => Promise<any>) => {
    try {
      if (!Capacitor.isNativePlatform()) {
        console.warn('⚠️ 非原生环境，跳过Capacitor API调用');
        return null;
      }
      
      const result = await apiCall();
      return result;
    } catch (error) {
      console.warn('⚠️ Capacitor API调用失败:', error);
      return null;
    }
  }, []);
  
  return { callCapacitorAPI };
};
```

## 📱 iOS移动端优化

### iOS触摸交互规范
```scss
// iOS触摸目标规范（Apple HIG标准）
.ios-touch-target {
  min-height: var(--ios-touch-target); // 44px
  min-width: var(--ios-touch-target);  // 44px
  padding: var(--space-3);
  
  // iOS原生触摸反馈
  &:active {
    transform: scale(0.98);
    transition: transform 0.1s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  // iOS长按反馈
  &:hover {
    transform: scale(1.02);
  }
}

// iOS Safe Area布局
.ios-safe-layout {
  padding-top: var(--safe-area-inset-top);
  padding-bottom: var(--safe-area-inset-bottom);
  padding-left: var(--safe-area-inset-left);
  padding-right: var(--safe-area-inset-right);
  
  // iPhone X系列刘海屏适配
  @supports (padding: max(0px)) {
    padding-top: max(var(--safe-area-inset-top), 20px);
  }
}
```

### iOS性能优化策略
```typescript
// 1. iOS Intersection Observer优化
const useIOSIntersectionObserver = (threshold = 0.1) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (!window.IntersectionObserver) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => setIsIntersecting(entry.isIntersecting),
      { 
        threshold,
        rootMargin: '20px' // iOS滚动优化
      }
    );
    
    if (ref.current) observer.observe(ref.current);
    
    return () => observer.disconnect();
  }, [threshold]);
  
  return [ref, isIntersecting] as const;
};

// 2. iOS滚动性能优化
const useIOSScrollOptimization = () => {
  useEffect(() => {
    if (Capacitor.getPlatform() === 'ios') {
      // 启用iOS硬件加速滚动
      document.documentElement.style.setProperty(
        '-webkit-overflow-scrolling', 
        'touch'
      );
      
      // 防止iOS橡皮筋效果
      document.body.style.setProperty(
        'overscroll-behavior', 
        'none'
      );
    }
  }, []);
};
```

## 🚨 iOS开发禁忌

### iOS严格禁止的做法
```typescript
// ❌ 禁止：使用不兼容iOS的API
function BadIOSExample() {
  // 错误：iOS Safari不支持某些Web API
  navigator.vibrate(200); // iOS不支持
  screen.orientation.lock('portrait'); // iOS限制
}

// ❌ 禁止：忽略Safe Area适配
.bad-ios-component {
  position: fixed;
  top: 0; // 错误！应该使用 env(safe-area-inset-top)
  width: 100vw;
}

// ❌ 禁止：使用非标准触摸事件
function BadTouchExample() {
  // 错误：应该使用标准的onClick和onTouchStart
  element.addEventListener('mousedown', handler); // iOS可能不响应
}

// ❌ 禁止：忽略iOS性能限制
function BadPerformanceExample() {
  // 错误：iOS设备可能无法承受大量DOM操作
  for (let i = 0; i < 10000; i++) {
    document.body.appendChild(document.createElement('div'));
  }
}

// ❌ 禁止：在Capacitor中使用Web专用API
async function BadCapacitorExample() {
  // 错误：原生环境中localStorage可能受限
  localStorage.setItem('key', JSON.stringify(largeObject));
  
  // 正确：使用Capacitor Preferences
  await Preferences.set({ key: 'key', value: JSON.stringify(largeObject) });
}
```

### iOS样式禁忌
```scss
// ❌ 禁止：忽略iOS特有的CSS属性
.bad-ios-style {
  // 错误：没有考虑iOS WebKit前缀
  user-select: none; // 应该添加 -webkit-user-select: none;
  
  // 错误：没有使用iOS Safe Area
  height: 100vh; // 应该使用 calc(100vh - env(safe-area-inset-bottom))
  
  // 错误：使用过小的触摸目标
  width: 30px;  // 应该至少44px
  height: 30px; // 应该至少44px
}

// ❌ 禁止：在iOS中使用不流畅的动画
.bad-ios-animation {
  // 错误：没有启用硬件加速
  transition: left 0.3s ease;
  
  // 正确：使用transform启用GPU加速
  // transition: transform 0.3s ease;
  // will-change: transform;
}
```

## 💡 iOS开发提示

### iOS代码质量检查清单
- [ ] 所有组件都有完整的TypeScript类型定义
- [ ] 使用CSS变量而非硬编码值
- [ ] 遵循iOS优先的响应式设计
- [ ] 实现Apple Watch风格的数据可视化
- [ ] 使用Capacitor进行iOS原生功能集成
- [ ] 完整的iOS Safe Area适配
- [ ] 最小44px触摸目标（Apple HIG标准）
- [ ] iOS硬件加速动画优化
- [ ] 完善的iOS错误处理和降级方案
- [ ] iOS模拟器和真机测试验证

### iOS持续集成要求
- 所有代码必须通过TypeScript编译（严格模式）
- 所有代码必须通过ESLint检查
- iOS构建必须无错误（Capacitor sync成功）
- 构建产物大小不得超过500KB (gzipped)
- iOS首屏加载时间不得超过3秒
- 必须在iOS模拟器中验证功能完整性
- 推荐在真机设备上进行性能测试

### iOS发布准备清单
- [ ] App Store图标和启动屏设计完成
- [ ] iOS应用描述和截图准备
- [ ] Apple Developer账号和证书配置
- [ ] iOS应用隐私政策和使用条款
- [ ] TestFlight内测版本发布
- [ ] App Store审核准备
- [ ] iOS性能和崩溃监控集成
