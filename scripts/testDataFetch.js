/**
 * 测试数据获取脚本
 * 用于在Node.js环境中测试API数据获取
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 测试用户配置
const TEST_USER_CONFIG = {
  user_id: 1,
  email: '<EMAIL>',
  openid: 'oCU0j7Rg9kzigLzquCBje3KfnQXk'
};

// API配置
const API_BASE_URL = process.env.API_BASE_URL || 'http://124.222.91.101:8000';

/**
 * 发起HTTP请求
 */
async function makeRequest(url, options = {}) {
  // 使用内置的fetch (Node.js 18+)
  const fetchFn = globalThis.fetch || (await import('node-fetch')).default;
  
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    timeout: 10000
  };

  const requestOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers
    }
  };

  console.log(`[请求] ${options.method || 'GET'} ${url}`);
  console.log(`[请求头] ${JSON.stringify(requestOptions.headers, null, 2)}`);

  try {
    const response = await fetchFn(url, requestOptions);
    
    console.log(`[响应] 状态码: ${response.status}`);
    
    const contentType = response.headers.get('content-type');
    let data;
    
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${JSON.stringify(data)}`);
    }

    return data;
  } catch (error) {
    console.error(`[请求失败] ${error.message}`);
    throw error;
  }
}

/**
 * 测试用户认证
 */
async function testAuthentication() {
  console.log('\n=== 测试用户认证 ===');
  
  try {
    // 尝试使用测试用户登录
    const loginData = await makeRequest(`${API_BASE_URL}/api/v1/auth/login/test`, {
      method: 'POST',
      body: JSON.stringify(TEST_USER_CONFIG)
    });

    console.log('[认证成功] 登录响应:', JSON.stringify(loginData, null, 2));
    
    return {
      success: true,
      token: loginData.token || loginData.access_token,
      user: loginData.user
    };

  } catch (error) {
    console.log('[认证失败] 尝试模拟认证');
    
    // 如果正式认证失败，返回模拟token
    return {
      success: false,
      token: 'mock_token_' + Date.now(),
      user: {
        id: TEST_USER_CONFIG.user_id,
        email: TEST_USER_CONFIG.email,
        openid: TEST_USER_CONFIG.openid,
        nickName: '测试用户'
      },
      error: error.message
    };
  }
}

/**
 * 获取动态数据
 */
async function fetchFeedData(token) {
  console.log('\n=== 获取动态数据 ===');
  
  try {
    const feedData = await makeRequest(`${API_BASE_URL}/api/v1/community/posts/`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-User-ID': TEST_USER_CONFIG.user_id.toString(),
        'X-User-OpenID': TEST_USER_CONFIG.openid
      }
    });

    console.log('[数据获取成功] 响应类型:', typeof feedData);
    console.log('[数据获取成功] 是否为数组:', Array.isArray(feedData));
    
    if (Array.isArray(feedData)) {
      console.log('[数据获取成功] 数组长度:', feedData.length);
      if (feedData.length > 0) {
        console.log('[数据获取成功] 第一项字段:', Object.keys(feedData[0]));
      }
    } else if (typeof feedData === 'object' && feedData !== null) {
      console.log('[数据获取成功] 对象字段:', Object.keys(feedData));
    }

    return {
      success: true,
      data: feedData
    };

  } catch (error) {
    console.error('[数据获取失败]', error.message);
    
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 分析数据结构
 */
function analyzeDataStructure(data) {
  console.log('\n=== 数据结构分析 ===');
  
  const analysis = {
    dataType: typeof data,
    isArray: Array.isArray(data),
    structure: {},
    samples: {},
    statistics: {
      totalItems: 0,
      hasWorkoutData: 0,
      hasImages: 0,
      hasComments: 0
    }
  };

  try {
    if (Array.isArray(data)) {
      analysis.statistics.totalItems = data.length;
      
      if (data.length > 0) {
        const firstItem = data[0];
        analysis.structure = getObjectStructure(firstItem);
        analysis.samples.firstItem = firstItem;

        // 统计特殊字段
        data.forEach(item => {
          if (item.related_workout_detail || item.related_workout_id) {
            analysis.statistics.hasWorkoutData++;
          }
          if (item.images && item.images.length > 0) {
            analysis.statistics.hasImages++;
          }
          if (item.comments_count > 0) {
            analysis.statistics.hasComments++;
          }
        });
      }
    } else if (typeof data === 'object' && data !== null) {
      analysis.structure = getObjectStructure(data);
      
      if (data.items && Array.isArray(data.items)) {
        analysis.statistics.totalItems = data.items.length;
        if (data.items.length > 0) {
          analysis.samples.firstItem = data.items[0];
        }
      }
    }

    console.log('[分析结果]', JSON.stringify(analysis, null, 2));
    return analysis;

  } catch (error) {
    console.error('[分析失败]', error.message);
    return { ...analysis, error: error.message };
  }
}

/**
 * 获取对象结构
 */
function getObjectStructure(obj, maxDepth = 2, currentDepth = 0) {
  if (currentDepth >= maxDepth || obj === null || typeof obj !== 'object') {
    return typeof obj;
  }

  if (Array.isArray(obj)) {
    return {
      type: 'array',
      length: obj.length,
      itemType: obj.length > 0 ? getObjectStructure(obj[0], maxDepth, currentDepth + 1) : 'unknown'
    };
  }

  const structure = {};
  Object.keys(obj).forEach(key => {
    structure[key] = getObjectStructure(obj[key], maxDepth, currentDepth + 1);
  });

  return structure;
}

/**
 * 保存数据到文件
 */
function saveDataToFile(data, filename) {
  try {
    const outputDir = path.join(__dirname, '..', 'memory-bank', 'api');
    
    // 确保目录存在
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    const filePath = path.join(outputDir, filename);
    const jsonString = JSON.stringify(data, null, 2);
    
    fs.writeFileSync(filePath, jsonString, 'utf8');
    console.log(`[文件保存] 数据已保存到: ${filePath}`);
    
    return filePath;
  } catch (error) {
    console.error(`[文件保存失败] ${error.message}`);
    return null;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🧪 开始测试FeedPage数据获取');
  console.log('API地址:', API_BASE_URL);
  console.log('测试用户:', TEST_USER_CONFIG);

  try {
    // 第一步：测试认证
    const authResult = await testAuthentication();
    
    if (!authResult.success) {
      console.log('⚠️ 认证失败，但继续使用模拟token测试');
    }

    // 第二步：获取动态数据
    const feedResult = await fetchFeedData(authResult.token);
    
    if (!feedResult.success) {
      console.error('❌ 数据获取失败:', feedResult.error);
      return;
    }

    // 第三步：分析数据结构
    const analysis = analyzeDataStructure(feedResult.data);

    // 第四步：保存数据样例
    const savedFile = saveDataToFile({
      timestamp: new Date().toISOString(),
      authResult,
      feedData: feedResult.data,
      analysis
    }, 'real-feed-data-sample.json');

    // 生成报告
    const report = `# 动态数据获取测试报告

## 测试时间
${new Date().toISOString()}

## 认证结果
- 状态: ${authResult.success ? '✅ 成功' : '❌ 失败'}
- Token: ${authResult.token ? '已获取' : '未获取'}
- 用户: ${JSON.stringify(authResult.user, null, 2)}
${authResult.error ? `- 错误: ${authResult.error}` : ''}

## 数据获取结果
- 状态: ${feedResult.success ? '✅ 成功' : '❌ 失败'}
- 数据类型: ${analysis.dataType}
- 是否为数组: ${analysis.isArray}
- 项目总数: ${analysis.statistics.totalItems}

## 数据统计
- 包含训练数据: ${analysis.statistics.hasWorkoutData}
- 包含图片: ${analysis.statistics.hasImages}
- 包含评论: ${analysis.statistics.hasComments}

## 数据结构
\`\`\`json
${JSON.stringify(analysis.structure, null, 2)}
\`\`\`

## 样例数据
\`\`\`json
${JSON.stringify(analysis.samples.firstItem, null, 2)}
\`\`\`

---
数据文件: ${savedFile}
`;

    // 保存报告
    const reportFile = saveDataToFile(report, 'data-fetch-report.md');
    
    console.log('\n✅ 测试完成!');
    console.log(`📊 数据文件: ${savedFile}`);
    console.log(`📋 报告文件: ${reportFile}`);

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export {
  testAuthentication,
  fetchFeedData,
  analyzeDataStructure
};
