#!/usr/bin/env node

/**
 * SSL/TLS Server Verification Script
 * 验证服务器的SSL/TLS配置状态
 */

import https from 'https';
import http from 'http';

const SERVER_HOST = '**************';
const SERVER_PORT = 8000;
const TEST_ENDPOINT = '/api/v1/community/posts/?skip=0&limit=1';

console.log('🔍 开始验证服务器SSL/TLS配置...\n');

// 测试HTTP连接
function testHTTP() {
  return new Promise((resolve, reject) => {
    const url = `http://${SERVER_HOST}:${SERVER_PORT}${TEST_ENDPOINT}`;
    console.log(`📡 测试HTTP连接: ${url}`);
    
    const req = http.get(url, (res) => {
      console.log(`✅ HTTP连接成功: ${res.statusCode} ${res.statusMessage}`);
      console.log(`📋 响应头:`, res.headers);
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          success: true,
          statusCode: res.statusCode,
          headers: res.headers,
          dataLength: data.length
        });
      });
    });
    
    req.on('error', (error) => {
      console.log(`❌ HTTP连接失败: ${error.message}`);
      resolve({
        success: false,
        error: error.message
      });
    });
    
    req.setTimeout(10000, () => {
      console.log('⏰ HTTP请求超时');
      req.destroy();
      resolve({
        success: false,
        error: 'Request timeout'
      });
    });
  });
}

// 测试HTTPS连接
function testHTTPS() {
  return new Promise((resolve, reject) => {
    const url = `https://${SERVER_HOST}:${SERVER_PORT}${TEST_ENDPOINT}`;
    console.log(`🔒 测试HTTPS连接: ${url}`);
    
    const options = {
      hostname: SERVER_HOST,
      port: SERVER_PORT,
      path: TEST_ENDPOINT,
      method: 'GET',
      rejectUnauthorized: false // 允许自签名证书
    };
    
    const req = https.get(options, (res) => {
      console.log(`✅ HTTPS连接成功: ${res.statusCode} ${res.statusMessage}`);
      console.log(`🔐 SSL信息:`, {
        authorized: res.socket.authorized,
        authorizationError: res.socket.authorizationError,
        cipher: res.socket.getCipher(),
        protocol: res.socket.getProtocol()
      });
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          success: true,
          statusCode: res.statusCode,
          ssl: {
            authorized: res.socket.authorized,
            authorizationError: res.socket.authorizationError,
            cipher: res.socket.getCipher(),
            protocol: res.socket.getProtocol()
          },
          dataLength: data.length
        });
      });
    });
    
    req.on('error', (error) => {
      console.log(`❌ HTTPS连接失败: ${error.message}`);
      
      // 分析具体的SSL错误
      if (error.code === 'ECONNREFUSED') {
        console.log('💡 分析: 服务器拒绝连接，可能未启用SSL');
      } else if (error.code === 'EPROTO') {
        console.log('💡 分析: 协议错误，可能服务器不支持SSL');
      } else if (error.message.includes('wrong version number')) {
        console.log('💡 分析: SSL版本不匹配，服务器可能只支持HTTP');
      }
      
      resolve({
        success: false,
        error: error.message,
        code: error.code
      });
    });
    
    req.setTimeout(10000, () => {
      console.log('⏰ HTTPS请求超时');
      req.destroy();
      resolve({
        success: false,
        error: 'Request timeout'
      });
    });
  });
}

// 主函数
async function main() {
  console.log(`🎯 目标服务器: ${SERVER_HOST}:${SERVER_PORT}`);
  console.log(`📍 测试端点: ${TEST_ENDPOINT}\n`);
  
  // 测试HTTP
  const httpResult = await testHTTP();
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 测试HTTPS
  const httpsResult = await testHTTPS();
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 总结报告
  console.log('📊 测试结果总结:');
  console.log(`HTTP支持: ${httpResult.success ? '✅ 是' : '❌ 否'}`);
  console.log(`HTTPS支持: ${httpsResult.success ? '✅ 是' : '❌ 否'}`);
  
  if (httpResult.success && !httpsResult.success) {
    console.log('\n🎯 建议: 服务器仅支持HTTP，客户端应使用HTTP连接');
    console.log('🔧 修复方案: 确保所有客户端配置使用HTTP协议');
  } else if (!httpResult.success && httpsResult.success) {
    console.log('\n🎯 建议: 服务器仅支持HTTPS，客户端应使用HTTPS连接');
    console.log('🔧 修复方案: 更新客户端配置使用HTTPS协议');
  } else if (httpResult.success && httpsResult.success) {
    console.log('\n🎯 建议: 服务器同时支持HTTP和HTTPS');
    console.log('🔧 修复方案: 选择一种协议并统一配置');
  } else {
    console.log('\n❌ 警告: 服务器HTTP和HTTPS都无法连接');
    console.log('🔧 修复方案: 检查服务器状态和网络连接');
  }
}

// 运行脚本
main().catch(console.error);
